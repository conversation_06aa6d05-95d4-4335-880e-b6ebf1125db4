import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { InspectionTypeDto, InspectionTypeServiceProxy } from '@proxies/inspection-type.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-inspection-type-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class InspectionTypeCreateEditComponent extends ViewComponent implements OnInit {

    private inspectionTypeServiceProxy: InspectionTypeServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: InspectionTypeDto = new InspectionTypeDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get inspectionTypeName(): AbstractControl {
        return this.modalForm.controls['inspectionTypeNameInput'];
    };

    get inspectionTypeEnabled(): AbstractControl {
        return this.modalForm.controls['inspectionTypeEnabledSelect'];
    };

    get inspectionTypeCode(): AbstractControl {
        return this.modalForm.controls['inspectionTypeCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.inspectionTypeServiceProxy = _injector.get(InspectionTypeServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            inspectionTypeNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            inspectionTypeEnabledSelect: ['false', [Validators.required]],
            inspectionTypeCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.inspectionTypeServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.inspectionTypeName.setValue(this.item.name);
                        this.inspectionTypeEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.inspectionTypeCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new InspectionTypeDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.inspectionTypeName.value;
        this.item.enabled = this.inspectionTypeEnabled.value == 'true';
        this.item.code = this.inspectionTypeCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre del tipo de inspección es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.inspectionTypeServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de inspección actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.inspectionTypeServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de inspección creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}