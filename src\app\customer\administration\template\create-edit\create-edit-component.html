<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal title="Editar configuración" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="TemplateName" class="form-label"
                                [ngClass]="{'ng-invalid' : (name.invalid && (name.touched || name.dirty))}">
                                Descripción
                            </label>

                            <textarea type="text" class="form-control" id="TemplateName" name="TemplateName"
                                formControlName="nameInput" rows="4"></textarea>
                        </div>
                    </div>
                </div>
 
                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="TemplateKey" class="form-label" [ngClass]="{'ng-invalid' : (key.invalid && (key.touched || key.dirty))}">
                                Llave
                            </label>

                            <input type="text" class="form-control" id="TemplateKey" name="TemplateKey"
                                formControlName="keyInput">

                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="TemplateValue" class="form-label" [ngClass]="{'ng-invalid' : (value.invalid && (value.touched || value.dirty))}">
                                Valor (*)
                            </label>

                            <textarea type="text" class="form-control" id="TemplateValue" name="TemplateValue"
                                formControlName="valueInput" maxlength="25000" rows="4"></textarea>

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El valor de la propiedad es obligatoria.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>