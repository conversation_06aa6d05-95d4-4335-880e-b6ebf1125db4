
import { Component, Injector, Input, OnInit } from "@angular/core";
import { AbstractControl, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { AppFileUploadProgressComponent } from "@components/file-upload-progress/file-upload-progress.component";
import { ViewComponent } from "@core/inheritance/app-component.base";
import { IUploadProgressRespose } from "@core/models/app-config";
import { UploadResource } from "@core/utils/core.request";
import { MeetCompleteDto, MeetDto, MeetServiceProxy, MeetStatus } from "@proxies/meet.proxy";
import { DateTime } from "luxon";
import { finalize } from "rxjs";

@Component({
    selector: 'app-meet-complete',
    templateUrl: 'complete.component.html',
    styleUrls: [
        'complete.component.scss'
    ]
})
export class MeetCompleteComponent extends ViewComponent implements OnInit {

    private meetServiceProxy: MeetServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;

    item!: MeetDto;
    loaded!: boolean;
    disabled!: boolean;
    modalForm!: FormGroup;

    meetTime!: Date;
    meetStartTime!: Date;
    meetEndTime!: Date;

    uploadResources: UploadResource[] = [];
    size: number = 15_728_640;

    get meetDescription(): AbstractControl {
        return this.modalForm.controls['meetDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.meetServiceProxy = _injector.get(MeetServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            meetDescriptionInput: ['', Validators.compose([Validators.required, Validators.maxLength(25000)])],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.meetServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.meetTime = response.startTime.toJSDate();
                    this.meetStartTime = response.startTime.toJSDate();
                    this.meetEndTime = response.endTime.toJSDate();

                    if (this.item.status == MeetStatus.Canceled) {
                        this.message.info('No se puede realizar la operación debido a que la capacitación ha sido cancelada.', 'Aviso');
                        this.dialog.dismiss();
                        return;
                    }

                    if (this.item.status == MeetStatus.Executed) {
                        this.message.info('No se puede realizar la operación debido a que la charla ha sido ejecutada.', 'Aviso');
                        this.dialog.dismiss();
                        return;
                    }

                },
                error: async () => await this.dialog.dismiss()
            });
    }

    onUploadItem(event: UploadResource): void {
        this.uploadResources ??= [];
        this.uploadResources.push(event);
    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.uploadResources ??= [];
                this.uploadResources.splice(index, 1);
            }
        });
    }

    save(): void {
        if (!this.uploadResources || this.uploadResources.length == 0) {
            this.message.info('El documento de finalización es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;

        this.uploads(async () => {
            const loading = await this.loader.show();

            const input: MeetCompleteDto = new MeetCompleteDto();

            input.id = this.item.id;
            input.description = this.meetDescription.value;
            input.uploadResources = this.uploadResources;

            this.meetServiceProxy
                .complete(input)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Charla finalizada satisfactoriamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        });

    }

    private uploads(callback: () => void) {
        this.dialog.show({
            component: AppFileUploadProgressComponent,
            componentProps: {
                files: this.uploadResources.map(p => p.file),
                processed: (data: IUploadProgressRespose) => {
                    if (data.completed) {
                        let index: number = 0;

                        for (let token of data.tokens) {
                            this.uploadResources[index].token = token;
                            index++;
                        }

                        callback();
                    } else {
                        this.disabled = false;
                    }
                }
            }
        });
    }
}
