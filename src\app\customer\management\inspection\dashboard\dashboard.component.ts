import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { InspectionDto, InspectionMode, InspectionServiceProxy } from '@proxies/inspection.proxy';
import { IntegrationServiceProxy } from '@proxies/integration.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Subscription, finalize } from 'rxjs';
import { InspectionCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';
import { InspectionTypeServiceProxy } from '@proxies/inspection-type.proxy';
import { IPopoverAction } from '@core/models/popover-action';
import { DateTime } from 'luxon';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { InspectionChangeInspectionTimeComponent } from '../change-inspection-time/change-inspection-time.component';
import { InspectionRejectComponent } from '../reject-inspection/reject-inspection.component';
import { InspectionCancelComponent } from '../cancel-inspection/cancel-inspection.component';

@Component({
    selector: 'app-inspection-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class InspectionDashboardComponent extends ViewComponent implements OnInit {

    private inspectionServiceProxy: InspectionServiceProxy;
    private inspectionTypeServiceProxy: InspectionTypeServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    centers!: IFilterOption<string>[];
    inspectionTypes!: IFilterOption<number>[];
    inspectionModeTypes: IFilterOption<InspectionMode>[] = [
        { label: 'Programada', value: InspectionMode.Programed },
        { label: 'Reprogramada', value: InspectionMode.Modified },
        { label: 'Reprogramada sin fecha', value: InspectionMode.ModifiedWithoutDate },
        { label: 'En proceso', value: InspectionMode.Process },
        { label: 'Pendiente de revisión', value: InspectionMode.Executed },
        { label: 'Cancelada', value: InspectionMode.Canceled },
        { label: 'Cancelada por contrato', value: InspectionMode.Contract },
        { label: 'Rechazada', value: InspectionMode.Rejected },
        { label: 'Finalizada', value: InspectionMode.Completed },
    ];
    hasRiskInspection: IFilterOption<boolean>[] = [
        { label: 'Sí', value: true },  
        { label: 'No', value: false } 
    ];
    inspectionModes = {
        none: InspectionMode.None,
        programed: InspectionMode.Programed,
        modifiedWithoutDate: InspectionMode.ModifiedWithoutDate,
        modified: InspectionMode.Modified,
        process: InspectionMode.Process,
        executed: InspectionMode.Executed,
        canceled: InspectionMode.Canceled,
        contract: InspectionMode.Contract,
        rejected: InspectionMode.Rejected,
        completed: InspectionMode.Completed
    };

    private subscription: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.inspectionServiceProxy = _injector.get(InspectionServiceProxy);
        this.inspectionTypeServiceProxy = _injector.get(InspectionTypeServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    ngOnInit(): void {

        this.integrationServiceProxy
            .getAllLogiticCenters()
            .subscribe({
                next: (response) => {
                    this.centers = response.items.map(p => {
                        return {
                            label: p.name,
                            value: p.code
                        };
                    });
                }
            });

        this.inspectionTypeServiceProxy
            .getAll(undefined, true, undefined, "Name DESC", 1000, 0)
            .subscribe({
                next: (response) => {
                    this.inspectionTypes = response.items.map(p => {
                        return {
                            label: p.name,
                            value: p.id
                        };
                    });
                }
            });

        this.getData();
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: InspectionCreateEditComponent
        }).then((response) => {
            if (response.data.result)
                this.getData();
        });
    }

    editItem(item: InspectionDto): void {
        this.dialog.showWithData<boolean>({
            component: InspectionCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    changeInspectionTime(item: InspectionDto): void {
        this.dialog.showWithData<boolean>({
            component: InspectionChangeInspectionTimeComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    rejectInspection(item: InspectionDto): void {
        this.dialog.showWithData<boolean>({
            component: InspectionRejectComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    completeInspection(item: InspectionDto): void {
        this.message.confirm(`¿Estas seguro de finalizar la inspección "${item.code}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.inspectionServiceProxy
                    .complete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha finalizado la inspección satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    cancelInspection(item: InspectionDto): void {
        this.dialog.showWithData<boolean>({
            component: InspectionCancelComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    async exportInspectionCertificate(item: InspectionDto): Promise<void> {
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .exportCertification(item.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    importInspections(): void {
        this.navigation.forward('/customer/management/inspections/import');
    }

    deleteItem(item: InspectionDto): void {
        this.message.confirm(`¿Estas seguro de eliminar la inspección "${item.code}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.inspectionServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el registro satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.inspectionServiceProxy
            .getAll(
                this.dataTable?.filters?.['code']?.['value'],
                (<IFilterOption<number>[]>this.dataTable?.filters?.['inspectionTypeIds']?.['value'])?.map(p => p.value) || [],
                (<IFilterOption<InspectionMode>[]>this.dataTable?.filters?.['inspectionModes']?.['value'])?.map(p => p.value) || [],
                this.dataTable?.filters?.['customer']?.['value'],
                this.dataTable?.filters?.['customerName']?.['value'],
                this.dataTable?.filters?.['property']?.['value'],
                this.dataTable?.filters?.['propertyName']?.['value'],
                DateTime.fromJSDate(this.dataTable?.filters?.['inspectionStartTime']?.['value']),
                DateTime.fromJSDate(this.dataTable?.filters?.['inspectionEndTime']?.['value']),
                (<IFilterOption<string>[]>this.dataTable?.filters?.['centerIds']?.['value'])?.map(p => p.value) || [],
                this.dataTable?.filters?.['hasRiskInspection']?.['value'] ?? null,
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: InspectionDto): void {

        let actions: IPopoverAction[] = [
            {
                label: item.mode == InspectionMode.Programed || item.mode == InspectionMode.Modified || item.mode == InspectionMode.ModifiedWithoutDate ? 'Editar' : 'Visualizar',
                permissions: [
                    'Pages.Management.Inspection.Modify'
                ],
                callback: () => this.editItem(item)
            }
        ];

        if (item.mode != InspectionMode.Executed && item.mode != InspectionMode.Contract && item.mode != InspectionMode.Completed && item.mode != InspectionMode.Canceled && item.mode != InspectionMode.Rejected) {

            actions.push({
                label: 'Reprogramar',
                permissions: [
                    'Pages.Management.Inspection.Modify'
                ],
                callback: () => this.changeInspectionTime(item)
            });
        }

        if (item.mode == InspectionMode.Executed) {
            actions.push({
                label: 'Rechazar',
                permissions: [
                    'Pages.Management.Inspection.Modify'
                ],
                callback: () => this.rejectInspection(item)
            });

            actions.push({
                label: 'Finalizar',
                permissions: [
                    'Pages.Management.Inspection.Modify'
                ],
                callback: () => this.completeInspection(item)
            });
        }

        if (item.mode != InspectionMode.Completed && item.mode != InspectionMode.Canceled && item.mode != InspectionMode.Contract) {
            actions.push({
                label: 'Cancelar',
                permissions: [
                    'Pages.Management.Inspection.Modify'
                ],
                callback: () => this.cancelInspection(item)
            });
        }

        if (item.mode == InspectionMode.Completed) {
            actions.push({
                label: 'Exportar informe',
                permissions: [],
                callback: () => this.exportInspectionCertificate(item)
            });
        }

        actions.push({
            label: 'Eliminar',
            permissions: [
                'Pages.Management.Inspection.Delete'
            ],
            callback: () => this.deleteItem(item)
        });

        this.popover.show(event, actions);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .export(
                this.dataTable?.filters?.['code']?.['value'],
                (<IFilterOption<number>[]>this.dataTable?.filters?.['inspectionTypeIds']?.['value'])?.map(p => p.value) || [],
                (<IFilterOption<InspectionMode>[]>this.dataTable?.filters?.['inspectionModes']?.['value'])?.map(p => p.value) || [],
                this.dataTable?.filters?.['customer']?.['value'],
                this.dataTable?.filters?.['customerName']?.['value'],
                this.dataTable?.filters?.['property']?.['value'],
                this.dataTable?.filters?.['propertyName']?.['value'],
                DateTime.fromJSDate(this.dataTable?.filters?.['inspectionStartTime']?.['value']),
                DateTime.fromJSDate(this.dataTable?.filters?.['inspectionEndTime']?.['value']),
                (<IFilterOption<string>[]>this.dataTable?.filters?.['centerIds']?.['value'])?.map(p => p.value) || [],
                this.dataTable?.filters?.['hasRiskInspection']?.['value'] ?? null,
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    async exportProperties(): Promise<void> {
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .exportProperties()
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters(): void {
        this.dataTable?.clear();
        this.getData();
    }
}