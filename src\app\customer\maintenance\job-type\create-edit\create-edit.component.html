<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar tipo de trabajo' : 'Crear tipo de trabajo'"
        [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="JobTypeName" class="form-label"
                                [ngClass]="{'ng-invalid' : (jobTypeName.invalid && (jobTypeName.touched || jobTypeName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="JobTypeName" name="JobTypeName"
                                formControlName="jobTypeNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="JobTypeEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="JobTypeEnabled" name="JobTypeEnabled"
                                formControlName="jobTypeEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="JobTypeShowExtra">
                                Solicitar descripción (*)
                            </label>
                            <select class="form-control" id="JobTypeShowExtra" name="JobTypeShowExtra"
                                formControlName="jobTypeShowExtraSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="JobTypeCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="JobTypeCode" name="JobTypeCode"
                                formControlName="jobTypeCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>