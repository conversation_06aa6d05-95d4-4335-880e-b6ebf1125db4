import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { AlarmAccountRoutingModule } from './alarm-account.routing.module';
import { AlarmAccountDashboardComponent } from './dashboard/dashboard.component';
import { AlarmAccountCreateEditComponent } from './create-edit/create-edit.component';
import { AlarmPartitionInformationComponent } from './create-edit/partition-information/partition-information.component';
import { AlarmPartitionCreateEditComponent } from './create-edit/partition-information/create-edit/create-edit.component';
import { AlarmZoneInformationComponent } from './create-edit/zone-information/zone-information.component';
import { AlarmZoneCreateEditComponent } from './create-edit/zone-information/create-edit/create-edit.component';
import { AlarmUserInformationComponent } from './create-edit/user-information/user-information.component';
import { AlarmUserCreateEditComponent } from './create-edit/user-information/create-edit/create-edit.component';

@NgModule({
  imports: [
    AlarmAccountRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    AlarmAccountDashboardComponent,
    AlarmAccountCreateEditComponent,
    AlarmPartitionInformationComponent,
    AlarmPartitionCreateEditComponent,
    AlarmZoneInformationComponent,
    AlarmZoneCreateEditComponent,
    AlarmUserInformationComponent,
    AlarmUserCreateEditComponent
  ]
})
export class AlarmAccountModule { }