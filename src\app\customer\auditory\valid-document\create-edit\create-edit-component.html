<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? ( onlyview ? 'Visualizar' : 'Nueva Version') : 'Crear documento'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="DocumentTypeSearch"
                                        [ngClass]="{'ng-invalid' : (documentType.invalid && (documentType.touched || documentType.dirty))}">
                                        Tipo de documento (*)
                                    </label>

                                    <select class="form-control" (change)="documentTypeChange($event)" id="DocumentTypeSearch" name="DocumentTypeSearch"
                                        formControlName="documentTypeSelect">
                                        <option [value]="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let documentType of types" [value]="documentType.id">
                                            {{documentType.code  + ' - ' + documentType.name }}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El tipo de documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" *ngIf="viewDocumentRel">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="DocumentRel">
                                        Documento Relacionado
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindDocument()" type="text" class="form-control rounded"
                                            id="DocumentRel" name="DocumentRel" formControlName="documentRelInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentCode" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentCode.invalid && (documentCode.touched || documentCode.dirty))}">
                                        Código del Documento
                                    </label>

                                    <input type="text" class="form-control" id="DocumentCode" name="DocumentCode"
                                        formControlName="documentCodeInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El código del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentVersion" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentVersion.invalid && (documentVersion.touched || documentVersion.dirty))}">
                                        Versión
                                    </label>

                                    <input type="text" class="form-control" id="DocumentVersion" name="DocumentVersion"
                                        formControlName="documentVersionInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            La versión del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentCreatedBy" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentCreatedBy.invalid && (documentCreatedBy.touched || documentCreatedBy.dirty))}">
                                        Elaborado Por
                                    </label>

                                    <input type="text" ng-disabled="locked" class="form-control" id="DocumentCreatedBy"
                                        name="DocumentCreatedBy" formControlName="documentCreatedByInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El creador del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="DocumentStatusSearch"
                                        [ngClass]="{'ng-invalid' : (documentStatus.invalid && (documentStatus.touched || documentStatus.dirty))}">
                                        Estado de documento (*)
                                    </label>

                                    <select class="form-control" id="DocumentStatusSearch" name="DocumentStatusSearch"
                                        formControlName="documentStatusSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let documentStatus of statuses" [value]="documentStatus.id">
                                            {{documentStatus.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El estado del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentName.invalid && (documentName.touched || documentName.dirty))}">
                                        Nombre (*)
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control" id="DocumentName" name="DocumentName"
                                        formControlName="documentNameInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentManagement" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentManagement.invalid && (documentManagement.touched || documentManagement.dirty))}">
                                        Gerencia (*)
                                    </label>

                                    <select class="form-control" id="DocumentManagement" name="DocumentManagement"
                                        formControlName="documentManagementSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let management of managements" [value]="management.code">
                                            {{management.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            Gerencia es obligatoria.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentArea" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentArea.invalid && (documentArea.touched || documentArea.dirty))}">
                                        Área(*)
                                    </label>
                                    <select class="form-control" id="DocumentArea" name="DocumentArea"
                                        formControlName="documentAreaSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let area of areasSelect" [value]="area.code">
                                            {{area.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El área del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" *ngIf="viewDocumentRelWithId">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentLocationType" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentLocationType.invalid && (documentLocationType.touched || documentLocationType.dirty))}">
                                        Tipo de Ubicación (*)
                                    </label>

                                    <select class="form-control" id="DocumentLocationType" name="DocumentLocationType"
                                        formControlName="documentLocationTypeSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let locationType of locationTypes" [value]="locationType">
                                            {{locationType}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentLocation" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentLocation.invalid && (documentLocation.touched || documentLocation.dirty))}">
                                        Ubicación
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control" id="DocumentLocation" name="DocumentLocation"
                                        formControlName="documentLocationInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" *ngIf="viewDocumentRelWithId">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentProvision" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentProvision.invalid && (documentProvision.touched || documentProvision.dirty))}">
                                        Disposición Final (*)
                                    </label>

                                    <select class="form-control" id="DocumentProvision" name="DocumentProvision"
                                        formControlName="documentProvisionSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let provision of provisionTypes" [value]="provision">
                                            {{provision}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentRetentionTime" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentRetentionTime.invalid && (documentRetentionTime.touched || documentRetentionTime.dirty))}">
                                        Tiempo de Retencion (Años)
                                    </label>

                                    <input type="number" style="text-transform: uppercase;" class="form-control" id="DocumentRetentionTime" name="DocumentRetentionTime"
                                        formControlName="documentRetentionTimeInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="row">
                                <div class="col-12">
                                    <div class="ion-input mb-4">
                                        <label for="DocumentTag" class="form-label"
                                            [ngClass]="{'ng-invalid' : (documentTag.invalid && (documentTag.touched || documentTag.dirty))}">
                                            Etiquetas (*)
                                        </label>
                                        <tag-input [disable]="getDisableChips()" name="DocumentTag"
                                            secondaryPlaceholder="+" placeholder="+"
                                            [animationDuration]="{enter: '0ms', leave: '0ms'}"
                                            (onRemove)="removeTag($event)" formControlName="documentTagSelect"
                                            identifyBy="tagid" (click)="showFindTag()" [addOnBlur]="false"
                                            displayBy="name" [removable]="true">
                                        </tag-input>
                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Las etiquetas del documento son obligatorias.
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="ion-input mb-4">
                                        <label for="DocumentModifier" class="form-label"
                                            [ngClass]="{'ng-invalid' : (documentModifier.invalid && (documentModifier.touched || documentModifier.dirty))}">
                                            Modificadores (*)    
                                            <a style="color:deepskyblue" [hidden]="!getShowSaveButton()" (click)="clearModifier()">Limpiar</a>
                                        </label>
                                        <tag-input [disable]="!getShowSaveButton()" name="DocumentModifier"
                                            [animationDuration]="{enter: '0ms', leave: '0ms'}"
                                            (onRemove)="removeUser($event)" secondaryPlaceholder="+" placeholder="+"
                                            (click)="showFindUser()" formControlName="documentModifierSelect"
                                            identifyBy="email" displayBy="email" [addOnBlur]="false" [removable]="true">
                                        </tag-input>
                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Los modificadores del documento son obligatorios.
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="ion-input mb-4">
                                        <label for="DocumentNotification" class="form-label"
                                            [ngClass]="{'ng-invalid' : (documentNotification.invalid && (documentNotification.touched || documentNotification.dirty))}">
                                            Notificar a (*)
                                            <a style="color:deepskyblue" [hidden]="!getShowSaveButton()" (click)="clearNotification()">Limpiar</a>
                                        </label>
                                        <tag-input [disable]="!getShowSaveButton()" name="DocumentNotification"
                                            [animationDuration]="{enter: '0ms', leave: '0ms'}"
                                            (onRemove)="removeNotification($event)" secondaryPlaceholder="+"
                                            placeholder="+" (click)="showFindEmployee()"
                                            formControlName="documentNotificationSelect" identifyBy="email"
                                            displayBy="email" [addOnBlur]="false" [removable]="true">
                                        </tag-input>
                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Los notificados del documento son obligatorios.
                                            </ion-col>
                                        </ion-row>
                                        <!-- 
                                        <app-tag-input [formGroup]="modalForm" controlName="documentNotificationSelect" [tags]="availableNotification"
                                            label="Notificar a (*)" placeholder="Seleccione..."></app-tag-input> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="row">
                                <div class="col-12">
                                    <div class="ion-input mb-4">
                                        <div class="input-control">
                                            <label for="DocumentMacroProcess" class="form-label"
                                                [ngClass]="{'ng-invalid' : (documentMacroProcess.invalid && (documentMacroProcess.touched || documentMacroProcess.dirty))}">
                                                Macro Proceso
                                            </label>
                                            <select class="form-control" style="z-index: 1;" id="DocumentMacroProcess"
                                                name="DocumentMacroProcess"
                                                formControlName="documentMacroProcessSelect">
                                                <option value="-1">
                                                    Seleccione
                                                </option>
                                                <option *ngFor="let macroprocess of macroProcesses"
                                                    [value]="macroprocess.id">
                                                    {{macroprocess.name}}
                                                </option>
                                            </select>

                                            <ion-row class="input-validation">
                                                <ion-col size="12">
                                                    Seleccione un Macro Proceso
                                                </ion-col>
                                            </ion-row>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="ion-input mb-4">
                                        <div class="input-control">
                                            <label for="DocumentProcess" class="form-label"
                                                [ngClass]="{'ng-invalid' : (documentProcess.invalid && (documentProcess.touched || documentProcess.dirty))}">
                                                Proceso
                                            </label>
                                            <select class="form-control" id="DocumentProcess" name="DocumentProcess"
                                                formControlName="documentProcessSelect">
                                                <option value="-1">
                                                    Seleccione
                                                </option>
                                                <option *ngFor="let process of processSelect" [value]="process.id">
                                                    {{process.name}}
                                                </option>
                                            </select>

                                            <ion-row class="input-validation">
                                                <ion-col size="12">
                                                    Seleccione un Proceso
                                                </ion-col>
                                            </ion-row>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="ion-input mb-4">
                                        <div class="input-control">
                                            <label for="DocumentSubProcess" class="form-label"
                                                [ngClass]="{'ng-invalid' : (documentSubProcess.invalid && (documentSubProcess.touched || documentSubProcess.dirty))}">
                                                SubProceso
                                            </label>
                                            <select class="form-control" id="DocumentSubProcess"
                                                name="DocumentSubProcess" formControlName="documentSubProcessSelect">
                                                <option value="-1">
                                                    Seleccione
                                                </option>
                                                <option *ngFor="let process of subProcessSelect" [value]="process.id">
                                                    {{process.name}}
                                                </option>
                                            </select>

                                            <ion-row class="input-validation">
                                                <ion-col size="12">
                                                    Seleccione un SubProceso
                                                </ion-col>
                                            </ion-row>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="Documentos Anexos">
                    <div *ngIf="id" class="row">
                        <div class="col-12 mb-3">
                            <label class="fz-normal fw-bold text-dark mb-0">Archivo: {{ item?.filePath }}</label>
                        </div>
                    </div>
                    <div *ngIf="id" class="row">
                        <div class="col-12 mb-3">
                            <button class="btn btn-danger" (click)="loadResource()">Visualizar documento</button>
                            <button [hidden]="!showDownload()" class="btn btn-danger" (click)="downloadResource()">Descargar documento</button>
                        </div>
                    </div>
                    <div *ngIf="!onlyview" class="row">

                        <div class="col-12 mb-3">
                            <app-file-uploader [size]="size" [files]="true" [documents]="true"
                                (onUploadItem)="onUploadItem($event)">
                            </app-file-uploader>
                        </div>

                        <div class="col-12">
                            <div class="alert alert-warning" role="alert">
                                Ud. puede seleccionar un archivo WORD (docx), Excel (.xlsx), Power Point (.pptx), con un tamaño máximo de
                                15MB, Para mantener la visualización deseada el archivo debe tener un área de impresión establecida, El archivo sobreescribirá uno ya existente.
                            </div>
                        </div>

                        <div *ngIf="uploadResources.length > 0" class="col-12 mb-3">
                            <label class="fz-normal fw-bold text-dark mb-0">
                                Recursos pendientes de publicación
                            </label>
                            <hr>
                        </div>

                        <div *ngFor="let resource of uploadResources; index as i;" class="col-12">
                            <app-file-preview [resource]="resource" [type]="resource.type"
                                (onRemove)="onRemoveUploadResource(i)">
                            </app-file-preview>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel *ngIf="id && onlyview && item.compareResult" header="Comparación">
                    <div class="row">
                        <table style="white-space: pre-wrap;">
                            <tr class="compare-table-cell">
                                <th>Version</th>
                                <th>Fecha</th>
                                <th>Descripción del cambio</th>
                            </tr>
                            <tr class=compare-table-cell *ngFor="let comparison of comparisonArray">
                                <td>{{ comparison.version }}</td> 
                                <td>{{ comparison.date | luxonFormat: 'dd/MM/yyyy' }}</td> 
                                <td>{{ comparison.description }}</td> 
                            </tr>
                        </table>
                    </div>
                </p-tabPanel>
                <p-tabPanel *ngIf="id && onlyview" header="Historial">
                    <div class="row">
                        <div class="col-12">
                            <p-table #dataTable (onLazyLoad)="getData($event)"
                                [value]="table.records" [rows]="table.defaultRecordsCountPerPage" [paginator]="false"
                                [lazy]="true" [scrollable]="true" ScrollWidth="100%" scrollDirection="horizontal"
                                [resizableColumns]="table.resizableColumns">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 100px">
                                            Estado
                                        </th>
                                        <th style="min-width: 100px">
                                            Modificado Por
                                        </th>
                                        <th style="min-width: 100px">
                                            Comentario
                                        </th>
                                        <th style="min-width: 100px">
                                            Fecha
                                        </th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                                    <tr>
                                        <td>
                                            <span class="p-column-title">
                                                Status
                                            </span> {{record?.status}}
                                        </td>
                                        <td>
                                            <span class="p-column-title">
                                                User
                                            </span> {{record?.user}}
                                        </td>
                                        <td>
                                            <span class="p-column-title">
                                                Status
                                            </span> {{record?.comment}}
                                        </td>
                                        <td style="min-width: 200px">
                                            <span class="p-column-title">
                                                Created At
                                            </span> {{record?.createdat  | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                            <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                                No hay data
                            </div>
                            <div class="primeng-paging-container">
                                <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                                    (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                                    [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                                    [showCurrentPageReport]="true" dropdownAppendTo="body"
                                    currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                                </p-paginator>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [showSaveButton]="getShowSaveButton()" [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>