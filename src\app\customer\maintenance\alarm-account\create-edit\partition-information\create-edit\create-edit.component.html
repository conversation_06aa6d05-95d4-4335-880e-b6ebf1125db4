<div class="h-100 w-100">
    <app-modal [title]="id ? 'Editar partición de alarma' : 'Crear partición de alarma'" [disabled]="disabled">
        <app-modal-body>
            <form class="row" [formGroup]="modalForm">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmTypeName" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmTypeName.invalid && (alarmTypeName.touched || alarmTypeName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmTypeName" name="AlarmTypeName"
                                formControlName="alarmTypeNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmTypeCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmTypeCode.invalid && (alarmTypeCode.touched || alarmTypeCode.dirty))}">
                                Código (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmTypeCode" name="AlarmTypeCode"
                                formControlName="alarmTypeCodeInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El código es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

            </form>

        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</div>