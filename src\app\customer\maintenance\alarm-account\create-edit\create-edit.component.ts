import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { AlarmAccountDto, AlarmAccountServiceProxy } from '@proxies/alarm-account.proxy';
import { finalize } from 'rxjs';

interface IAlarmAccountIcon {
    label: string;
    icon: string;
}

@Component({
    selector: 'app-alarm-account-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class AlarmAccountCreateEditComponent extends ViewComponent implements OnInit {

    private alarmAccountServiceProxy: AlarmAccountServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: string;

    item: AlarmAccountDto = new AlarmAccountDto();

    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get alarmAccountName(): AbstractControl {
        return this.modalForm.controls['alarmAccountNameInput'];
    };

    get alarmAccountCode(): AbstractControl {
        return this.modalForm.controls['alarmAccountCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmAccountServiceProxy = _injector.get(AlarmAccountServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            alarmAccountNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(150)])],
            alarmAccountCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(4)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.alarmAccountServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.alarmAccountName.setValue(this.item.name);
                        this.alarmAccountCode.setValue(this.item.code);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new AlarmAccountDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.alarmAccountName.value;
        this.item.code = this.alarmAccountCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de la cuenta de alarma es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código de la cuenta de alarma es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.alarmAccountServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Cuenta de alarma actualizada exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.alarmAccountServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Cuenta de alarma creada exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}