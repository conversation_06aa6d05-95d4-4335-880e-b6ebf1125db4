import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IFilterOption } from '@core/models/filters';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { AlarmTypeDto, AlarmTypeServiceProxy } from '@proxies/alarm-type.proxy';
import { finalize } from 'rxjs';

interface IAlarmTypeIcon {
    label: string;
    icon: string;
}

@Component({
    selector: 'app-alarm-type-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class AlarmTypeCreateEditComponent extends ViewComponent implements OnInit {

    private alarmTypeServiceProxy: AlarmTypeServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: AlarmTypeDto = new AlarmTypeDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get alarmTypeName(): AbstractControl {
        return this.modalForm.controls['alarmTypeNameInput'];
    };

    alarmTypeColor: string = '#000000';
    alarmTypeBackground: string = '#ffffff';

    get alarmTypeHasProcess(): AbstractControl {
        return this.modalForm.controls['alarmTypeHasProcessSelect'];
    };

    get alarmTypeHasReal(): AbstractControl {
        return this.modalForm.controls['alarmTypeHasRealSelect'];
    };

    get alarmTypeHasReason(): AbstractControl {
        return this.modalForm.controls['alarmTypeHasReasonSelect'];
    };

    get alarmTypeHasDescription(): AbstractControl {
        return this.modalForm.controls['alarmTypeHasDescriptionSelect'];
    };

    get alarmTypeSound(): AbstractControl {
        return this.modalForm.controls['alarmTypeSoundSelect'];
    };

    get alarmTypeHasServiceCall(): AbstractControl {
        return this.modalForm.controls['alarmTypeHasServiceCallSelect'];
    };

    get alarmTypeCloseCode(): AbstractControl {
        return this.modalForm.controls['alarmTypeCloseCodeInput'];
    };

    get alarmTypeCode(): AbstractControl {
        return this.modalForm.controls['alarmTypeCodeInput'];
    };

    icon: IAlarmTypeIcon;

    icons: IAlarmTypeIcon[] = [
        {
            label: 'Alerta',
            icon: 'alert.svg'
        },
        {
            label: 'Robo',
            icon: 'robber.svg'
        },
        {
            label: 'Incendio',
            icon: 'fire.svg'
        }
    ];

    sounds: IFilterOption<string>[] = [
        {
            label: 'Sin sonido',
            value: 'none'
        },
        {
            label: 'Robo',
            value: 'robber.mp3'
        },
        {
            label: 'Incendio',
            value: 'fire.mp3'
        }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmTypeServiceProxy = _injector.get(AlarmTypeServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            alarmTypeNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            alarmTypeBackgroundInput: ['#ffffff', Validators.compose([Validators.required, Validators.maxLength(32)])],
            alarmTypeHasProcessSelect: ['false', Validators.compose([])],
            alarmTypeHasServiceCallSelect: ['false', Validators.compose([])],            
            alarmTypeHasRealSelect: ['true', Validators.compose([])],
            alarmTypeHasReasonSelect: ['true', Validators.compose([])],
            alarmTypeHasDescriptionSelect: ['true', Validators.compose([])],
            alarmTypeSoundSelect: ['none', Validators.compose([])],
            alarmTypeCloseCodeInput: ['', Validators.compose([Validators.maxLength(32)])],
            alarmTypeCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.alarmTypeServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.alarmTypeName.setValue(this.item.name);
                        this.alarmTypeColor = this.item.color;
                        this.alarmTypeBackground = this.item.background;
                        this.alarmTypeHasProcess.setValue(this.item.hasProcess ? 'true' : 'false');
                        this.alarmTypeHasReal.setValue(this.item.hasReal ? 'true' : 'false');
                        this.alarmTypeHasReason.setValue(this.item.hasReason ? 'true' : 'false');
                        this.alarmTypeHasDescription.setValue(this.item.hasDescription ? 'true' : 'false');
                        this.alarmTypeHasServiceCall.setValue(this.item.hasServiceCall ? 'true' : 'false');
                        this.alarmTypeSound.setValue(isNullEmptyOrWhiteSpace(this.item.sound) ? 'none' : this.item.sound);
                        this.alarmTypeCloseCode.setValue(this.item.closeCode);
                        this.alarmTypeCode.setValue(this.item.code);

                        this.icon = this.item.icon ? 
                            this.icons.find(p => p.icon == this.item.icon) : 
                            undefined;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new AlarmTypeDto();
        }
    }

    onHasProcessChange(event: any) : void {
        this.item.hasProcess = event.target.value == 'true';
    }

    onHasRealChange(event: any) : void {
        this.item.hasReal = event.target.value == 'true';
    }

    async save(): Promise<void> {

        this.item.name = this.alarmTypeName.value;
        this.item.color = this.alarmTypeColor;
        this.item.background = this.alarmTypeBackground;
        this.item.hasProcess = this.alarmTypeHasProcess.value == 'true';
        this.item.hasReal = this.alarmTypeHasReal.value == 'true';
        this.item.hasReason = this.alarmTypeHasReason.value == 'true';
        this.item.hasDescription = this.alarmTypeHasDescription.value == 'true';
        this.item.hasServiceCall = this.alarmTypeHasServiceCall.value == 'true';
        this.item.sound = this.alarmTypeSound.value;
        this.item.closeCode = this.alarmTypeCloseCode.value;
        this.item.code = this.alarmTypeCode.value;
        this.item.icon = this.icon?.icon;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre del tipo de alarma es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código del tipo de alarma es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.alarmTypeServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de alarma actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.alarmTypeServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de alarma creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}