import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss'],
})
export class SelectComponent implements OnInit {
  @Input() label: string = '';
  @Input() defaultText: string = '';
  @Input() items: Array<{ [key: string]: any }> = [];
  @Input() value: string = '';
  @Input() _id: string = '';
  @Input() name: string = '';

  // Output para emitir el cambio de valor
  @Output() valueChange = new EventEmitter<string>();

  constructor() { }

  ngOnInit() {}

  // Método para manejar el cambio de selección
  onSelectionChange(event: any) {
    this.value = event.target.value;
    console.log(this.value, 'xddddddddd')
    this.valueChange.emit(this.value); // Emitir el nuevo valor seleccionado
  }
}
