import { Component, Injector, Input, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { InspectionServiceProxy, InspectionDto } from '@proxies/inspection.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-inspection-cancel',
    templateUrl: 'cancel-inspection.component.html',
    styleUrls: [
        'cancel-inspection.component.scss'
    ]
})
export class InspectionCancelComponent extends ViewComponent implements OnInit {

    private inspectionServiceProxy: InspectionServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;

    item!: InspectionDto;

    modalForm!: FormGroup;
    disabled!: boolean;

    get inspectionCancelDescription(): AbstractControl {
        return this.modalForm.controls['inspectionCancelDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.formBuilder = _injector.get(FormBuilder);
        this.inspectionServiceProxy = _injector.get(InspectionServiceProxy);

        this.modalForm = this.formBuilder.group({
            inspectionCancelDescriptionInput: ['', Validators.compose([Validators.required, Validators.maxLength(5000)])],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.disabled = false;
                },
                error: () => this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {

        const description: string = this.inspectionCancelDescription.value;

        if (isNullEmptyOrWhiteSpace(description)) {
            this.message.info('El motivo del cancelación de la inspección es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .cancel(this.item.id, description)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss()
            })).subscribe({
                next: () => {
                    this.notify.success('Inspección cancelada satisfactoriamente', 5000);
                    this.dialog.dismiss(true);
                }
            });
    }
}