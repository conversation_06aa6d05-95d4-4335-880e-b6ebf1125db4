.monitoring-statistic__container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 10px;

    .monitoring-statistic__item {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 10px;
        border-radius: 5px;
        background: var(--background);
        width: 250px;
        cursor: pointer;

        &:hover {
            opacity: 0.9;
        }

        &:active {
            opacity: 0.7;
        }

        .monitoring-statistic__icon {
            height: 28px;
            margin-bottom: 5px;
        }

        .monitoring-statistic__label {
            color: var(--color);
            font-size: 14px;
            font-weight: bold;
        }

        .monitoring-statistic__badge {
            position: absolute;
            right: 10px;
            top: 10px;
            background: #FFD132;
            color: #000000;
            font-weight: bold;
            font-size: 12px;
            padding: 0px 5px;
            border: 2px solid #000000;
            border-radius: 1px;

            .monitoring-statistic__badge-box {
                position: absolute;
                left: -5px;
                bottom: -5px;
                border-left: 2px solid #000000;
                border-bottom: 2px solid #000000;
                border-radius: 1px;
                height: 15px;
                width: 15px;
            }
        }

        .monitoring-statistic__icon,
        .monitoring-statistic__label,
        .monitoring-statistic__badge  {
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
        }
    }
}