<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="loaded ? meetCompleted ? 'Visualizar charla' : (id ? 'Editar charla' : 'Crear charla') : 'Cargando...'" [disabled]="disabled" size="large">
        <app-modal-body>
            <p-tabView *ngIf="loaded" [(activeIndex)]="activeIndex" [scrollable]="true">
                <p-tabPanel header="Información general">
                    <app-meet-general-information 
                        [modalForm]="modalForm"
                        [meet]="item" 
                        [completed]="meetCompleted"
                        [centers]="centers"
                        [(meetDate)]="meetDate"
                        [(meetStartTime)]="meetStartTime"
                        [(meetEndTime)]="meetEndTime"/>
                </p-tabPanel>
                <p-tabPanel header="Asistencia">
                    <app-meet-assistance-information 
                        [meet]="item"
                        [completed]="meetCompleted"/>
                </p-tabPanel>   
                <p-tabPanel header="Historial">
                    <app-meet-operation-information  
                        [meet]="item"
                        [completed]="meetCompleted"/>
                </p-tabPanel>  
                <p-tabPanel *ngIf="id" [disabled]="item.status != meetStatuses.executed" header="Ejecución">
                    <app-meet-complete-information
                        [meet]="item"
                        [modalForm]="modalForm"
                        [completed]="meetCompleted"/>
                </p-tabPanel>           
                <p-tabPanel *ngIf="id" [disabled]="item.status != meetStatuses.canceled" header="Cancelación">
                    <app-meet-cancel-information
                        [meet]="item"
                        [modalForm]="modalForm"
                        [completed]="meetCompleted"/>
                </p-tabPanel>                      
            </p-tabView>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" [showSaveButton]="!meetCompleted && loaded" (onSave)="save()" />
    </app-modal>
</form>