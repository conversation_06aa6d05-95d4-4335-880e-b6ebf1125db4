<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar Sistema 2' : 'Crear Sistema 2'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <ng-container *ngIf="id || maintenanceMacroSystem; else creationEntity">
                    <div class="col-12">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label for="MaintenanceMacroSystemName" class="form-label">
                                    Sistema 1 (*)
                                </label>

                                <input *ngIf="maintenanceMacroSystem; else editionEntity" type="text"
                                    class="form-control" id="MaintenanceMacroSystemName"
                                    name="MaintenanceMacroSystemName"
                                    value="{{maintenanceMacroSystem.name}} - {{maintenanceMacroSystem.code}}" readonly>

                                <ng-template #editionEntity>
                                    <input type="text" class="form-control" id="MaintenanceMacroSystemName"
                                        name="MaintenanceMacroSystemName"
                                        value="{{item?.maintenanceMacroSystem?.name}} - {{item?.maintenanceMacroSystem?.code}}"
                                        readonly>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                </ng-container>

                <ng-template #creationEntity>
                    <div class="col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
                        <div class="ion-input">
                            <div class="input-control">
                                <label class="form-label" for="MaintenanceMacroSystem">
                                    Sistema 1 (*)
                                </label>
                                <div class="input-group-close">

                                    <div class="input-group action">
                                        <input (click)="showFindMaintenanceMacroSystem()" class="form-control rounded"
                                            id="MaintenanceMacroSystem" name="MaintenanceMacroSystem"
                                            value="{{maintenanceMacroSystemLabel}}" readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>

                                    <button (click)="clearMaintenanceMacroSystem()" *ngIf="item.maintenanceMacroSystem"
                                        class="input-group-close-action">
                                        <ion-icon class="input-group-close-action-icon" name="close"></ion-icon>
                                    </button>

                                </div>
                            </div>
                        </div>
                    </div>
                </ng-template>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceMicroSystemName" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceMicroSystemName.invalid && (maintenanceMicroSystemName.touched || maintenanceMicroSystemName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceMicroSystemName"
                                name="MaintenanceMicroSystemName" formControlName="maintenanceMicroSystemNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceMicroSystemCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceMicroSystemCode.invalid && (maintenanceMicroSystemCode.touched || maintenanceMicroSystemCode.dirty))}">
                                Código (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceMicroSystemCode"
                                name="MaintenanceMicroSystemCode" formControlName="maintenanceMicroSystemCodeInput"
                                maxlength="3">
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceMicroSystemSerie" class="form-label">
                                Serie
                            </label>

                            <input type="text" class="form-control" id="MaintenanceMicroSystemSerie"
                                name="MaintenanceMicroSystemSerie" formControlName="maintenanceMicroSystemSerieInput"
                                readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>