<div class="h-100 w-100">
    <app-modal [title]="id ? 'Editar tipo de alarma' : 'Crear tipo de alarma'" [disabled]="disabled">
        <app-modal-body>
            <form class="row" [formGroup]="modalForm">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmTypeName" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmTypeName.invalid && (alarmTypeName.touched || alarmTypeName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmTypeName" name="AlarmTypeName"
                                formControlName="alarmTypeNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="AlarmTypeHasProcess">
                                Tiene tratamiento (*)
                            </label>
                            <select (change)="onHasProcessChange($event)" class="form-control" id="AlarmTypeHasProcess"
                                name="AlarmTypeHasProcess" formControlName="alarmTypeHasProcessSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <ng-container *ngIf="item.hasProcess">

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label class="form-label" for="AlarmTypeHasReal">
                                    ¿Mostrar Real Si/No? (*)
                                </label>
                                <select (change)="onHasRealChange($event)" class="form-control" id="AlarmTypeHasReal"
                                    name="AlarmTypeHasReal" formControlName="alarmTypeHasRealSelect">
                                    <option value="true">
                                        Si
                                    </option>
                                    <option value="false">
                                        No
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <ng-container *ngIf="item.hasReal">

                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AlarmTypeHasReason">
                                        ¿Mostrar motivo? (*)
                                    </label>
                                    <select class="form-control" id="AlarmTypeHasReason" name="AlarmTypeHasReason"
                                        formControlName="alarmTypeHasReasonSelect">
                                        <option value="true">
                                            Si
                                        </option>
                                        <option value="false">
                                            No
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                    </ng-container>

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label class="form-label" for="AlarmTypeHasDescription">
                                    ¿Mostrar descripción? (*)
                                </label>
                                <select class="form-control" id="AlarmTypeHasDescription" name="AlarmTypeHasDescription"
                                    formControlName="alarmTypeHasDescriptionSelect">
                                    <option value="true">
                                        Si
                                    </option>
                                    <option value="false">
                                        No
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">

                                <label for="AlarmTypeColor" class="form-label">
                                    Color (*)
                                </label>

                                <div class="form-group color-input-container">
                                    <p-colorPicker [(ngModel)]="alarmTypeColor" [ngModelOptions]="{standalone: true}"
                                        class="pe-2 my-auto" id="AlarmTypeColor" name="AlarmTypeColor" format="hex">
                                    </p-colorPicker>
                                    <input [(ngModel)]="alarmTypeColor" [ngModelOptions]="{standalone: true}"
                                        id="AlarmTypeColorValue" name="AlarmTypeColorValue" class="form-control"
                                        maxlength="32">
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">

                                <label for="AlarmTypeBackground" class="form-label">
                                    Fondo (*)
                                </label>

                                <div class="form-group color-input-container">
                                    <p-colorPicker [(ngModel)]="alarmTypeBackground"
                                        [ngModelOptions]="{standalone: true}" class="pe-2 my-auto"
                                        id="AlarmTypeBackground" name="AlarmTypeBackground" format="hex">
                                    </p-colorPicker>
                                    <input id="AlarmTypeBackgroundValue" name="AlarmTypeBackgroundValue"
                                        class="form-control" [(ngModel)]="alarmTypeBackground"
                                        [ngModelOptions]="{standalone: true}" maxlength="32">
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">

                                <label for="AlarmTypeIcon" class="form-label d-block">
                                    ícono (*)
                                </label>

                                <p-dropdown [(ngModel)]="icon" class="d-block" styleClass="w-100" [options]="icons"
                                    placeholder="Seleccione" [autoDisplayFirst]="false" [filter]="false"
                                    [showClear]="false" [ngModelOptions]="{standalone: true}" appendTo="body">
                                    <ng-template let-option pTemplate="item">
                                        <span class="ml-1 mt-1">
                                            {{option.label}} <span class="asset-icon">
                                                <img height="16" src="/assets/icons/{{option.icon}}">
                                            </span>
                                        </span>
                                    </ng-template>
                                    <ng-template let-option pTemplate="selectedItem">
                                        <span *ngIf="option" class="ml-1 mt-1">
                                            {{option.label}} <span class="asset-icon">
                                                <img height="16" src="/assets/icons/{{option.icon}}">
                                            </span>
                                        </span>
                                    </ng-template>
                                </p-dropdown>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label class="form-label" for="AlarmTypeSound">
                                    Sonido (*)
                                </label>
                                <select class="form-control" id="AlarmTypeSound" name="AlarmTypeSound"
                                    formControlName="alarmTypeSoundSelect">
                                    <option *ngFor="let item of sounds" [value]="item.value">
                                        {{item.label}}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                </ng-container>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="AlarmTypeHasServiceCall">
                                ¿Genera llamada de servicio? (*)
                            </label>
                            <select class="form-control" id="AlarmTypeHasServiceCall" name="AlarmTypeHasServiceCall"
                                formControlName="alarmTypeHasServiceCallSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmTypeCloseCode" class="form-label">
                                Código de cierre
                            </label>

                            <input type="text" class="form-control" id="AlarmTypeCloseCode" name="AlarmTypeCloseCode"
                                formControlName="alarmTypeCloseCodeInput">
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmTypeCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmTypeCode.invalid && (alarmTypeCode.touched || alarmTypeCode.dirty))}">
                                Código de alarma (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmTypeCode" name="AlarmTypeCode"
                                formControlName="alarmTypeCodeInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El código de alarma es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-12 mb-4">
                    <small>
                        Notas: Si hay múltiples códigos pertenecientes al mismo nombre puede separarlos por ",".
                    </small>
                </div>

            </form>

        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</div>