<div class="row">

    <div class="col-12">
        <ion-label class="d-block fz-normal fw-bold" color="dark">
            Miembros de {{hierarchy.name}}
        </ion-label>
    </div>

    <div class="col-12 text-end mb-4">

        <ion-button (click)="getData()" class="ion-option me-2" color="tertiary" size="small" fill="solid">
            <ion-icon name="search"></ion-icon>
        </ion-button>

        <ion-button *ngIf="'Pages.Management.Hierarchy.Modify' | permission" (click)="createItem()" class="ion-option" size="small" color="primary" fill="solid">
            <ion-icon name="add"></ion-icon>
            <ion-label class="fz-small d-none d-lg-inline-block">
                Agregar miembro
            </ion-label>
        </ion-button>

    </div>

    <div class="col-12">
        <p-table #dataTable sortMode="multiple" [value]="table.records"
            [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
            ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                    !(
                        [
                            'Pages.Management.Employee.Modify',
                            'Pages.Management.Training.Delete'
                        ] | permissionAny
                    )
                    ">
                        Acciones
                    </th>
                    <th style="min-width: 200px">
                        Recurso
                    </th>
                    <th style="min-width: 150px; width: 150px; max-width: 150px;">
                        Área
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr>
                    <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                    !(
                        [
                            'Pages.Management.Employee.Modify',
                            'Pages.Management.Training.Delete'
                        ] | permissionAny
                    )
                    ">
                        <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                            fill="solid" size="small">
                            <ion-label class="fz-small">
                                Acciones
                            </ion-label>
                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                        </ion-button>
                    </td>
                    <td style="min-width: 200px">
                        {{record?.employee?.firstName}} {{record?.employee?.middleName}} {{record?.employee?.lastName}}
                    </td>
                    <td style="min-width: 150px; width: 150px; max-width: 150px;">
                        {{record?.area?.prcName}}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
        <div class="primeng-paging-container">
            <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator (onPageChange)="getData($event)"
                [totalRecords]="table.totalRecordsCount" [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                [showCurrentPageReport]="true" dropdownAppendTo="body"
                currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
            </p-paginator>
        </div>
    </div>
</div>