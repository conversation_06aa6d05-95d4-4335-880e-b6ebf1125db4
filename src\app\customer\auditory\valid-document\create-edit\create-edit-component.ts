import { Component, Injector, Input, OnInit, NgModule, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AbstractControl, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { DocumentTypeFormat, Status, DocumentDto, DocumentStatusDto, TagDto, DocumentTypeDto, StatusDto, MacroProcessDto, ProcessDto, SubProcessDto, DocumentServiceProxy, DocumentModifierDto, DocumentTagDto, DocumentResource, DocumentComparisonDto } from '@proxies/auditory/document.proxy';
import { AreaPositionDto, ProcessTierDto, Tier } from '@proxies/auditory/area-position.proxy'
import { AppAuditoryFindEmployeeComponent } from '@components/auditory/find-employee/find-employee.component';
import { UserEmployeeDto, UserDto, UserServiceProxy } from '@proxies/user.proxy';
import { EmployeeDto } from '@proxies/employee.proxy';
import { UploadResource } from '@core/utils/core.request';
import { finalize } from 'rxjs';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { IUploadProgressRespose } from '@core/models/app-config';
import { AppDocumentPreviewComponent } from '@components/file-preview-pdf/file-preview-pdf.component';
import { AppAuditoryService } from '@core/services/auditory/auditory.service';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { TagModel } from 'ngx-chips/core/tag-model'
import { AppFindTagComponent } from '@components/auditory/find-tag/find-tag.component';
import { AppFindDocumentComponent } from '@components/auditory/find-document/find-document.component';
import { IntegrationAreaSigDto, IntegrationManagementSigDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { DocumentNotificationServiceProxy, DocumentNotificationDto } from '@proxies/auditory/document-notification.proxy';
import { Button } from 'primeng/button';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class DocumentCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;
    @Input() code: string;
    @Input() onlyview: boolean = false;
    @Input() newversion: boolean = false;
    @Input() edit: boolean = false;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    private documentServiceProxy: DocumentServiceProxy;
    private documentNotificationServiceProxy: DocumentNotificationServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private auditoryDocumentService: AppAuditoryService;
    private formBuilder: FormBuilder;

    size: number = 15_728_640;
    uploadResources: UploadResource[] = [];
    comparisonArray: DocumentComparisonDto[] = [];
    item: DocumentDto = new DocumentDto();
    disabled: boolean = false;
    types: DocumentTypeDto[] = [];
    statuses: StatusDto[];
    macroProcesses: MacroProcessDto[] = [];
    processes: ProcessDto[] = [];
    subProcesses: SubProcessDto[] = [];
    users: UserDto[] = [];
    areas: IntegrationAreaSigDto[] = [];
    tags: TagDto[];
    managements: IntegrationManagementSigDto[];
    loaded: boolean = false;
    isOnPreview: boolean = false;

    modalForm!: FormGroup;

    processControl: boolean
    processTier: ProcessTierDto | undefined;
    availableUsers: DocumentModifierDto[] = [];
    availableNotification: DocumentNotificationDto[] = [];
    availableTags: DocumentTagDto[] = [];
    tableStatuses: DocumentStatusDto[] = [];
    locationTypes: string[] = ["VIRTUAL", "FÍSICO"];
    provisionTypes: string[] = ["ARCHIVAMIENTO", "ELIMINACIÓN"];

    get documentRel(): AbstractControl {
        return this.modalForm.controls['documentRelInput'];
    };

    get documentName(): AbstractControl {
        return this.modalForm.controls['documentNameInput'];
    };

    get documentCode(): AbstractControl {
        return this.modalForm.controls['documentCodeInput'];
    };

    get documentVersion(): AbstractControl {
        return this.modalForm.controls['documentVersionInput'];
    };

    get documentCreatedBy(): AbstractControl {
        return this.modalForm.controls['documentCreatedByInput'];
    };

    get documentStatus(): AbstractControl {
        return this.modalForm.controls['documentStatusSelect'];
    };

    get documentArea(): AbstractControl {
        return this.modalForm.controls['documentAreaSelect'];
    };

    get documentManagement(): AbstractControl {
        return this.modalForm.controls['documentManagementSelect'];
    };

    get documentType(): AbstractControl {
        return this.modalForm.controls['documentTypeSelect'];
    };

    get documentMacroProcess(): AbstractControl {
        return this.modalForm.controls['documentMacroProcessSelect'];
    };

    get documentProcess(): AbstractControl {
        return this.modalForm.controls['documentProcessSelect'];
    };

    get documentSubProcess(): AbstractControl {
        return this.modalForm.controls['documentSubProcessSelect'];
    };

    get documentModifier(): AbstractControl {
        return this.modalForm.controls['documentModifierSelect'];
    };

    get documentNotification(): AbstractControl {
        return this.modalForm.controls['documentNotificationSelect'];
    };

    get documentTag(): AbstractControl {
        return this.modalForm.controls['documentTagSelect'];
    };

    get documentLocation(): AbstractControl {
        return this.modalForm.controls['documentLocationInput'];
    };

    get documentLocationType(): AbstractControl {
        return this.modalForm.controls['documentLocationTypeSelect'];
    };

    get documentProvision(): AbstractControl {
        return this.modalForm.controls['documentProvisionSelect'];
    };

    get documentRetentionTime(): AbstractControl {
        return this.modalForm.controls['documentRetentionTimeInput'];
    };

    constructor(_injector: Injector, private router: Router) {
        super(_injector);

        this.documentServiceProxy = _injector.get(DocumentServiceProxy);
        this.documentNotificationServiceProxy = _injector.get(DocumentNotificationServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.auditoryDocumentService = _injector.get(AppAuditoryService);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            documentRelInput: ['Presiona para buscar un documento'],
            documentTypeSelect: ['-1', Validators.compose([Validators.required])],
            documentStatusSelect: [{ value: '1', disabled: true }, Validators.compose([Validators.required])],
            documentManagementSelect: ['-1', Validators.compose([Validators.required])],
            documentMacroProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentSubProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentAreaSelect: ['-1', Validators.compose([Validators.required])],
            documentNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(100)])],
            documentCodeInput: [{ value: '', disabled: true }, Validators.compose([])],
            documentLocationTypeSelect: ['-1'],
            documentLocationInput: [''],
            documentRetentionTimeInput: [''],
            documentProvisionSelect: ['-1'],
            documentVersionInput: [{ value: '1', disabled: true }, Validators.compose([])],
            documentTagSelect: [this.availableTags],
            documentNotificationSelect: [this.availableNotification],
            documentModifierSelect: [this.availableUsers],
            documentCreatedByInput: [{ value: this.session?.user?.name + ' ' + this.session?.user?.surname, disabled: true }, Validators.compose([])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.processControl = this.session?.processTier?.find(x => x.processid == 0) != null;
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (!this.id) {
            await loading.dismiss();
            this.disabled = false;
            if (!this.processControl) {
                this.documentManagement.setValue(this.session?.employee?.empAreaSigQuery?.u_bsf_ger_sig);
                this.documentManagement.disable();
                this.documentArea.setValue(this.session?.employee?.empAreaSigQuery?.code);
                this.documentArea.disable();
                let currentProcessForTier: number[] = []
                currentProcessForTier = this.session?.processTier?.filter(x => x.tiers.find(y => y == Tier.Modifier) != null)?.map(x => x.processid);
                this.processes = this.processes.filter(x => currentProcessForTier.includes(parseInt(x.id)))
                this.macroProcesses = this.macroProcesses.filter(x => this.processes.find(y => y.macroprocessid == x.id) != null);
            }
            return;
        }

        if (this.onlyview) {
            this.modalForm.disable();
        }

        if (this.processControl) {
            this.documentTag.enable();
            this.documentNotification.enable();
            this.documentModifier.enable();
            this.documentLocation.enable();
            this.documentLocationType.enable();
            this.documentProvision.enable();
            this.documentRetentionTime.enable();
        }

        this.documentServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    this.item = response;
                    this.processTier = this.session?.processTier?.find(x => x.processid.toString() == this.item.documentProcess.processid);
                    const documentprocess = this.processes.filter(x => x.id == this.item.documentProcess.processid)[0];
                    this.documentRel.setValue(this.item.origin || '');
                    this.documentName.setValue(this.item.name.toUpperCase() || '');
                    this.documentCode.setValue(this.item.code || '');
                    this.documentType.setValue(this.item.documentTypeId || '-1');
                    this.documentType.disable();
                    this.documentStatus.setValue(this.item.documentStatusId);
                    this.documentManagement.setValue(this.item.documentManagementCode || '-1');
                    this.documentManagement.disable();
                    this.documentArea.setValue(this.item.documentAreaCode || '-1');
                    this.documentArea.disable();
                    this.documentMacroProcess.setValue(documentprocess.macroprocessid || '-1');
                    this.documentProcess.setValue(documentprocess.id || '-1');
                    this.documentSubProcess.setValue(this.item.documentProcessId || '-1');
                    this.documentVersion.setValue(this.id && !this.onlyview ? (this.item.version + 1) : this.item.version || '1');
                    this.documentLocationType.setValue(this.item.locationType)
                    this.documentLocation.setValue(this.item.location)
                    this.documentProvision.setValue(this.item.provision)
                    this.documentRetentionTime.setValue(this.item.retentionTime)

                    this.documentMacroProcess.disable();
                    this.documentProcess.disable();
                    this.documentSubProcess.disable();

                    if (this.onlyview || this.id) {
                        this.documentName.disable();
                    }
                    
                    let documentComparison: DocumentComparisonDto = new DocumentComparisonDto();
                    documentComparison.documentid = this.item.documentId;
                    documentComparison.version = this.item.version;
                    documentComparison.description = this.item.compareResult
                    documentComparison.date = this.item.documentStatuses.find(x => x.statusid == Status.Valid)?.createdat
                    this.addComparison(documentComparison);

                    if (this.processTier?.tiers?.includes(Tier.Owner) || this.processControl) {
                        this.table.records = this.getDataStatus(this.item.documentStatuses.slice(0, 10))
                        this.table.totalRecordsCount = this.item.documentStatuses.length
                    }
                    else {
                        let resultado = Object.values(
                            this.item.documentStatuses.reduce((acc, item) => {
                                if (
                                    !acc[item.statusid] ||
                                    item.createdat > acc[item.statusid].createdat
                                ) {
                                    acc[item.statusid] = item;
                                }
                                return acc;
                            }, {} as Record<number, DocumentStatusDto>)
                        );
                        resultado = resultado.filter(x => 
                            x.statusid == Status.Approved ||
                            x.statusid == Status.Reviewed ||
                            x.statusid == Status.WorkingOn)
                        this.item.documentStatuses = resultado;
                        this.table.records = this.getDataStatus(this.item.documentStatuses.slice(0, 10))
                        this.table.totalRecordsCount = this.item.documentStatuses.length
                    }

                    let creator = this.auditoryDocumentService.users.find(x => x.emailAddress == this.item.createdBy);
                    this.documentCreatedBy.setValue(creator?.name + ' ' + creator?.surname)
                    for (let tagdata of this.item.documentTags) {
                        tagdata.name = this.tags.find(tag => tag.id === tagdata.tagid)?.name || '';
                        this.availableTags.push(tagdata);
                    }
                    this.documentTag.setValue(this.availableTags);
                    for (let modifierData of this.item.documentModifiers) {
                        modifierData.name = this.users.find(user => user.id === modifierData.id)?.emailAddress || '';
                        this.availableUsers.push(modifierData);
                    }
                    this.documentModifier.setValue(this.availableUsers);

                    this.availableNotification = this.item.documentNotifications;
                    this.documentNotification.setValue(this.availableNotification);

                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {
        this.item.name = this.documentName.value.toUpperCase();
        this.item.documentId = this.edit ? this.item.documentId : this.id;
        this.item.documentTypeId = this.documentType.value;
        this.item.documentStatusId = this.newversion ? Status.WorkingOn : this.documentStatus.value;
        this.item.documentManagementCode = this.documentManagement.value;
        this.item.documentAreaCode = this.documentArea.value;
        this.item.version = this.documentVersion.value;
        this.item.documentProcessId = this.documentSubProcess.value;
        this.item.origin = this.documentRel.value;
        this.item.code = this.documentCode.value;
        this.item.location = (this.documentLocation.value ?? "").toUpperCase();
        this.item.locationType = this.documentLocationType.value;
        this.item.provision = this.documentProvision.value;
        this.item.retentionTime = this.documentRetentionTime.value;
        this.item.documentTags = [];
        for (let tagdata of this.documentTag.value) {
            let tag = new DocumentTagDto();
            tag.documentid = this.item.id;
            tag.tagid = tagdata.tagid;
            this.item.documentTags.push(tag);
        }
        this.item.documentNotifications = [];
        for (let notificationdata of this.documentNotification.value) {
            let notification = new DocumentNotificationDto();
            notification.documentid = this.item.id;
            notification.email = notificationdata.email;
            notification.read = notificationdata.read;
            notification.send = notificationdata.send;
            this.item.documentNotifications.push(notification);
        }
        this.item.documentModifiers = [];
        for (let modifierdata of this.documentModifier.value) {
            let modifier = new DocumentModifierDto();
            modifier.documentid = this.item.id;
            modifier.email = modifierdata.email;
            this.item.documentModifiers.push(modifier);
        }
        this.item.uploadResources = this.uploadResources;
        this.item.filePath = this.item.uploadResources.length != 0 ? this.item.uploadResources[0].name : this.item.filePath;

        if (this.viewDocumentRel && (!this.documentRel.value || this.documentRel.value == 'Presiona para buscar un documento')) {
            this.message.info('El documento relacionado es obligatorio', 'Aviso');
            return;
        }

        if (this.item.documentTypeId < 0) {
            this.message.info('El tipo del documento es obligatorio', 'Aviso');
            return;
        }

        if (this.item.documentManagementCode == '-1') {
            this.message.info('La gerencia es obligatoria', 'Aviso');
            return;
        }

        if (this.item.documentAreaCode == '-1') {
            this.message.info('El area es obligatoria', 'Aviso');
            return;
        }

        if (this.uploadResources.length == 0 && !this.edit) {
            this.message.info('Debe ingresar un archivo de manera obligatoria', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();
        if (this.newversion) {
            if (this.item.documentId) {
                this.documentServiceProxy
                    .getRelated(this.item.documentId)
                    .pipe(finalize(async () => {
                        this.disabled = false;
                        await loading.dismiss();
                    })).subscribe({
                        next: (response) => {
                            let relateddoc = response;
                            if (!relateddoc.id) {
                                this.uploads(this.item, async () => {
                                    this.documentServiceProxy
                                        .create(this.item)
                                        .pipe(finalize(async () => {
                                            this.disabled = false;
                                            await loading.dismiss();
                                        })).subscribe({
                                            next: () => {
                                                this.notify.success('Documento creado exitosamente', 5000);
                                                this.dialog.dismiss(true);
                                                this.router.navigate(['/customer/auditory/document/dashboard']);
                                            }
                                        });
                                });
                            }
                            else {
                                this.notify.error('Documento ya tiene una nueva version en proceso', 5000);
                            }
                        },
                        error: async () => await this.dialog.dismiss()
                    });
            }
            else {
                var exist = await this.nameExist(this.item.name);
                if (exist?.id) {
                    this.notify.error('El nombre ya se encuentra registrado en un documento.')
                    await loading.dismiss();
                    this.disabled = false;
                    return;
                }

                this.uploads(this.item, async () => {
                    this.documentServiceProxy
                        .create(this.item)
                        .pipe(finalize(async () => {
                            this.disabled = false;
                            await loading.dismiss();
                        })).subscribe({
                            next: () => {
                                this.notify.success('Documento creado exitosamente', 5000);
                                this.dialog.dismiss(true);
                                this.router.navigate(['/customer/auditory/document/dashboard']);
                            }
                        });
                });
            }
        }
        if (this.edit) {
            this.documentServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Documento actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    getData(event?: TableLazyLoadEvent): void {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        let take = this.table.getMaxResultCount(this.paginator, event);
        let skip = this.table.getSkipCount(this.paginator, event);

        this.tableStatuses = [...this.item.documentStatuses];
        this.table.totalRecordsCount = this.tableStatuses.length;
        this.table.records = this.getDataStatus(this.tableStatuses.slice(skip, take + skip));
    }

    getDataStatus(data: DocumentStatusDto[]): any {
        return data.map(x => {
            let usr = this.auditoryDocumentService.users?.find(y => y.emailAddress == x.createdby)
            return {
                ...x,
                status: this.auditoryDocumentService.filter?.statuses?.find(y => y.id == x.statusid)?.name,
                user: usr?.name + ' ' + usr?.surname
            }
        });
    }



    private getInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.documentServiceProxy
                .getInputs()
                .subscribe({
                    next: (response) => {
                        this.macroProcesses = response.macroProcess;
                        this.subProcesses = response.subProcess;
                        this.processes = response.process;
                        this.tags = response.tags;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private getAreasAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllAreaSig().subscribe({
                next: (response) => {
                    this.areas = response.items;
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private getManagementAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllManagementSig().subscribe({
                next: (response) => {
                    this.managements = response.items;
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private async loadInputs(): Promise<void> {
        await Promise.all([
            this.getInputs(),
            this.getAreasAsync(),
            this.getManagementAsync()
        ]);
        this.types = this.auditoryDocumentService.filter.documentTypes;
        this.statuses = this.auditoryDocumentService.filter.statuses;
        this.users = this.auditoryDocumentService.users;
    }

    loadResource(): void {
        let filename = this.item.code + " - " + this.item.name + '.' + this.item.filePath.split('.').pop();
        this.onPreviewFile(this.documentServiceProxy
            .getResource(this.item.fileName, filename, DocumentResource.Pdf));
        this.saveAsRead();
    }

    saveAsRead(): void {
        let notification = new DocumentNotificationDto();
        notification.documentid = this.id;
        this.documentNotificationServiceProxy.updateRead(notification).subscribe();
    }

    downloadResource(): void {
        let filename = this.item.code + " - " + this.item.name + '.' + this.item.filePath.split('.').pop();
        window.open(this.documentServiceProxy
            .getResource(this.item.fileName, filename, DocumentResource.Original), '_blank');     
    }

    documentTypeChange(event: any): void {
        const selectedValue = this.documentType?.value;
        if (!(!this.id && this.types.find(x => x.id == selectedValue)?.format == DocumentTypeFormat.Related.toString())) {
            this.documentRel.setValue(null);
            this.documentMacroProcess.setValue('-1');
            this.documentProcess.setValue('-1');
            this.documentSubProcess.setValue('-1');
            if (this.processControl) {
                this.documentManagement.enable();
                this.documentArea.enable();
                this.documentManagement.setValue('-1');
                this.documentArea.setValue('-1');
            }
            this.documentMacroProcess.enable();
            this.documentProcess.enable();
            this.documentSubProcess.enable();
        }
    }

    get viewDocumentRel() {
        const selectedValue = this.documentType?.value;
        return !this.id && this.types.find(x => x.id == selectedValue)?.format == DocumentTypeFormat.Related.toString();
    }

    get viewDocumentRelWithId() {
        const selectedValue = this.documentType?.value;
        return this.types.find(x => x.id == selectedValue)?.format == DocumentTypeFormat.Related.toString();
    }

    get processSelect() {
        const selectedValue = this.documentMacroProcess?.value;
        return this.processes.filter(x => x.macroprocessid == selectedValue);
    }

    get subProcessSelect() {
        const selectedValue = this.documentProcess?.value;
        return this.subProcesses.filter(x => x.processid == selectedValue);
    }

    get areasSelect() {
        const selectedValue = this.documentManagement?.value;
        return this.areas.filter(x => x.managementSigQuery?.code == selectedValue);
    }

    onUploadItem(event: UploadResource): void {
        this.uploadResources ??= [];
        if (!event.name.endsWith(".xlsx") && !event.name.endsWith(".docx") && !event.name.endsWith(".pptx")) {
            this.notify.error('El archivo seleccionado debe tener como extension .docx, .xlsx o .pptx');
            return;
        }
        if (this.uploadResources.length == 1) {
            this.notify.warn('Ya ha seleccionado un archivo.', 3000);
            return;
        }

        this.uploadResources.push(event);
        if (!this.id && !this.documentName.value) {
            this.documentName.setValue(event.name.toUpperCase());
        }
    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.uploadResources ??= [];
                this.uploadResources.splice(index, 1);
            }
        });
    }

    onPreviewFile(url: string): void {
        if (this.isOnPreview)
            return;

        this.isOnPreview = true;
        this.dialog.show({
            component: AppDocumentPreviewComponent,
            cssClass: 'transparent',
            componentProps: {
                url: url,
                fileType: 'pdf',
                showAction: true,
                button: {
                    label: 'Marcar como leído',
                    action: () => this.saveAsRead()
                },
                disableRightClick: !this.processControl && !this.processTier?.tiers?.includes(Tier.Owner)
            }
        }).then(() => {
            this.isOnPreview = false
        }).catch(() => this.isOnPreview = false);
    }

    showFindTag(): void {
        if (!this.getShowSaveButton())
            return;

        this.dialog.showWithData<TagDto>({
            component: AppFindTagComponent
        }).then((response) => {
            if (response.data.result) {
                let tag = new TagDto().fromJS(response.data.result);
                let newtag = new DocumentTagDto();
                let exist = this.availableTags.filter(x => x.tagid == tag.id);
                if (!exist.length) {
                    newtag.tagid = tag.id;
                    newtag.name = tag.name;
                    this.availableTags.push(newtag);
                } else {
                    this.notify.error('La etiqueta ya ha se encuentra añadida.', 3000)
                }
            }
        });
    }

    showDownload(): boolean {
        return this.processControl ||
            (this.processTier?.tiers?.includes(Tier.Owner) || this.processTier?.tiers?.includes(Tier.Modifier)) ||
            this.item.documentModifiers.find(x => x.email == this.session?.user?.emailAddress) != null
    }

    showFindEmployee(): void {
        if (!this.getShowSaveButton())
            return;

        this.dialog.showWithData<EmployeeDto>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                selecteds: this.availableNotification.map(x => x.email)
            }
        }).then((response) => {
            if (response.data.result) {
                let employees: UserEmployeeDto[] = [];
                if (Array.isArray(response.data.result)) {
                    for (let item of response.data.result)
                        employees.push(new UserEmployeeDto().fromJS(item));
                }
                for (let item of employees) {
                    let notification = new DocumentNotificationDto();
                    let exist = this.availableNotification.filter(x => x.email == item.employee?.email);
                    if (!exist.length && item.employee?.email) {
                        notification.id = item.employee?.empid;
                        notification.email = item.employee?.email;
                        notification.name = item.employee?.email;
                        this.availableNotification.push(notification);
                    }
                }
            }
        });
    }
    showFindUser(): void {
        if (!this.getShowSaveButton())
            return;

        this.dialog.showWithData<UserDto>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableUser: true,
                selecteds: this.availableUsers.map(x => x.email)
            }
        }).then((response) => {
            if (response.data.result) {
                let employees: UserEmployeeDto[] = [];
                if (Array.isArray(response.data.result)) {
                    for (let item of response.data.result)
                        employees.push(new UserEmployeeDto().fromJS(item));
                }
                for (let item of employees) {
                    let modifier = new DocumentModifierDto();
                    let exist = this.availableUsers.filter(x => x.email == item.employee?.email);
                    if (!exist.length && item.employee?.email) {
                        modifier.id = item.employee?.empid;
                        modifier.name = item.employee?.email;
                        modifier.email = item.employee?.email;
                        this.availableUsers.push(modifier);
                    }
                }
            }
        });
    }
    showFindDocument(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<DocumentDto>({
            component: AppFindDocumentComponent,
            componentProps: { }
        }).then((response) => {
            if (response.data.result) {
                this.documentRel.setValue(response.data.result["code"]);
                this.documentManagement.setValue(response.data.result["documentManagementCode"]);
                this.documentManagement.disable();
                this.documentArea.setValue(response.data.result["documentAreaCode"]);
                this.documentArea.disable();
                const documentprocess = this.processes.filter(x => x.id == response.data.result["documentProcess"]["processid"])[0];
                this.documentMacroProcess.setValue(documentprocess.macroprocessid);
                this.documentMacroProcess.disable();
                this.documentProcess.setValue(response.data.result["documentProcess"]["processid"]);
                this.documentProcess.disable();
                this.documentSubProcess.setValue(response.data.result["documentProcessId"]);
                this.documentSubProcess.disable();
            }
        });
    }

    removeNotification($event: TagModel): void {
        this.availableNotification = this.availableNotification.filter(x => x.email != $event["email"])
        this.documentNotification.setValue(this.availableNotification);
    }

    removeUser($event: TagModel): void {
        this.availableUsers = this.availableUsers.filter(x => x.id != $event["id"])
        this.documentModifier.setValue(this.availableUsers);
    }

    removeTag($event: TagModel): void {
        this.availableTags = this.availableTags.filter(x => x.tagid != $event["tagid"])
        this.documentTag.setValue(this.availableTags);
    }

    clearModifier(): void {
        this.availableUsers.length = 0;
    }

    clearNotification(): void {
        this.availableNotification.length = 0;
    }

    private uploads(newDocument: DocumentDto, callback: () => void) {
        if (newDocument.uploadResources.length) {
            this.dialog.show({
                component: AppFileUploadProgressComponent,
                componentProps: {
                    source: 1,
                    files: newDocument.uploadResources.map(p => p.file),
                    processed: (data: IUploadProgressRespose) => {
                        if (data.completed) {
                            let index: number = 0;

                            for (let token of data.tokens) {
                                newDocument.fileName = token;
                                index++;
                            }

                            callback();
                        } else {
                            this.disabled = false;
                        }
                    }
                }
            });
        } else {
            callback();
        }
    }

    addComparison(comparison: DocumentComparisonDto): void {
        this.comparisonArray.push(comparison);
        if (comparison.documentid && comparison.version > 1) {
            this.documentServiceProxy.get(comparison.documentid).subscribe({
                next: (response) => {
                    let documentComparison: DocumentComparisonDto = new DocumentComparisonDto();
                    documentComparison.documentid = response.documentId;
                    documentComparison.version = response.version;
                    documentComparison.description = response.compareResult
                    documentComparison.date = response.documentStatuses.find(x => x.statusid == Status.Valid)?.createdat
                    this.addComparison(documentComparison)
                },
                error: () => { }
            })
        }
    }

    getShowSaveButton() {
        return !this.onlyview || this.processControl || this.processTier?.tiers?.includes(Tier.Modifier) || this.processTier?.tiers?.includes(Tier.Owner)
            || this.item.documentModifiers.find(x => x.email == this.session?.user?.emailAddress) != null;
    }

    getDisableChips() {
        return this.onlyview && !(this.item.documentModifiers.find(x => x.email == this.session?.user?.emailAddress) != null ||
        this.processControl || this.processTier?.tiers?.includes(Tier.Modifier) || this.processTier?.tiers?.includes(Tier.Owner));
    }

    private nameExist(name: string): Promise<DocumentDto> {
        return new Promise<DocumentDto>((resolve, reject) => {
            this.documentServiceProxy
                .getByName(name)
                .subscribe({
                    next: (response) => {
                        resolve(response);
                    },
                    error: () => reject()
                })
        });
    }
}