import { Component, Injector, OnInit } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { finalize } from 'rxjs';
import { CachingDto, CachingServiceProxy } from '@proxies/caching.proxy';
import { WebLogServiceProxy } from '@proxies/web.logs.proxy';

@Component({
    selector: 'app-maintenance-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class MaintenanceDashboardComponent extends ViewComponent implements OnInit {

    private cachingServiceProxy: CachingServiceProxy;
    private webLogServiceProxy: WebLogServiceProxy;

    caches: CachingDto[];
    webLogs: string[];

    constructor(_injector: Injector) {
        super(_injector);

        this.cachingServiceProxy = _injector.get(CachingServiceProxy);
        this.webLogServiceProxy = _injector.get(WebLogServiceProxy);
    }

    ngOnInit(): void {
        this.cachingServiceProxy.getAll().subscribe({
            next: (caches) => {
                this.caches = caches.items;
            }
        });

        this.refreshLogs();
    }

    clearAllCache() {
        this.message.confirm(`¿Esta seguro de borrar todos los cachés del sistema?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.cachingServiceProxy
                    .clearAll()
                    .pipe(finalize(async () => loading.dismiss()))
                    .subscribe({
                        next: () => this.notify.success(`Se ha borrado exitosamente todos los caches del sistema`, 5000)
                    });
            }
        });
    }

    clearCache(caching: CachingDto) {
        this.message.confirm(`¿Esta seguro de borrar el cache "${caching.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.cachingServiceProxy
                    .clear(caching.name)
                    .pipe(finalize(async () => loading.dismiss()))
                    .subscribe({
                        next: () => this.notify.success(`Se ha borrado exitosamente el cache "${caching.name}"`, 5000)
                    });
            }
        });
    }

    refreshLogs() {
        this.webLogServiceProxy.getAll().subscribe({
            next: (webLogs) => {
                this.webLogs = webLogs.description;
                this.fixWebLogsPanelHeight();
            }
        });
    }

    getRawLogContent(log: string): string {
        return log
            .replace('DEBUG', '')
            .replace('INFO', '')
            .replace('WARN', '')
            .replace('ERROR', '')
            .replace('FATAL', '');
    }

    getLogClass(log: string): string {
        if (log.startsWith('DEBUG'))
            return 'tertiary';
        if (log.startsWith('INFO'))
            return 'secondary';
        if (log.startsWith('WARN'))
            return 'warning';
        if (log.startsWith('ERROR'))
            return 'danger';
        if (log.startsWith('FATAL'))
            return 'danger';

        return '';
    }

    getLogType(log: string): string {
        if (log.startsWith('DEBUG')) 
            return 'DEBUG';        
        if (log.startsWith('INFO')) 
            return 'INFO';        
        if (log.startsWith('WARN')) 
            return 'WARN';        
        if (log.startsWith('ERROR')) 
            return 'ERROR';        
        if (log.startsWith('FATAL')) 
            return 'FATAL';        

        return '';
    }

    fixWebLogsPanelHeight(): void {
        let panel = document.getElementsByClassName('full-height')[0];
        const windowHeight = document.body.clientHeight;
        const panelHeight = panel.clientHeight;
        const difference = windowHeight - panelHeight;
        const fixedHeight = panelHeight + difference;
        (panel as any).style.height = fixedHeight - 400 + 'px';
    }

    exportItems() {

    }
}