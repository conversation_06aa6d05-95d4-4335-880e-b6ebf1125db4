import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { InspectionTypeRoutingModule } from './inspection-type.routing.module';
import { InspectionTypeDashboardComponent } from './dashboard/dashboard.component';
import { InspectionTypeCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    InspectionTypeRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    InspectionTypeDashboardComponent,
    InspectionTypeCreateEditComponent
  ]
})
export class InspectionTypeModule { }
