<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar jerarquía' : 'Crear jerarquía'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div *ngIf="item?.parent" class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="HierarchyParentName" class="form-label">
                                Jerarquía padre
                            </label>

                            <input type="text" class="form-control" id="HierarchyParentName" name="HierarchyParentName"
                                value="{{item?.parent?.name}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="HierarchyName" class="form-label"
                                [ngClass]="{'ng-invalid' : (hierarchyName.invalid && (hierarchyName.touched || hierarchyName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="HierarchyName" name="HierarchyName"
                                formControlName="hierarchyNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="HierarchyEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="HierarchyEnabled" name="HierarchyEnabled"
                                formControlName="hierarchyEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label d-block" for="HierarchyIcon">
                                Ícono (*)
                            </label>
                            <p-dropdown class="d-block" formControlName="hierarchyIconSelect" [options]="icons"
                                styleClass="w-100" [autoDisplayFirst]="false" [showClear]="true" inputId="HierarchyIcon"
                                appendTo="body">
                                <ng-template pTemplate="selectedItem">
                                    <div class="d-flex flex-row align-items-center" *ngIf="hierarchyIcon.value">
                                        <span *ngIf="hierarchyIcon.value !== 'none'"
                                            class="pi pi-{{hierarchyIcon.value}}"></span>
                                        <div class="ps-1">
                                            {{hierarchyIcon.value == 'none' ? 'Sin ícono' : hierarchyIcon.value}}
                                        </div>
                                    </div>
                                </ng-template>
                                <ng-template let-item pTemplate="item">
                                    <div class="d-flex flex-row align-items-center">
                                        <span *ngIf="item !== 'none'" class="pi pi-{{item}}"></span>
                                        <div class="ps-2">
                                            {{item === 'none' ? 'Sin ícono' : item}}
                                        </div>
                                    </div>
                                </ng-template>
                            </p-dropdown>
                        </div>

                        <small>
                            Lista de íconos en: <a aria-label="Icons Url" href="https://primeng.org/icons"
                                target="_blank" rel="noopener noreferrer">
                                PrimeNG Icons
                            </a>
                        </small>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>