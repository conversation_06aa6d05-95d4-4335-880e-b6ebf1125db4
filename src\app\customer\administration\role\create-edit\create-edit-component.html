<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar rol' : 'Crear rol'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">

                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="RoleName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (roleName.invalid && (roleName.touched || roleName.dirty))}">
                                        Nombre (*)
                                    </label>

                                    <input type="text" class="form-control" id="RoleName" name="RoleName"
                                        formControlName="roleNameInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre del rol es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>

                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="RoleIsDefault">
                                        Por defecto (*)
                                    </label>
                                    <select class="form-control" id="RoleIsDefault" name="RoleIsDefault"
                                        formControlName="isDefaultSelect">
                                        <option value="true">
                                            Si
                                        </option>
                                        <option value="false">
                                            No
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="RoleIsStatic">
                                        ¿Estático?
                                    </label>
                                    <select class="form-control" id="RoleIsStatic" name="RoleIsStatic"
                                        formControlName="isStaticSelect">
                                        <option value="true">
                                            Si
                                        </option>
                                        <option value="false">
                                            No
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>
                </p-tabPanel>
                <p-tabPanel header="Permisos">
                    <div class="row">
                        <p-tree [value]="permissionsData" [(selection)]="selectedPermissions" selectionMode="checkbox"
                            [propagateSelectionUp]="false" emptyMessage="No hay permisos registrados">
                            <ng-template let-node pTemplate="default">
                                <b>{{ node.label }}</b>
                            </ng-template>
                        </p-tree>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>