import { Component, Injector, Input, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AlarmAccountDto, AlarmAccountServiceProxy } from '@proxies/alarm-account.proxy';
import { AlarmPartitionDto, AlarmPartitionServiceProxy } from '@proxies/alarm-partition.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { AlarmPartitionCreateEditComponent } from './create-edit/create-edit.component';

@Component({
    selector: 'app-alarm-partition-dashboard',
    templateUrl: 'partition-information.component.html',
    styleUrls: [
        'partition-information.component.scss'
    ]
})
export class AlarmPartitionInformationComponent extends ViewComponent {

    private alarmPartitionServiceProxy: AlarmPartitionServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    @Input() alarmAccount: AlarmAccountDto;

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmPartitionServiceProxy = _injector.get(AlarmPartitionServiceProxy);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: AlarmPartitionCreateEditComponent,
            componentProps: {
                alarmAccount: this.alarmAccount
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: AlarmPartitionDto) {
        this.dialog.showWithData<boolean>({
            component: AlarmPartitionCreateEditComponent,
            componentProps: {
                alarmAccount: this.alarmAccount,
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: AlarmPartitionDto) {
        this.message.confirm(`¿Estas seguro de eliminar la partición de alarma "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.alarmPartitionServiceProxy
                    .delete(this.alarmAccount.id, item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado la partición de alarma satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.alarmPartitionServiceProxy
            .getAll(
                this.alarmAccount.id,
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: AlarmPartitionDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.AlarmPartition.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.AlarmPartition.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}