import { Component, Injector, Input, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IUploadProgressRespose } from '@core/models/app-config';
import { EntityDto, UploadResource } from '@core/utils/core.request';
import { InspectionServiceProxy, InspectionDto, InspectionModifiedType, InspectionModifyDto } from '@proxies/inspection.proxy';
import { DateTime } from 'luxon';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-change-inspection-time',
    templateUrl: 'change-inspection-time.component.html',
    styleUrls: [
        'change-inspection-time.component.scss'
    ]
})
export class InspectionChangeInspectionTimeComponent extends ViewComponent implements OnInit {

    private inspectionServiceProxy: InspectionServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;

    item!: InspectionDto;

    size: number = 15_728_640;
    inspectionDate!: Date;
    inspectionTime!: Date;
    modalForm!: FormGroup;
    disabled!: boolean;
    uploadResources: UploadResource[] = [];

    inspectionModes = {
        none: InspectionModifiedType.None,
        internal: InspectionModifiedType.Internal,
        external: InspectionModifiedType.External,
        propertyUnavailable: InspectionModifiedType.PropertyUnavailable
    };

    get inspectionModifiedType(): AbstractControl {
        return this.modalForm.controls['inspectionModifiedTypeSelect'];
    };

    get inspectionModifiedObservation(): AbstractControl {
        return this.modalForm.controls['inspectionModifiedObservationInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.formBuilder = _injector.get(FormBuilder);
        this.inspectionServiceProxy = _injector.get(InspectionServiceProxy);

        this.modalForm = this.formBuilder.group({
            inspectionModifiedTypeSelect: [this.inspectionModes.none],
            inspectionModifiedObservationInput: ['', Validators.compose([Validators.required, Validators.maxLength(25000)])],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.disabled = false;
                },
                error: () => this.dialog.dismiss()
            });
    }

    onUploadItem(event: UploadResource): void {
        this.uploadResources ??= [];
        this.uploadResources.push(event);
    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.uploadResources ??= [];
                this.uploadResources.splice(index, 1);
            }
        });
    }

    async save(): Promise<void> {
        if (this.inspectionModifiedType.value == this.inspectionModes.none) {
            this.message.info('El motivo de la reprogramación es obligatorio', 'Aviso');
            return;
        }

        const inspectionDate: DateTime = DateTime.fromJSDate(this.inspectionDate);
        const inspectionTime: DateTime = DateTime.fromJSDate(this.inspectionTime);

        if (!inspectionDate.isValid && inspectionTime.isValid) {
            this.message.info('La fecha de reprogramación es inválida', 'Aviso');
            return;
        }

        if (inspectionDate.isValid && !inspectionTime.isValid) {
            this.message.info('La hora de reprogramación es inválida', 'Aviso');
            return;
        }

        let inspectionModified: InspectionModifyDto = new InspectionModifyDto();

        inspectionModified.type = this.inspectionModifiedType.value;
        inspectionModified.inspection = new EntityDto<number>(this.item.id);
        inspectionModified.observation = this.inspectionModifiedObservation.value;
        inspectionModified.uploadResources = this.uploadResources;

        if (inspectionDate.isValid && inspectionTime.isValid) {
            inspectionModified.inspectionTime = DateTime.fromObject({
                year: inspectionDate.year,
                month: inspectionDate.month,
                day: inspectionDate.day,
                hour: inspectionTime.hour,
                minute: inspectionTime.minute,
                second: 0,
                millisecond: 0
            });
        }

        if (this.disabled)
            return;

        this.disabled = true;

        this.uploads(inspectionModified, async () => {
            const loading = await this.loader.show();

            this.inspectionServiceProxy
                .modify(inspectionModified)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss()
                })).subscribe({
                    next: () => {
                        this.notify.success('Inspección reprogramada satisfactoriamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        });
    }

    private uploads(inspectionModified: InspectionModifyDto, callback: () => void) {
        if (inspectionModified.uploadResources && inspectionModified.uploadResources.length > 0) {
            this.dialog.show({
                component: AppFileUploadProgressComponent,
                componentProps: {
                    files: inspectionModified.uploadResources.map(p => p.file),
                    processed: (data: IUploadProgressRespose) => {
                        if (data.completed) {
                            let index: number = 0;

                            for (let token of data.tokens) {
                                inspectionModified.uploadResources[index].token = token;
                                index++;
                            }

                            callback();
                        } else {
                            this.disabled = false;
                        }
                    }
                }
            });
        } else {
            callback();
        }
    }
}