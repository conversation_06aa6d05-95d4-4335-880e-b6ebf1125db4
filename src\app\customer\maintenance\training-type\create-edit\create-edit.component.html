<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar tipo de capacitación' : 'Crear tipo de capacitación'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="TrainingTypeName" class="form-label"
                                [ngClass]="{'ng-invalid' : (trainingTypeName.invalid && (trainingTypeName.touched || trainingTypeName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="TrainingTypeName" name="TrainingTypeName"
                                formControlName="trainingTypeNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="TrainingTypeEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="TrainingTypeEnabled" name="TrainingTypeEnabled"
                                formControlName="trainingTypeEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="TrainingTypeCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="TrainingTypeCode" name="TrainingTypeCode"
                                formControlName="trainingTypeCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>