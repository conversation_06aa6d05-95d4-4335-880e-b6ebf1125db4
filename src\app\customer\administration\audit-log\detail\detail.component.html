<form class="h-100 w-100">
    <app-modal title="Detalle de auditoría">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <ion-label class="d-block fw-bold fz-normal mb-2" color="dark">
                        Información del usuario
                    </ion-label>
                    <hr>
                </div>

                <div class="col-12 mb-4">

                    <ion-label class="d-block" color="dark">
                        <b>Nombre de usuario:</b> {{auditLog.userName}}
                    </ion-label>

                    <ion-label class="d-block" color="dark">
                        <b>Dirección IP:</b> {{auditLog.clientIpAddress}}
                    </ion-label>

                    <ion-label class="d-block" color="dark">
                        <b>Navegador:</b> {{auditLog.browserInfo}}
                    </ion-label>

                </div>

                <div class="col-12">
                    <ion-label class="d-block fw-bold fz-normal mb-2" color="dark">
                        Información de acciones
                    </ion-label>
                    <hr>
                </div>

                <div class="col-12 mb-4">

                    <ion-label class="d-block" color="dark">
                        <b>Servicio:</b> {{auditLog.userName}}
                    </ion-label>

                    <ion-label class="d-block" color="dark">
                        <b>Acción:</b> {{auditLog.clientIpAddress}}
                    </ion-label>

                    <ion-label class="d-block" color="dark">
                        <b>Fecha de ejecución:</b> {{auditLog.executionTime | luxonFromNow}} ({{auditLog.executionTime |
                        luxonFormat: 'yyyy-MM-dd HH:mm:ss'}})
                    </ion-label>

                    <ion-label class="d-block" color="dark">
                        <b>Duración:</b> {{auditLog.executionDuration}} ms
                    </ion-label>

                </div>

                <div class="col-12">
                    <ion-label class="d-block fw-bold fz-normal mb-2" color="dark">
                        Información de párametros
                    </ion-label>
                    <hr>
                </div>

                <div class="col-12">
                    <ion-label class="d-block" color="dark">
                        <pre class="audit-log-code" lang="js">{{auditLog.parameters}}</pre>
                    </ion-label>
                </div>

                <div class="col-12">
                    <ion-label class="d-block fw-bold fz-normal mb-2" color="dark">
                        Información de excepciones
                    </ion-label>
                    <hr>
                </div>

                <div *ngIf="!auditLog.exception" class="col-12">
                    <ion-label class="d-flex align-items-center" color="dark">
                        <ion-icon class="me-1" name="checkmark-circle" color="success" size="normal"></ion-icon>
                        Completado
                    </ion-label>
                </div>

                <div *ngIf="auditLog.exception" class="col-12">
                    <ion-label class="d-block" color="dark">
                        <pre class="audit-log-code" lang="js">{{auditLog.exception}}</pre>
                    </ion-label>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [showSaveButton]="false" />
    </app-modal>
</form>