import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { RoleDto, RolePermissionDto, RoleServiceProxy } from '@proxies/role.proxy';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { TreeNode } from 'primeng/api';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-create-edit-role',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class RoleCreateEditComponent extends ViewComponent implements OnInit {

    private roleServiceProxy: RoleServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: RoleDto = new RoleDto();
    rolePermissions: RolePermissionDto[];
    grantedPermissionNames: string[] = [];

    permissionsData: TreeNode<RolePermissionDto>[];
    selectedPermissions: any;

    segment: 'general' | 'permissions' = 'general';
    reference = {
        isDefault: 'false',
        isStatic: 'false'
    }

    disabled: boolean = false;

    modalForm!: FormGroup;

    get roleName(): AbstractControl {
        return this.modalForm.controls['roleNameInput'];
    };

    get isDefault(): AbstractControl {
        return this.modalForm.controls['isDefaultSelect'];
    };

    get isStatic(): AbstractControl {
        return this.modalForm.controls['isStaticSelect'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.roleServiceProxy = _injector.get(RoleServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            roleNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
            isDefaultSelect: ['false', [Validators.required]],
            isStaticSelect: [{ value: 'false', disabled: true }, [Validators.required]]
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();

        this.roleServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    if (response.role) {
                        this.item = response.role;
                        this.grantedPermissionNames = response.grantedPermissionNames;

                        this.reference.isDefault = (this.item.isDefault ? 'true' : 'false');
                        this.reference.isStatic = (this.item.isStatic ? 'true' : 'false');

                        this.roleName.setValue(this.item.name);
                        this.isDefault.setValue(this.reference.isDefault);
                        this.isStatic.setValue(this.reference.isStatic);
                    }

                    this.rolePermissions = response.permissions;
                    this.createPermissionsData();
                    this.setSelectedNodes();
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {

        this.item.name = this.roleName.value;
        this.reference.isDefault = this.isDefault.value;
        this.reference.isStatic = this.isStatic.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre del rol es obligatorio', 'Aviso');
            return;
        }

        this.item.grantedPermissionNames = this.selectedPermissions.filter(p => p.data).map(p => p.data.name);
        this.item.isDefault = (this.reference.isDefault == 'true');

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.roleServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Rol actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.roleServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Rol creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private createPermissionsData() {
        this.permissionsData = this.createTree(
            this.rolePermissions,
            'parentName',
            'name',
            undefined,
            'children',
            [
                {
                    target: 'label',
                    source: 'displayName',
                },
                {
                    target: 'expandedIcon',
                    value: 'fa fa-folder-open text-warning',
                },
                {
                    target: 'collapsedIcon',
                    value: 'fa fa-folder text-warning',
                },
                {
                    target: 'expanded',
                    value: true,
                },
            ]
        );
    }

    private createTree(array: RolePermissionDto[], parentIdProperty: string, idProperty: string, parentIdValue: string, childrenProperty: string, fieldMappings: { target: string, source?: string, value?: string | boolean }[]): any {
        let tree: TreeNode[] = [];
        let nodes = array.filter(p => p[parentIdProperty] == parentIdValue);

        for (let node of nodes) {
            let newNode = <TreeNode<RolePermissionDto>>{
                data: node
            };

            for (let fieldMapping of fieldMappings) {
                if (!fieldMapping['target']) {
                    return;
                }

                if (fieldMapping.hasOwnProperty('value')) {
                    newNode[fieldMapping['target']] = fieldMapping['value'];
                } else if (fieldMapping['source']) {
                    newNode[fieldMapping['target']] = node[fieldMapping['source']];
                } else if (fieldMapping['targetFunction']) {
                    newNode[fieldMapping['target']] = fieldMapping['targetFunction'](node);
                }
            }

            newNode[childrenProperty] = this.createTree(
                array,
                parentIdProperty,
                idProperty,
                node[idProperty],
                childrenProperty,
                fieldMappings
            );

            tree.push(newNode);
        }

        return tree;
    }

    private setSelectedNodes() {
        this.selectedPermissions = [];
        for (let permission of this.grantedPermissionNames) {
            let item = this.findNode(this.permissionsData, permission);
            if (item) {
                this.selectedPermissions.push(item);
            }
        }
    }

    private findNode(data: TreeNode<RolePermissionDto>[], selector: string): any {
        let nodes = data.filter(p => p.data.name == selector);
        if (nodes && nodes.length === 1) {
            return nodes[0];
        }

        let foundNode = undefined;

        for (let item of data) {
            if (!foundNode) {
                foundNode = this.findNode(item.children, selector);
            }
        }

        return foundNode;
    }
}