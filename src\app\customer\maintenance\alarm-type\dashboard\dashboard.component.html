<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Tipos de alarma
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button (click)="export()" class="ion-option me-2" color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Maintenance.AlarmType.Modify' | permission" (click)="createItem()"
                class="ion-option" color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar tipo de alarma
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Maintenance.AlarmType.Modify',
                                        'Pages.Maintenance.AlarmType.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th style="min-width: 200px" pSortableColumn="Name">
                                    Nombre
                                    <p-sortIcon field="Name"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px;"
                                    pSortableColumn="HasProcess">
                                    ¿Tiene tratamiento?
                                    <p-sortIcon field="HasProcess"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px;" pSortableColumn="HasReal">
                                    ¿Mostrar Real Si/No?
                                    <p-sortIcon field="HasReal"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px;"
                                    pSortableColumn="HasReason">
                                    ¿Mostrar motivo?
                                    <p-sortIcon field="HasReason"></p-sortIcon>
                                </th>
                                <th style="min-width: 220px; max-width: 220px; width: 220px;"
                                    pSortableColumn="HasDescription">
                                    ¿Mostrar descripción?
                                    <p-sortIcon field="HasDescription"></p-sortIcon>
                                </th>
                                <th style="min-width: 280px; max-width: 280px; width: 280px;"
                                    pSortableColumn="HasServiceCall">
                                    ¿Genera llamada de servicio?
                                    <p-sortIcon field="HasServiceCall"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px;">
                                    Color/Fondo/ícono
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    Sonido
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px"
                                    pSortableColumn="CloseCode">
                                    Código de cierre
                                    <p-sortIcon field="CloseCode"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px"
                                    pSortableColumn="Code">
                                    Código de alarma
                                    <p-sortIcon field="Code"></p-sortIcon>
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Maintenance.AlarmType.Modify',
                                        'Pages.Maintenance.AlarmType.Delete'
                                    ] | permissionAny
                                )
                                ">
                                </th>
                                <th style="min-width: 200px">
                                    <p-columnFilter type="text" field="name"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    <p-columnFilter field="hasProcess" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="hasProcessArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">
                                                        {{option.label}}
                                                    </span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    <p-columnFilter field="hasReal" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="hasRealArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">
                                                        {{option.label}}
                                                    </span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    <p-columnFilter field="hasReason" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="hasReasonArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">
                                                        {{option.label}}
                                                    </span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 220px; max-width: 220px; width: 220px">
                                    <p-columnFilter field="hasDescription" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="hasDescriptionArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">
                                                        {{option.label}}
                                                    </span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 280px; max-width: 280px; width: 280px">
                                    <p-columnFilter field="hasServiceCall" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="hasServiceCallArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">
                                                        {{option.label}}
                                                    </span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    <p-columnFilter type="text" field="closeCode"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    <p-columnFilter type="text" field="code"></p-columnFilter>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px"
                                    [hidden]="
                                !(
                                    [
                                        'Pages.Maintenance.AlarmType.Modify',
                                        'Pages.Maintenance.AlarmType.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 200px">
                                    {{record.name}}
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    <ion-badge color="tertiary" *ngIf="record.hasProcess">
                                        Si
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record.hasProcess">
                                        No
                                    </ion-badge>
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    <ng-container *ngIf="record.hasProcess">
                                        <ion-badge color="tertiary" *ngIf="record.hasReal">
                                            Si
                                        </ion-badge>
                                        <ion-badge color="danger" *ngIf="!record.hasReal">
                                            No
                                        </ion-badge>
                                    </ng-container>
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    <ng-container *ngIf="record.hasProcess && record.hasReal">
                                        <ion-badge color="tertiary" *ngIf="record.hasReason">
                                            Si
                                        </ion-badge>
                                        <ion-badge color="danger" *ngIf="!record.hasReason">
                                            No
                                        </ion-badge>
                                    </ng-container>
                                </td>
                                <td class="text-center" style="min-width: 220px; max-width: 220px; width: 220px">
                                    <ng-container *ngIf="record.hasProcess">
                                        <ion-badge color="tertiary" *ngIf="record.hasDescription">
                                            Si
                                        </ion-badge>
                                        <ion-badge color="danger" *ngIf="!record.hasDescription">
                                            No
                                        </ion-badge>
                                    </ng-container>
                                </td>
                                <td class="text-center" style="min-width: 280px; max-width: 280px; width: 280px">
                                    <ion-badge color="tertiary" *ngIf="record.hasServiceCall">
                                        Si
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record.hasServiceCall">
                                        No
                                    </ion-badge>
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    <ng-container *ngIf="record.hasProcess">
                                        <div class="alarm-type-tag" [style.background]="record.background"
                                            [style.color]="record.color">
                                            <span>{{record.name}}</span> <img *ngIf="record.icon" aria-label="Icon"
                                                src="/assets/icons/{{record.icon}}">
                                        </div>
                                    </ng-container>
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.sound}}
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record.closeCode}}
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record.code}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>