<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal title="Gestionar empleado" [disabled]="disabled">
        <app-modal-body>
            <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true">
                <p-tabPanel header="Información general">
                    <div class="row">
                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeName">
                                    Nombres
                                </label>
                                <input type="text" id="EmployeeName" name="EmployeeName" class="form-control"
                                    value="{{item?.employee?.firstName}} {{item?.employee?.middleName}}" readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeSurname">
                                    Apellidos
                                </label>
                                <input type="text" id="EmployeeSurname" name="EmployeeSurname" class="form-control"
                                    value="{{item?.employee?.lastName}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeId">
                                    Nº Empleado
                                </label>
                                <input type="text" id="EmployeeId" name="EmployeeId" class="form-control"
                                    value="{{item?.employee?.empid}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeExternalId">
                                    Nº Empleado Externo
                                </label>
                                <input type="text" id="EmployeeExternalId" name="EmployeeExternalId"
                                    class="form-control" value="{{item?.employee?.extempno}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeActive">
                                    Estado
                                </label>
                                <input type="text" id="EmployeeActive" name="EmployeeActive" class="form-control"
                                    value="{{item?.employee ? (item?.employee?.active == 'Y' ? 'Activo' : 'Inactivo') : ''}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeFunction">
                                    Denominación de función
                                </label>
                                <input type="text" id="EmployeeFunction" name="EmployeeFunction" class="form-control"
                                    value="{{item?.employee?.jobTitle}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeePosition">
                                    Posición
                                </label>
                                <input type="text" id="EmployeePosition" name="EmployeePosition" class="form-control"
                                    value="{{item?.employee?.positionsQuery?.name}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeDepartment">
                                    Departamento
                                </label>
                                <input type="text" id="EmployeeDepartment" name="EmployeeDepartment"
                                    class="form-control" value="{{item?.employee?.departmentsQuery?.name}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeOfficePhoneNumber">
                                    Teléfono de oficina
                                </label>
                                <input type="text" id="EmployeeOfficePhoneNumber" name="EmployeeOfficePhoneNumber"
                                    class="form-control" value="{{item?.employee?.officeTel}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeExtension">
                                    Extensión
                                </label>
                                <input type="text" id="EmployeeExtension" name="EmployeeExtension" class="form-control"
                                    value="{{item?.employee?.officeExt}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeBranchOffice">
                                    Sucursal
                                </label>
                                <input type="text" id="EmployeeBranchOffice" name="EmployeeBranchOffice"
                                    class="form-control" value="{{item?.employee?.branchesQuery?.name}}" readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeePhoneNumber">
                                    Teléfono móvil
                                </label>
                                <input type="text" id="EmployeePhoneNumber" name="EmployeePhoneNumber"
                                    class="form-control" value="{{item?.employee?.mobile}}" readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeDirector">
                                    Gerente
                                </label>
                                <input type="text" id="EmployeeDirector" name="EmployeeDirector" class="form-control"
                                    value="{{item?.employee?.manager}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeePayment">
                                    Pagar
                                </label>
                                <input type="text" id="EmployeePayment" name="EmployeePayment" class="form-control"
                                    value="{{item?.employee?.pager}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeUserCode">
                                    Código de usuario
                                </label>
                                <input type="text" id="EmployeeUserCode" name="EmployeeUserCode" class="form-control"
                                    value="{{item?.employee?.usersQuery?.user_code}}" readonly>
                            </div>
                        </div>

                        <div class="col-4 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeHomePhoneNumber">
                                    Teléfono de casa
                                </label>
                                <input type="text" id="EmployeeHomePhoneNumber" name="EmployeeHomePhoneNumber"
                                    class="form-control" value="{{item?.employee?.homeTel}}" readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeJobDepartment">
                                    Empleado del departamento
                                </label>
                                <input type="text" id="EmployeeJobDepartment" name="EmployeeJobDepartment"
                                    class="form-control" value="{{item?.employee?.salesEmployee?.slpName}}" readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeFax">
                                    Fax
                                </label>
                                <input type="text" id="EmployeeFax" name="EmployeeFax" class="form-control" value="{{item?.employee?.fax}}"
                                    readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeCostCenter">
                                    Centro de costo
                                </label>
                                <input type="text" id="EmployeeCostCenter" name="EmployeeCostCenter"
                                    class="form-control" value="{{item?.employee?.costCenter}}" readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeEmailAddress">
                                    Correo electrónico
                                </label>
                                <input type="text" id="EmployeeEmailAddress" name="EmployeeEmailAddress"
                                    class="form-control" value="{{item?.employee?.email}}" readonly>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeLinkedProvider">
                                    Proveedor vinculado
                                </label>
                                <input type="text" id="EmployeeLinkedProvider" name="EmployeeLinkedProvider"
                                    class="form-control" value="{{item?.employee?.bplink}}" readonly>
                            </div>
                        </div>

                    </div>
                </p-tabPanel>
                <p-tabPanel header="Permisos">
                    <div class="row">

                        <div class="col-12 mb-2">
                            <label class="fz-normal fw-bold text-dark mb-0">
                                Configuración
                            </label>
                            <hr>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeEnabled">
                                    ¿Activo en operaciones?
                                </label>
                                <select id="EmployeeEnabled" name="EmployeeEnabled" class="form-control" formControlName="operationEnabledSelect">
                                    <option value="false">
                                        Inactivo
                                    </option>
                                    <option value="true">
                                        Activo
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="col-6 mb-4">
                            <div class="form-group">
                                <label class="form-label" for="EmployeeArea">
                                    Área
                                </label>
                                <select (change)="onAreaChange($event)" id="EmployeeArea" name="EmployeeArea" class="form-control" formControlName="operationAreaSelect">
                                    <option value="-1">
                                        Seleccione
                                    </option>
                                    <option *ngFor="let area of areas" [value]="area.prcCode">
                                        {{area.prcName}}
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="col-12 mb-2">
                            <label class="fz-normal fw-bold text-dark mb-0">
                                Centros
                            </label>
                        </div>

                        <div class="col-12">
                            <p-table #dataTable sortMode="multiple" [value]="centers" [rows]="100" ScrollWidth="100%"
                                scrollDirection="horizontal">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 250px;">
                                            Centro logístico
                                        </th>
                                        <th style="min-width: 250px; width: 250px; max-width: 250px;">
                                            ¿Visualizar todos los usuarios?
                                        </th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                                    <ng-container *ngIf="!record.remove">
                                        <tr>
                                            <td style="min-width: 250px;">
                                                {{record.data.name}}
                                            </td>
                                            <td class="text-center" style="min-width: 250px; width: 250px; max-width: 250px;">
                                                <ion-toggle (ionChange)="onCenterChange($event, rowIndex)" [checked]="record.checked" mode="ios"></ion-toggle>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </ng-template>
                            </p-table>
                            <div class="primeng-no-data" *ngIf="centers?.length == 0">
                                No hay data
                            </div>
                        </div>

                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>