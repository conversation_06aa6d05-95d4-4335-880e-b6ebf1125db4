<ion-header mode="md">
    <ion-toolbar>

        <ion-title class="fz-normal fw-bold">
            Alarmas
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="export()" class="ion-option me-2" color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button (click)="getData()" class="ion-option me-2" color="tertiary" fill="solid">
                <ion-icon name="search"></ion-icon>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <div class="row">

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-3 col-xxl-2 mb-3">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label" for="AlarmType">
                                Tipo de alarma
                            </label>
                            <select [(ngModel)]="alarmType" class="form-control" id="AlarmType" name="AlarmType">
                                <option [value]="-1">
                                    Todos
                                </option>
                                <option *ngFor="let alarmType of alarmTypes" [value]="alarmType.value">
                                    {{alarmType.label}}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-3 col-xxl-2 mb-3">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label" for="AlarmStatus">
                                Estado de alarma
                            </label>
                            <select [(ngModel)]="alarmStatus" class="form-control" id="AlarmStatus" name="AlarmStatus">
                                <option [value]="alarmStatuses.none">
                                    Todos
                                </option>
                                <option [value]="alarmStatuses.pending">
                                    Pendiente de atención
                                </option>
                                <option [value]="alarmStatuses.process">
                                    En curso
                                </option>
                                <option [value]="alarmStatuses.completed">
                                    Atendido
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-3 col-xxl-3 mb-3">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label" for="AlarmCenter">
                                Centro
                            </label>
                            <select [(ngModel)]="center" class="form-control" id="AlarmCenter" name="AlarmCenter">
                                <option value="-1">
                                    Todos
                                </option>
                                <option *ngFor="let center of centers" [value]="center.value">
                                    {{center.label}}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-3 col-xxl-2 mb-3">
                    <app-date-input name="AlarmStartDate" label="Fecha inicio" [(value)]="startDate"
                        (onDateChange)="onStartDateChange($event)" />
                </div>

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-3 col-xxl-2 mb-3">
                    <app-date-input name="AlarmEndDate" label="Fecha fin" [(value)]="endDate"
                        (onDateChange)="onEndDateChange($event)" />
                </div>

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-4 col-xxl-4 mb-3">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label" for="AlarmCustomer">
                                Cliente
                            </label>
                            <div class="input-group-close">

                                <div class="input-group action">
                                    <input (click)="showFindUser()" class="form-control rounded" id="AlarmCustomer"
                                        name="AlarmCustomer" value="{{customerLabel}}" readonly />
                                    <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                </div>

                                <button (click)="clearCustomer()" *ngIf="customer" class="input-group-close-action">
                                    <ion-icon class="input-group-close-action-icon" name="close"></ion-icon>
                                </button>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-4 col-xxl-4 mb-3">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label" for="AlarmProperty">
                                Bodega
                            </label>
                            <div class="input-group-close">
                                <div class="input-group action">
                                    <input (click)="showFindProperty()" class="form-control rounded" id="AlarmProperty"
                                        name="AlarmProperty" value="{{propertyLabel}}" readonly />
                                    <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                </div>
                                <button (click)="clearProperty()" *ngIf="property" class="input-group-close-action">
                                    <ion-icon class="input-group-close-action-icon" name="close"></ion-icon>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 mt-2">
                    <p-table #dataTable sortMode="multiple" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns"
                        (onSort)="getData()">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px">
                                    Acciones
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Centro logístico
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Bodega
                                </th>
                                <th style="min-width: 250px; max-width: 250px; width: 250px">
                                    Cliente
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Usuario Alarma
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Alarma
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Evento
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Zona
                                </th>
                                <th style="min-width: 180px; width: 180px; max-width: 180px;">
                                    Estado de atención
                                </th>
                                <th style="min-width: 180px; width: 180px; max-width: 180px;">
                                    Estado real
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    Fecha y hora
                                </th>
                                <th style="min-width: 300px;">
                                    Detalle
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px">
                                    <ion-button *ngIf="record.alarmId" (click)="showActions($event, record)"
                                        class="ion-action" color="primary" fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.center ? record.center.name : ''}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.property ? record.property.name : ''}}
                                </td>
                                <td style="min-width: 250px; max-width: 250px; width: 250px">
                                    {{record.customer ? record.customer.document : ''}} - {{record.customer ?
                                    record.customer.name : ''}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record.alarmUser ? record.alarmUser.name || record.alarmUser.code : ''}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record.alarmName}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record.alarmType ? record.alarmType.name : ''}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record.alarmZone ? record.alarmZone.name || record.alarmZone.code : ''}}
                                </td>
                                <td class="text-center" style="min-width: 180px; max-width: 180px; width: 180px">
                                    <ion-badge color="danger" *ngIf="record.status == alarmStatuses.pending">
                                        Pendiente de atención
                                    </ion-badge>
                                    <ion-badge color="warning" *ngIf="record.status == alarmStatuses.process">
                                        En curso
                                    </ion-badge>
                                    <ion-badge color="success" *ngIf="record.status == alarmStatuses.completed">
                                        Atendido
                                    </ion-badge>
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ng-container *ngIf="record.status != alarmStatuses.none">
                                        <ion-badge color="success" *ngIf="record.canceled">
                                            Finalizada
                                        </ion-badge>
                                        <ion-badge color="danger" *ngIf="!record.canceled">
                                            En curso
                                        </ion-badge>
                                    </ng-container>
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.alarmTime | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                                <td style="min-width: 300px;">
                                    {{record.description}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </div>
            </div>
        </ion-card-content>
    </ion-card>
</ion-content>