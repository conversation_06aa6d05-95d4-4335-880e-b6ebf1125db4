import { Component, EventEmitter, Input, Output, ViewChild, ElementRef, AfterViewInit } from '@angular/core';

@Component({
    selector: 'app-training-video-player',
    templateUrl: 'video-player.component.html',
    styleUrls: ['video-player.component.scss']
})
export class TrainingVideoPlayerComponent implements AfterViewInit {

    @Input() video: any;
    @Output() onVideoEnded = new EventEmitter<any>();

    @ViewChild('videoElement', { static: true }) videoElement: ElementRef<HTMLVideoElement>;

    ngAfterViewInit(): void {
        if (this.videoElement && this.videoElement.nativeElement) {
            this.videoElement.nativeElement.addEventListener('ended', () => {
                this.onVideoEnded.emit(this.video);
            });

            // Agregar evento de error para debugging
            this.videoElement.nativeElement.addEventListener('error', (e) => {
                console.error('Error loading video:', e);
                console.error('Video source:', this.video.path);
            });

            // Agregar evento de carga para debugging
            this.videoElement.nativeElement.addEventListener('loadeddata', () => {
                console.log('Video loaded successfully:', this.video.name);
            });
        }
    }

    onVideoError(event: any): void {
        console.error('Video error event:', event);
        console.error('Video details:', this.video);
    }

    onVideoLoad(): void {
        console.log('Video loaded:', this.video.name);
    }
}
