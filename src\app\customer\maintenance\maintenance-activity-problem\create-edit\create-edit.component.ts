import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { MaintenanceActivityProblemDto, MaintenanceActivityProblemServiceProxy } from '@proxies/maintenance-activity-problem.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-activity-problem-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MaintenanceActivityProblemCreateEditComponent extends ViewComponent implements OnInit {

    private maintenanceActivityProblemServiceProxy: MaintenanceActivityProblemServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: MaintenanceActivityProblemDto = new MaintenanceActivityProblemDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get maintenanceActivityProblemName(): AbstractControl {
        return this.modalForm.controls['maintenanceActivityProblemNameInput'];
    };

    get maintenanceActivityProblemEnabled(): AbstractControl {
        return this.modalForm.controls['maintenanceActivityProblemEnabledSelect'];
    };

    get maintenanceActivityProblemCode(): AbstractControl {
        return this.modalForm.controls['maintenanceActivityProblemCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceActivityProblemServiceProxy = _injector.get(MaintenanceActivityProblemServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            maintenanceActivityProblemNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            maintenanceActivityProblemEnabledSelect: ['true', [Validators.required]],
            maintenanceActivityProblemCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.maintenanceActivityProblemServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.maintenanceActivityProblemName.setValue(this.item.name);
                        this.maintenanceActivityProblemEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.maintenanceActivityProblemCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new MaintenanceActivityProblemDto().fromJS({});
        }
    }

    async save(): Promise<void> {

        this.item.name = this.maintenanceActivityProblemName.value;
        this.item.enabled = this.maintenanceActivityProblemEnabled.value == 'true';
        this.item.code = this.maintenanceActivityProblemCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.maintenanceActivityProblemServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.maintenanceActivityProblemServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}