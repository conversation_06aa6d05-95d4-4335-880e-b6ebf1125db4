import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { BasicProtectionEquipmentDto, BasicProtectionEquipmentServiceProxy } from '@proxies/basic-protection-equipment.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-basic-protection-equipment-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class BasicProtectionEquipmentCreateEditComponent extends ViewComponent implements OnInit {

    private basicProtectionEquipmentServiceProxy: BasicProtectionEquipmentServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: BasicProtectionEquipmentDto = new BasicProtectionEquipmentDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get basicProtectionEquipmentName(): AbstractControl {
        return this.modalForm.controls['basicProtectionEquipmentNameInput'];
    };

    get basicProtectionEquipmentEnabled(): AbstractControl {
        return this.modalForm.controls['basicProtectionEquipmentEnabledSelect'];
    };

    get basicProtectionEquipmentCode(): AbstractControl {
        return this.modalForm.controls['basicProtectionEquipmentCodeInput'];
    };
    
    constructor(_injector: Injector) {
        super(_injector);

        this.basicProtectionEquipmentServiceProxy = _injector.get(BasicProtectionEquipmentServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            basicProtectionEquipmentNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            basicProtectionEquipmentEnabledSelect: ['true', [Validators.required]],
            basicProtectionEquipmentCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.basicProtectionEquipmentServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.basicProtectionEquipmentName.setValue(this.item.name);
                        this.basicProtectionEquipmentEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.basicProtectionEquipmentCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new BasicProtectionEquipmentDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.basicProtectionEquipmentName.value;
        this.item.enabled = this.basicProtectionEquipmentEnabled.value == 'true';
        this.item.code = this.basicProtectionEquipmentCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de equipo de protección personal básico es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.basicProtectionEquipmentServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Equipo de protección personal básico actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.basicProtectionEquipmentServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Equipo de protección personal básico creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}