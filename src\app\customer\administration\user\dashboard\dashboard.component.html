<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Usuarios
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button (click)="export()" class="ion-option me-2" color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Administration.User.Modify' | permission" (click)="createItem()" class="ion-option" color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar usuario
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Administration.User.Modify',
                                        'Pages.Administration.User.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th style="min-width: 180px" pSortableColumn="DocumentType.Name">
                                    Tipo documento
                                    <p-sortIcon field="DocumentType.Name"></p-sortIcon>
                                </th>
                                <th style="min-width: 180px" pSortableColumn="Document">
                                    Nº documento
                                    <p-sortIcon field="Document"></p-sortIcon>
                                </th>
                                <th style="min-width: 220px" pSortableColumn="Username">
                                    Nombre de usuario
                                    <p-sortIcon field="Username"></p-sortIcon>
                                </th>
                                <th style="min-width: 150px" pSortableColumn="Name">
                                    Nombres
                                    <p-sortIcon field="Name"></p-sortIcon>
                                </th>
                                <th style="min-width: 220px" pSortableColumn="Surname">
                                    Apellidos Paterno
                                    <p-sortIcon field="Surname"></p-sortIcon>
                                </th>
                                <th style="min-width: 220px" pSortableColumn="SecondSurname">
                                    Apellidos Materno
                                    <p-sortIcon field="SecondSurname"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px" pSortableColumn="EmailAddress">
                                    Correo electrónico
                                    <p-sortIcon field="EmailAddress"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="IsEmailConfirmed">
                                    Verificado
                                    <p-sortIcon field="IsEmailConfirmed"></p-sortIcon>
                                </th>
                                <th style="min-width: 150px" pSortableColumn="PhoneNumber">
                                    Celular
                                    <p-sortIcon field="PhoneNumber"></p-sortIcon>
                                </th>
                                <th style="min-width: 150px">
                                    Roles
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="IsActive">
                                    Activo
                                    <p-sortIcon field="IsActive"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px" pSortableColumn="Job">
                                    Cargo
                                    <p-sortIcon field="Job"></p-sortIcon>
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px"  [hidden]="
                                !(
                                    [
                                        'Pages.Administration.User.Modify',
                                        'Pages.Administration.User.Delete'
                                    ] | permissionAny
                                )
                                ">
                                </th>
                                <th style="min-width: 150px">
                                    <p-columnFilter field="documentType" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="documentTypes"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 220px">
                                    <p-columnFilter type="text" field="document"></p-columnFilter>
                                </th>
                                <th style="min-width: 220px">
                                    <p-columnFilter type="text" field="username"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px">
                                    <p-columnFilter type="text" field="names"></p-columnFilter>
                                </th>
                                <th style="min-width: 220px">
                                    <p-columnFilter type="text" field="surname"></p-columnFilter>
                                </th>
                                <th style="min-width: 220px">
                                    <p-columnFilter type="text" field="secondSurname"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px">
                                    <p-columnFilter type="text" field="emailAddress"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter field="isEmailConfirmed" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="verifiedStates"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px">
                                    <p-columnFilter type="text" field="phoneNumber"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px">
                                    <p-columnFilter field="role" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="roles"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter field="isActive" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="activeStates"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px">
                                    <p-columnFilter type="text" field="job"></p-columnFilter>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px"
                                    [hidden]="
                                    !(
                                        [
                                            'Pages.Administration.User.Modify',
                                            'Pages.Administration.User.Delete'
                                        ] | permissionAny
                                    )
                                    ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 180px">
                                    {{record.user ? record.user.documentType?.name : ''}}
                                </td>
                                <td style="min-width: 180px">
                                    {{record.user ? record.user.document : ''}}
                                </td>
                                <td style="min-width: 220px">
                                    {{record.user ? record.user.userName : ''}}
                                </td>
                                <td style="min-width: 150px">
                                    {{record.user ? record.user.name : ''}}
                                </td>
                                <td style="min-width: 220px">
                                    {{record.user ? record.user.surname : ''}}
                                </td>
                                <td style="min-width: 220px">
                                    {{record.user ? record.user.secondSurname : ''}}
                                </td>
                                <td style="min-width: 200px">
                                    {{record.user ? record.user.emailAddress : ''}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ion-badge color="success" *ngIf="record.user?.isEmailConfirmed">
                                        Si
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record.user?.isEmailConfirmed">
                                        No
                                    </ion-badge>
                                </td>
                                <td style="min-width: 150px">
                                    {{record.user ? record.user.phoneNumber : ''}}
                                </td>
                                <td style="min-width: 150px">
                                    {{record.roleNameText}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ion-badge color="success" *ngIf="record.user?.isActive">
                                        Si
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record.user?.isActive">
                                        No
                                    </ion-badge>
                                </td>
                                <td style="min-width: 200px">
                                    {{record.user ? record.user.job : ''}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>