import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { OperationTaskRoutingModule } from './operation-task.routing.module';
import { OperationTaskDashboardComponent } from './dashboard/dashboard.component';
import { ComponentModule } from '@components/component.module';
import { OperationTaskCreateEditComponent } from './create-edit/create-edit.component';
import { OperationTaskGeneralInformationComponent } from './create-edit/general-information/general-information.component';
import { OperationTaskPersonInformationComponent } from './create-edit/person-information/person-information.component';

@NgModule({
    imports: [
        OperationTaskRoutingModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        IonicModule,
        ThemeModule,
        CoreModule,
        SharedModule,
        ComponentModule
    ],
    declarations: [
        OperationTaskDashboardComponent,
        OperationTaskCreateEditComponent,
        OperationTaskGeneralInformationComponent,
        OperationTaskPersonInformationComponent
    ]
})
export class OperationTaskModule { }