<div class="row">

    <div class="col-12 mb-3">
        <ion-toolbar>
            <ion-title class="fz-normal fw-bold">
                Requisitos
            </ion-title>
            <ion-buttons slot="end">
                <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                    <ion-icon name="refresh"></ion-icon>
                </ion-button>
                <ion-button (click)="createItem()" class="ion-option" color="primary" fill="solid">
                    <ion-icon name="add"></ion-icon>
                    <ion-label class="fz-small d-none d-lg-inline-block">
                        Agregar Requisito
                    </ion-label>
                </ion-button>
            </ion-buttons>
        </ion-toolbar>
    </div>

    <div class="col-12">
        <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
            [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
            ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width: 100px; max-width: 100px; width: 100px">
                        Acciones
                    </th>
                    <th style="min-width: 20px">
                        ID
                    </th>
                    <th style="min-width: 200px">
                        Nombre
                    </th>
                    <th style="min-width: 100px">
                        Fecha de Creación
                    </th>
                </tr>
                <tr>
                    <th style="min-width: 80px; max-width: 80px; width: 80px">
                    </th>
                    <th style="min-width: 20px; width: 20px; max-width: 20px;">
                    </th>
                    <th style="min-width: 100px; width: 100px; max-width: 100px;">
                        <p-columnFilter type="text" field="Name"></p-columnFilter>
                    </th>
                    <th style="min-width: 100px; width: 100px; max-width: 100px;">
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr>
                    <td class="action-column" style="min-width: 80px; max-width: 80px; width: 80px">
                        <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                            fill="solid" size="small">
                            <ion-label class="fz-small">
                                Acciones
                            </ion-label>
                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                        </ion-button>
                    </td>
                    <td>
                        <span class="p-column-title">
                            ID
                        </span> {{record?.id}}
                    </td>
                    <td style="min-width: 100px">
                        <span class="p-column-title">
                            Name
                        </span> {{record?.name?.toUpperCase()}}
                    </td>
                    <td>
                        <span class="p-column-title">
                            CreatedAt
                        </span> {{ record?.dates }}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
        <div class="primeng-paging-container">
            <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator (onPageChange)="getData($event)"
                [totalRecords]="table.totalRecordsCount" [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                [showCurrentPageReport]="true" dropdownAppendTo="body"
                currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
            </p-paginator>
        </div>
    </div>
</div>