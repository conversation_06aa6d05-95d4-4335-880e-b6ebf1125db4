<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar equipo de protección personal básico' : 'Crear equipo de protección personal básico'"
        [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="BasicProtectionEquipmentName" class="form-label"
                                [ngClass]="{'ng-invalid' : (basicProtectionEquipmentName.invalid && (basicProtectionEquipmentName.touched || basicProtectionEquipmentName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="BasicProtectionEquipmentName" name="BasicProtectionEquipmentName"
                                formControlName="basicProtectionEquipmentNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="BasicProtectionEquipmentEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="BasicProtectionEquipmentEnabled" name="BasicProtectionEquipmentEnabled"
                                formControlName="basicProtectionEquipmentEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="BasicProtectionEquipmentCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="BasicProtectionEquipmentCode" name="BasicProtectionEquipmentCode"
                                formControlName="basicProtectionEquipmentCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>