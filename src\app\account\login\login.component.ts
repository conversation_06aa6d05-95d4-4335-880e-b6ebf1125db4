import { Component, Injector } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AuthenticationServiceProxy } from '@proxies/autentication.proxy';
import { IPasswordInputConfig } from '@core/models/password-input-config';
import { AppConsts } from '@core/inheritance/app-core-consts';
import { AuthenticationResult, PublicClientApplication } from '@azure/msal-browser';
import { finalize } from 'rxjs';
import { AppHttpRequestScaService } from '@core/services/servicesSCA/http.requestSCA.service';

@Component({
    templateUrl: 'login.component.html',
    styleUrls: [
        'login.component.scss'
    ]
})
export class LoginComponent extends ViewComponent {

    private authenticationServiceProxy!: AuthenticationServiceProxy;
    private formBuilder!: FormBuilder;
    private msal: PublicClientApplication;
    private requestSCA: AppHttpRequestScaService; 

    inputPassword: IPasswordInputConfig = {
        type: 'password',
        icon: 'eye-outline',
        toggle: () => {
            switch (this.inputPassword.type) {
                case 'text': {
                    this.inputPassword.type = 'password';
                    this.inputPassword.icon = 'eye-outline';
                    break;
                }
                case 'password': {
                    this.inputPassword.type = 'text';
                    this.inputPassword.icon = 'eye-off-outline';
                    break;
                }
            }
        }
    }

    loginForm!: FormGroup;

    get emailAddress(): AbstractControl {
        return this.loginForm.controls['emailAdressInput'];
    };

    get password(): AbstractControl {
        return this.loginForm.controls['passwordInput'];
    };

    private disabled!: boolean;
    showCredentialsForm = false;

    constructor(_injector: Injector) {
        super(_injector);

        this.authenticationServiceProxy = _injector.get(AuthenticationServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);
        this.requestSCA = _injector.get(AppHttpRequestScaService);

        this.loginForm = this.formBuilder.group({
            'emailAdressInput': ['', Validators.compose([Validators.required, Validators.email, Validators.maxLength(64)])],
            'passwordInput': ['', Validators.compose([Validators.required, Validators.maxLength(64)])]
        });
    }

    async login(): Promise<void> {
        this.loginForm.markAllAsTouched();

        if (this.disabled || this.loginForm.invalid)
            return;

        this.disabled = true;

        const loading = await this.loader.show();

        this.authenticationServiceProxy
            .authWithEmailAddress(this.emailAddress.value, this.password.value)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: async (response) => {
                    await this.preferences.set(AppConsts.authorization.authTokenName, response.accessToken);
                    await this.preferences.set(AppConsts.authorization.refreshAuthTokenName, response.refreshToken);
                    await this.preferences.set(AppConsts.authorization.encrptedAuthTokenName, response.encryptedAccessToken);
                    await this.requestSCA.loginSCA()

                    location.href = '';
                },
            });
    }

    async loginWithMicrosoft(): Promise<void> {
        if (!this.msal) {
            this.msal = new PublicClientApplication({
                auth: {
                    clientId: this.settings.get('App.Authentication.Microsoft.ClientId'),
                    authority: "https://login.microsoftonline.com/" + this.settings.get('App.Authentication.Microsoft.Authority'),
                    redirectUri: AppConsts.baseUrl,
                },
            });
        }

        const loading = await this.loader.show();

        const hideLoading = async () => {
            this.disabled = false;
            await loading.dismiss();
        };

        try {
            await this.msal.initialize();
            await this.msal.clearCache();
            await this.msal.loginPopup({
                scopes: ['user.read'],
                redirectUri: AppConsts.baseUrl
            });

            const accessTokenResponse: AuthenticationResult = await this.msal.acquireTokenSilent({
                account: this.msal.getAllAccounts()[0],
                scopes: ['user.read']
            });

            await this.requestSCA.loginSCA()

            this.authenticationServiceProxy
                .authWithMicrosoft(accessTokenResponse.accessToken)
                .pipe(finalize(async () => await hideLoading())).subscribe({
                    next: async (response) => {
                        await this.preferences.set(AppConsts.authorization.authTokenName, response.accessToken);
                        await this.preferences.set(AppConsts.authorization.refreshAuthTokenName, response.refreshToken);
                        await this.preferences.set(AppConsts.authorization.encrptedAuthTokenName, response.encryptedAccessToken);
                        location.href = '';
                    },
                });
        } catch (e) {
            await hideLoading();
        }
    }
}