import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IntegrationCenterLogisticDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Subscription, finalize } from 'rxjs';
import { Paginator } from 'primeng/paginator';
import { IFilterOption } from '@core/models/filters';
import { DateTime } from 'luxon';
import { MeetDto, MeetServiceProxy, MeetStatus, MeetTurn, MeetType } from '@proxies/meet.proxy';
import { MeetCreateEditComponent } from '../create-edit/create-edit.component';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { IPopoverAction } from '@core/models/popover-action';
import { MeetCompleteComponent } from '../complete/complete.component';
import { MeetCancelComponent } from '../cancel/cancel.component';

@Component({
    selector: 'app-meet-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class MeetDashboardComponent extends ViewComponent implements OnInit {

    private meetServiceProxy: MeetServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    meetTypeArray: IFilterOption<MeetType>[] = [
        { label: 'De rutina', value: MeetType.Recurrent },
        { label: 'A demanda', value: MeetType.OnDemand }
    ];

    meetTurnArray: IFilterOption<MeetTurn>[] = [
        { label: 'Diurno', value: MeetTurn.Diurnal },
        { label: 'Nocturno', value: MeetTurn.Nocturnal }
    ];

    meetStatusArray: IFilterOption<MeetStatus>[] = [
        { label: 'Programado', value: MeetStatus.Programmed },
        { label: 'Ejecutado', value: MeetStatus.Executed },
        { label: 'Cancelado', value: MeetStatus.Canceled }
    ];

    profitCenters: IFilterOption<string>[];

    meetTypes = {
        recurrent: MeetType.Recurrent,
        onDemand: MeetType.OnDemand
    };

    meetTurns = {
        diurnal: MeetTurn.Diurnal,
        nocturnal: MeetTurn.Nocturnal
    };

    meetStatuses = {
        programmed: MeetStatus.Programmed,
        executed: MeetStatus.Executed,
        canceled: MeetStatus.Canceled,
    };

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.meetServiceProxy = _injector.get(MeetServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    ngOnInit(): void {
        this.integrationServiceProxy
            .getAllLogiticCenters()
            .subscribe({
                next: (response) => {
                    this.profitCenters = response.items.map(p => {
                        return <IFilterOption<string>>{
                            label: p.name,
                            value: p.code
                        }
                    });
                }
            });
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: MeetCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: MeetDto) {
        this.dialog.showWithData<boolean>({
            component: MeetCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    completeItem(item: MeetDto) {
        this.dialog.showWithData<boolean>({
            component: MeetCompleteComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    cancelItem(item: MeetDto) {
        this.dialog.showWithData<boolean>({
            component: MeetCancelComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: MeetDto) {
        this.message.confirm(`¿Estas seguro de eliminar la charla "${item.code}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.meetServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado la charla satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.meetServiceProxy
            .getAll(
                this.dataTable?.filters?.['title']?.['value'],
                this.dataTable?.filters?.['description']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                (<IntegrationCenterLogisticDto[]>this.dataTable?.filters?.['centerIds']?.['value'])?.map(p => p.code) || [],
                (<IFilterOption<MeetType>[]>this.dataTable?.filters?.['types']?.['value'])?.map(p => p.value) || [],
                (<IFilterOption<MeetTurn>[]>this.dataTable?.filters?.['turns']?.['value'])?.map(p => p.value) || [],
                (<IFilterOption<MeetStatus>[]>this.dataTable?.filters?.['statuses']?.['value'])?.map(p => p.value) || [],
                DateTime.fromJSDate(this.dataTable?.filters?.['startTime']?.['value']),
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: MeetDto) {

        let actions: IPopoverAction[] = [];

        if (item.status == MeetStatus.Executed || item.status == MeetStatus.Canceled) {
            actions = [
                {
                    label: 'Visualizar',
                    permissions: [
                        'Pages.Management.Meet.Modify'
                    ],
                    callback: () => this.editItem(item)
                },
                {
                    label: 'Eliminar',
                    permissions: [
                        'Pages.Management.Meet.Delete'
                    ],
                    callback: () => this.deleteItem(item)
                }
            ];
        } else {
            actions = [
                {
                    label: 'Editar',
                    permissions: [
                        'Pages.Management.Meet.Modify'
                    ],
                    callback: () => this.editItem(item)
                },
                {
                    label: 'Finalizar',
                    permissions: [
                        'Pages.Management.Meet.Modify'
                    ],
                    callback: () => this.completeItem(item)
                },
                {
                    label: 'Cancelar',
                    permissions: [
                        'Pages.Management.Meet.Modify'
                    ],
                    callback: () => this.cancelItem(item)
                },
                {
                    label: 'Eliminar',
                    permissions: [
                        'Pages.Management.Meet.Delete'
                    ],
                    callback: () => this.deleteItem(item)
                }
            ];
        }

        this.popover.show(event, actions);

    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.meetServiceProxy
            .export(
                this.dataTable?.filters?.['title']?.['value'],
                this.dataTable?.filters?.['description']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                (<IntegrationCenterLogisticDto[]>this.dataTable?.filters?.['centerIds']?.['value'])?.map(p => p.code) || [],
                (<IFilterOption<MeetType>[]>this.dataTable?.filters?.['types']?.['value'])?.map(p => p.value) || [],
                (<IFilterOption<MeetTurn>[]>this.dataTable?.filters?.['turns']?.['value'])?.map(p => p.value) || [],
                (<IFilterOption<MeetStatus>[]>this.dataTable?.filters?.['statuses']?.['value'])?.map(p => p.value) || [],
                DateTime.fromJSDate(this.dataTable?.filters?.['startTime']?.['value']),
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters(): void {
        this.dataTable?.clear();
        this.getData();
    }
}