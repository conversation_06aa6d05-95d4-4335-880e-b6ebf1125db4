import { Component, Injector, Input, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { InspectionServiceProxy, InspectionDto } from '@proxies/inspection.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-inspection-reject',
    templateUrl: 'reject-inspection.component.html',
    styleUrls: [
        'reject-inspection.component.scss'
    ]
})
export class InspectionRejectComponent extends ViewComponent implements OnInit {

    private inspectionServiceProxy: InspectionServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;

    item!: InspectionDto;

    modalForm!: FormGroup;
    disabled!: boolean;

    get inspectionRejectDescription(): AbstractControl {
        return this.modalForm.controls['inspectionRejectDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.formBuilder = _injector.get(FormBuilder);
        this.inspectionServiceProxy = _injector.get(InspectionServiceProxy);

        this.modalForm = this.formBuilder.group({
            inspectionRejectDescriptionInput: ['', Validators.compose([Validators.required, Validators.maxLength(5000)])],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.disabled = false;
                },
                error: () => this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {

        const description: string = this.inspectionRejectDescription.value;

        if (isNullEmptyOrWhiteSpace(description)) {
            this.message.info('El motivo del rechazo de la inspección es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .reject(this.item.id, description)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss()
            })).subscribe({
                next: () => {
                    this.notify.success('Inspección rechazada satisfactoriamente', 5000);
                    this.dialog.dismiss(true);
                }
            });
    }
}