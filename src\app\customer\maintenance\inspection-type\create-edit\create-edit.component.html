<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar tipo de inspección' : 'Crear tipo de inspección'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="InspectionTypeName" class="form-label"
                                [ngClass]="{'ng-invalid' : (inspectionTypeName.invalid && (inspectionTypeName.touched || inspectionTypeName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="InspectionTypeName" name="InspectionTypeName"
                                formControlName="inspectionTypeNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="InspectionTypeEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="InspectionTypeEnabled" name="InspectionTypeEnabled"
                                formControlName="inspectionTypeEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="InspectionTypeCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="InspectionTypeCode" name="InspectionTypeCode"
                                formControlName="inspectionTypeCodeInput">
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>