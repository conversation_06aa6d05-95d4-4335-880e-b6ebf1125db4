import { mergeMap } from 'rxjs/operators';
import { Injectable, Injector } from "@angular/core";
import { Observable, of } from "rxjs";
import { AppHttpRequestService } from '@core/services/http.request.service';
import { IJsonConvertable } from '@core/models/mappings';

@Injectable()
export class TrainingEvaluationServiceProxy {
    private request: AppHttpRequestService;

    constructor(_injector: Injector) {
        this.request = _injector.get(AppHttpRequestService);
    }

    get(trainingId: number): Observable<TrainingEvaluationDto> {
        let url = '/api/services/app/TrainingEvaluation/GetByTrainingId?';
        if (trainingId !== null && trainingId !== undefined)
            url += "Id=" + encodeURIComponent("" + trainingId) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new TrainingEvaluationDto().fromJS(data))));
    }

    create(item: TrainingEvaluationCreateDto): Observable<void> {
        const url = '/api/services/app/TrainingEvaluation/Create';
        const body: string = item.toJSON();

        return this.request.post(url, body);
    }

    update(item: TrainingEvaluationUpdateDto): Observable<void> {
        const url = '/api/services/app/TrainingEvaluation/Update';
        const body: string = item.toJSON();

        return this.request.put(url, body);
    }
}

export class TrainingEvaluationDto implements IJsonConvertable<TrainingEvaluationDto> {
    id!: number;
    title!: string;
    description!: string;
    isActive!: boolean;
    trainingId!: number;
    questions!: TrainingEvaluationQuestionDto[];

    constructor(data?: ITrainingEvaluationDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.title = data["title"];
            this.description = data["description"];
            this.isActive = data["isActive"];
            this.trainingId = data["trainingId"];
            if (Array.isArray(data["questions"])) {
                this.questions = [] as any;
                for (let item of data["questions"])
                    this.questions!.push(new TrainingEvaluationQuestionDto().fromJS(item));
            }
        }
    }

    static fromJS(data: any): TrainingEvaluationDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["title"] = this.title;
        data["description"] = this.description;
        data["isActive"] = this.isActive;
        data["trainingId"] = this.trainingId;
        if (Array.isArray(this.questions)) {
            data["questions"] = [];
            for (let item of this.questions)
                data["questions"].push(item.toJSON());
        }
        return JSON.stringify(data);
    }
}

export class TrainingEvaluationCreateDto implements IJsonConvertable<TrainingEvaluationCreateDto> {
    title!: string;
    description!: string;
    isActive!: boolean;
    trainingId!: number;
    questions!: TrainingEvaluationQuestionCreateDto[];

    constructor(data?: ITrainingEvaluationCreateDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.title = data["title"];
            this.description = data["description"];
            this.isActive = data["isActive"];
            this.trainingId = data["trainingId"];
            if (Array.isArray(data["questions"])) {
                this.questions = [] as any;
                for (let item of data["questions"])
                    this.questions!.push(new TrainingEvaluationQuestionCreateDto().fromJS(item));
            }
        }
    }

    static fromJS(data: any): TrainingEvaluationCreateDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationCreateDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationCreateDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["title"] = this.title;
        data["description"] = this.description;
        data["isActive"] = this.isActive;
        data["trainingId"] = this.trainingId;
        if (Array.isArray(this.questions)) {
            data["questions"] = [];
            for (let item of this.questions) {
                // Crear objeto directamente en lugar de string JSON
                data["questions"].push({
                    questionText: item.questionText,
                    optionA: item.optionA,
                    optionB: item.optionB,
                    optionC: item.optionC,
                    optionD: item.optionD,
                    correctAnswer: item.correctAnswer,
                    order: item.order
                });
            }
        }
        return JSON.stringify(data);
    }
}

export class TrainingEvaluationQuestionDto implements IJsonConvertable<TrainingEvaluationQuestionDto> {
    id!: number;
    questionText!: string;
    optionA!: string;
    optionB!: string;
    optionC!: string;
    optionD!: string;
    correctAnswer!: string;
    order!: number;

    constructor(data?: ITrainingEvaluationQuestionDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.questionText = data["questionText"];
            this.optionA = data["optionA"];
            this.optionB = data["optionB"];
            this.optionC = data["optionC"];
            this.optionD = data["optionD"];
            this.correctAnswer = data["correctAnswer"];
            this.order = data["order"];
        }
    }

    static fromJS(data: any): TrainingEvaluationQuestionDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationQuestionDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationQuestionDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["questionText"] = this.questionText;
        data["optionA"] = this.optionA;
        data["optionB"] = this.optionB;
        data["optionC"] = this.optionC;
        data["optionD"] = this.optionD;
        data["correctAnswer"] = this.correctAnswer;
        data["order"] = this.order;
        return JSON.stringify(data);
    }
}

export class TrainingEvaluationQuestionCreateDto implements IJsonConvertable<TrainingEvaluationQuestionCreateDto> {
    questionText!: string;
    optionA!: string;
    optionB!: string;
    optionC!: string;
    optionD!: string;
    correctAnswer!: string;
    order!: number;

    constructor(data?: ITrainingEvaluationQuestionCreateDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.questionText = data["questionText"];
            this.optionA = data["optionA"];
            this.optionB = data["optionB"];
            this.optionC = data["optionC"];
            this.optionD = data["optionD"];
            this.correctAnswer = data["correctAnswer"];
            this.order = data["order"];
        }
    }

    static fromJS(data: any): TrainingEvaluationQuestionCreateDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationQuestionCreateDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationQuestionCreateDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["questionText"] = this.questionText;
        data["optionA"] = this.optionA;
        data["optionB"] = this.optionB;
        data["optionC"] = this.optionC;
        data["optionD"] = this.optionD;
        data["correctAnswer"] = this.correctAnswer;
        data["order"] = this.order;
        return JSON.stringify(data);
    }
}

export class TrainingEvaluationUpdateDto implements IJsonConvertable<TrainingEvaluationUpdateDto> {
    id!: number;
    title!: string;
    description!: string;
    isActive!: boolean;
    trainingId!: number;
    questions!: TrainingEvaluationQuestionUpdateDto[];

    constructor(data?: ITrainingEvaluationUpdateDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.title = data["title"];
            this.description = data["description"];
            this.isActive = data["isActive"];
            this.trainingId = data["trainingId"];
            if (Array.isArray(data["questions"])) {
                this.questions = [] as any;
                for (let item of data["questions"])
                    this.questions!.push(new TrainingEvaluationQuestionUpdateDto().fromJS(item));
            }
        }
    }

    static fromJS(data: any): TrainingEvaluationUpdateDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationUpdateDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationUpdateDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["title"] = this.title;
        data["description"] = this.description;
        data["isActive"] = this.isActive;
        data["trainingId"] = this.trainingId;
        if (Array.isArray(this.questions)) {
            data["questions"] = [];
            for (let item of this.questions) {
                // Crear objeto directamente en lugar de string JSON
                data["questions"].push({
                    id: item.id,
                    questionText: item.questionText,
                    optionA: item.optionA,
                    optionB: item.optionB,
                    optionC: item.optionC,
                    optionD: item.optionD,
                    correctAnswer: item.correctAnswer,
                    order: item.order,
                    trainingEvaluationId: item.trainingEvaluationId
                });
            }
        }
        return JSON.stringify(data);
    }
}

export class TrainingEvaluationQuestionUpdateDto implements IJsonConvertable<TrainingEvaluationQuestionUpdateDto> {
    id!: number;
    questionText!: string;
    optionA!: string;
    optionB!: string;
    optionC!: string;
    optionD!: string;
    correctAnswer!: string;
    order!: number;
    trainingEvaluationId!: number;

    constructor(data?: ITrainingEvaluationQuestionUpdateDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.questionText = data["questionText"];
            this.optionA = data["optionA"];
            this.optionB = data["optionB"];
            this.optionC = data["optionC"];
            this.optionD = data["optionD"];
            this.correctAnswer = data["correctAnswer"];
            this.order = data["order"];
            this.trainingEvaluationId = data["trainingEvaluationId"];
        }
    }

    static fromJS(data: any): TrainingEvaluationQuestionUpdateDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationQuestionUpdateDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationQuestionUpdateDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["questionText"] = this.questionText;
        data["optionA"] = this.optionA;
        data["optionB"] = this.optionB;
        data["optionC"] = this.optionC;
        data["optionD"] = this.optionD;
        data["correctAnswer"] = this.correctAnswer;
        data["order"] = this.order;
        data["trainingEvaluationId"] = this.trainingEvaluationId;
        return JSON.stringify(data);
    }
}

export interface ITrainingEvaluationDto {
    id: number;
    title: string;
    description: string;
    isActive: boolean;
    trainingId: number;
    questions: TrainingEvaluationQuestionDto[];
}

export interface ITrainingEvaluationCreateDto {
    title: string;
    description: string;
    isActive: boolean;
    trainingId: number;
    questions: TrainingEvaluationQuestionCreateDto[];
}

export interface ITrainingEvaluationQuestionDto {
    id: number;
    questionText: string;
    optionA: string;
    optionB: string;
    optionC: string;
    optionD: string;
    correctAnswer: string;
    order: number;
}

export interface ITrainingEvaluationQuestionCreateDto {
    questionText: string;
    optionA: string;
    optionB: string;
    optionC: string;
    optionD: string;
    correctAnswer: string;
    order: number;
}

export interface ITrainingEvaluationUpdateDto {
    id: number;
    title: string;
    description: string;
    isActive: boolean;
    trainingId: number;
    questions: TrainingEvaluationQuestionUpdateDto[];
}

export interface ITrainingEvaluationQuestionUpdateDto {
    id: number;
    questionText: string;
    optionA: string;
    optionB: string;
    optionC: string;
    optionD: string;
    correctAnswer: string;
    order: number;
    trainingEvaluationId: number;
}
