<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="loaded ? (completed ? 'Visualizar inspección' : (id ? 'Editar inspección' : 'Crear inspección')) : 'Cargando...'" [disabled]="disabled">
        <app-modal-body>
            <p-tabView *ngIf="loaded" [(activeIndex)]="activeIndex" [scrollable]="true">
                <p-tabPanel header="Información general">
                    <app-inspection-general-information
                        [inspection]="item"
                        [completed]="completed"
                        [modalForm]="modalForm"
                        [centers]="centers"
                        [inspectionTypes]="inspectionTypes"
                        [(inspectionDate)]="inspectionDate"
                        [(inspectionTime)]="inspectionTime"/>
                </p-tabPanel>
                <p-tabPanel header="Reprogramaciones">
                    <app-inspection-modified-information
                        [inspection]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel header="Historial">
                    <app-inspection-history-information
                        [inspection]="item"
                        [completed]="completed"/>
                </p-tabPanel>
                <p-tabPanel *ngIf="item.mode == inspectionModes.executed || item.mode == inspectionModes.rejected || item.mode == inspectionModes.completed" header="Información de inspección">
                    <app-inspection-operation-information
                        [inspection]="item"
                        [completed]="completed"/>
                </p-tabPanel>
            </p-tabView>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" [showSaveButton]="!completed" (onSave)="save()" />
    </app-modal>
</form>