import { Component, Injector, Input } from '@angular/core';
import { AbstractControl, FormGroup } from '@angular/forms';
import { AppFindBussinessPartnerComponent } from '@components/find-bussiness-partner/find-bussiness-partner.component';
import { AppFindProjectComponent } from '@components/find-project/find-project.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IntegrationCostCenter, IntegrationMasterManagementDto, IntegrationMasterIncomeExpenseDto, IntegrationOperationProjectDto, IntegrationServiceCallLogisticCenter, IntegrationBusinessPartnerDto } from '@proxies/integration.proxy';
import { OperationTaskDto } from '@proxies/operation-task.proxy';

@Component({
    selector: 'app-operation-task-general-information',
    templateUrl: 'general-information.component.html',
    styleUrls: [
        'general-information.component.scss'
    ]
})
export class OperationTaskGeneralInformationComponent extends ViewComponent {

    @Input() operationTask!: OperationTaskDto;
    @Input() areas!: IntegrationCostCenter[];
    @Input() centers!: IntegrationServiceCallLogisticCenter[];
    @Input() managements!: IntegrationMasterManagementDto[];
    @Input() incomeExpenses!: IntegrationMasterIncomeExpenseDto[];
    @Input() modalForm!: FormGroup;
    @Input() editable!: boolean;

    get customer(): string {
        return this.operationTask.customerCode === undefined || this.operationTask.customerName == '' ?
            "" :
            `${this.operationTask.customerCode} - ${this.operationTask.customerName}`;
    }

    get operationTaskCode(): AbstractControl {
        return this.modalForm.controls['operationTaskCodeInput'];
    };

    get operationTaskHasIntegration(): AbstractControl {
        return this.modalForm.controls['operationTaskHasIntegrationSelect'];
    };

    get projectCode(): AbstractControl {
        return this.modalForm.controls['projectCodeInput'];
    };

    get projectName(): AbstractControl {
        return this.modalForm.controls['projectNameInput'];
    };

    get location(): AbstractControl {
        return this.modalForm.controls['locationInput'];
    };

    get center(): AbstractControl {
        return this.modalForm.controls['centerSelect'];
    };

    get management(): AbstractControl {
        return this.modalForm.controls['managementSelect'];
    };

    get area(): AbstractControl {
        return this.modalForm.controls['areaSelect'];
    };

    get movement(): AbstractControl {
        return this.modalForm.controls['movementSelect'];
    };

    constructor(injector: Injector) {
        super(injector);
    }

    showFindProject(): void {
        this.dialog.showWithData<IntegrationOperationProjectDto>({
            component: AppFindProjectComponent
        }).then((response) => {
            if (response.data.result) {
                const project: IntegrationOperationProjectDto = response.data.result;

                this.projectCode.setValue(project.prjCode);
                this.projectName.setValue(project.prjName);

                this.operationTask.projectCode = project.prjCode;
                this.operationTask.projectName = project.prjName;

                this.operationTask.centerCode = project.u_mss_dim1;
                this.operationTask.centerName = project.nameDim1;

                this.operationTask.customerCode = project.u_mss_codsn;
                this.operationTask.customerName = project.u_mss_nomsn;

                this.operationTask.managementCode = project.u_mss_dim3;
                this.operationTask.managementName = project.nameDim3;

                this.operationTask.areaCode = project.u_mss_dim4;
                this.operationTask.areaName = project.nameDim4;

                this.operationTask.movementCode = project.u_mss_dim2;
                this.operationTask.movementName = project.nameDim2;

                this.operationTask.locationName = project.u_mss_ubic;
            }
        });
    }

    showFindCustomer(): void {
        this.dialog.showWithData<IntegrationBusinessPartnerDto>({
            component: AppFindBussinessPartnerComponent,
            componentProps: {
                selectionMode: 'single',
                selecteds: []
            }
        }).then((response) => {
            if (response.data.result) {
                this.operationTask.customerCode = response.data.result.cardCode;
                this.operationTask.customerName = response.data.result.cardName;
            }
        });
    }

    onHasIntegrationChange(event: any): void {

        this.operationTask.projectCode = null;
        this.operationTask.projectName = null;
        this.operationTask.centerCode = null;
        this.operationTask.centerName = null;
        this.operationTask.customerCode = null;
        this.operationTask.customerName = null;
        this.operationTask.managementCode = null;
        this.operationTask.managementName = null;
        this.operationTask.areaCode = null;
        this.operationTask.areaName = null;
        this.operationTask.movementCode = null;
        this.operationTask.movementName = null;
        this.operationTask.locationName = null;

        this.operationTaskCode.setValue('');
        this.projectCode.setValue('');
        this.projectName.setValue('');
        this.location.setValue('');
        this.center.setValue('-1');
        this.management.setValue('-1');
        this.area.setValue('-1');
        this.movement.setValue('-1');
    }
}