<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar usuario' : 'Crear usuario'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView *ngIf="loaded" [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div *ngIf="step == steps.search" class="row">

                        <div class="col-12 mb-4">
                            <div class="ion-input">
                                <div class="input-control">
                                    <label class="form-label" for="UserEmployeeSearch">
                                        Empleado (*)
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployee()" class="form-control rounded"
                                            id="UserEmployeeSearch" name="UserEmployeeSearch" rows="2"
                                            value="Presiona para buscar un empleado" readonly>
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="alert alert-warning" role="alert">
                                Debe seleccionar un employeado para continuar con el registro del usuario
                            </div>
                        </div>

                    </div>

                    <div *ngIf="step == steps.typing" class="row">

                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label class="form-label" for="UserDocumentTypeSearch"
                                        [ngClass]="{'ng-invalid' : (documentType.invalid && (documentType.touched || documentType.dirty))}">
                                        Tipo de documento (*)
                                    </label>

                                    <select class="form-control" id="UserDocumentTypeSearch"
                                        name="UserDocumentTypeSearch" formControlName="documentTypeSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let documentType of documentTypes" [value]="documentType.id">
                                            {{documentType.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El tipo de documento es obligatorio.
                                        </ion-col>
                                    </ion-row>

                                </div>
                            </div>
                        </div>

                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="UserDocument" class="form-label"
                                        [ngClass]="{'ng-invalid' : (document.invalid && (document.touched || document.dirty))}">
                                        Documento (*)
                                    </label>

                                    <input type="text" class="form-control" id="UserDocument" name="UserDocument"
                                        formControlName="documentInput" maxlength="32">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El documento es obligatorio.
                                        </ion-col>
                                    </ion-row>

                                </div>
                            </div>
                        </div>

                        <ng-container *ngIf="isAdmin">

                            <div class="col-12">
                                <div class="ion-input form-group mb-4">
                                    <div class="input-control">

                                        <label for="UserName" class="form-label"
                                            [ngClass]="{'ng-invalid' : (name.invalid && (name.touched || name.dirty))}">
                                            Nombre (*)
                                        </label>

                                        <input type="text" class="form-control" id="UserName" name="UserName"
                                            formControlName="nameInput">

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                El nombre del usuario es obligatorio.
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserSurname" class="form-label"
                                            [ngClass]="{'ng-invalid' : (surname.invalid && (surname.touched || surname.dirty))}">
                                            Apellido Paterno (*)
                                        </label>

                                        <input type="text" class="form-control" id="UserSurname" name="UserSurname"
                                            formControlName="surnameInput">

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Los apellido paterno del usuario es obligatorio.
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserSecondSurname" class="form-label"
                                            [ngClass]="{'ng-invalid' : (secondSurname.invalid && (secondSurname.touched || secondSurname.dirty))}">
                                            Apellidos Materno (*)
                                        </label>

                                        <input type="text" class="form-control" id="UserSecondSurname"
                                            name="UserSecondSurname" formControlName="secondSurnameInput">

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Los apellido materno del usuario es obligatorio.
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserEmailAddress" class="form-label"
                                            [ngClass]="{'ng-invalid' : (emailAddress.invalid && (emailAddress.touched || emailAddress.dirty))}">
                                            Correo electrónico (*)
                                        </label>

                                        <input type="text" class="form-control" id="UserEmailAddress"
                                            name="UserEmailAddress" formControlName="emailAddressInput">

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                {{
                                                emailAddress.value ?
                                                'El correo electrónico no es válido.' :
                                                'El correo electrónico es obligatorio.'
                                                }}
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserPhoneNumber" class="form-label"
                                            [ngClass]="{'ng-invalid' : (phoneNumber.invalid && (phoneNumber.touched || phoneNumber.dirty))}">
                                            Celular
                                        </label>

                                        <input type="tel" class="form-control" id="UserPhoneNumber"
                                            name="UserPhoneNumber" formControlName="phoneNumberInput">

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                {{
                                                phoneNumber.value ?
                                                'El celular ingresado no es válido.' :
                                                'El celular es obligatorio.'
                                                }}
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="ion-input mb-4">
                                    <div class="input-control">
                                        <label class="form-label" for="UserIsActive">
                                            ¿Activo?
                                        </label>
                                        <select class="form-control" id="UserIsActive" name="UserIsActive"
                                            formControlName="isActiveSelect">
                                            <option value="true">
                                                Si
                                            </option>
                                            <option value="false">
                                                No
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div *ngIf="id" class="col-12 mb-3">
                                <ion-checkbox labelPlacement="end" justify="start"
                                    formControlName="isNewPasswordCheckbox">
                                    <span class="fz-small">Crear nueva contraseña</span>
                                </ion-checkbox>
                            </div>

                            <div *ngIf="!id || isNewPassword.value" class="col-12">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserNewPassword" class="form-label"
                                            [ngClass]="{'ng-invalid' : (newPassword.invalid && (newPassword.touched || newPassword.dirty))}">
                                            {{id ? 'Nueva contraseña' : 'Contraseña'}} (*)
                                        </label>

                                        <input type="password" class="form-control" id="UserNewPassword"
                                            name="UserNewPassword" formControlName="newPasswordInput">

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                La contraseña es obligatoria.
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>

                        </ng-container>

                        <ng-container *ngIf="!isAdmin">

                            <div class="col-6">
                                <div class="ion-input form-group mb-4">
                                    <div class="input-control">

                                        <label for="UserEmployeeName" class="form-label">
                                            Nombres
                                        </label>

                                        <input type="text" class="form-control" id="UserEmployeeName"
                                            name="UserEmployeeName"
                                            value="{{item?.employee?.employee?.firstName}} {{item?.employee?.employee?.middleName}}"
                                            readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserEmployeeSurname" class="form-label">
                                            Apellidos
                                        </label>

                                        <input type="text" class="form-control" id="UserEmployeeSurname"
                                            name="UserEmployeeSurname" value="{{item?.employee?.employee?.lastName}}"
                                            readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserEmployeeEmailAddress" class="form-label">
                                            Correo electrónico
                                        </label>

                                        <input type="text" class="form-control" id="UserEmployeeEmailAddress"
                                            name="UserEmployeeEmailAddress" value="{{item?.employee?.employee?.email}}"
                                            readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="ion-input mb-4">
                                    <div class="input-control">

                                        <label for="UserEmployeePhoneNumber" class="form-label">
                                            Celular
                                        </label>

                                        <input type="tel" class="form-control" id="UserEmployeePhoneNumber"
                                            name="UserEmployeePhoneNumber" value="{{item?.employee?.employee?.mobile}}"
                                            readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="ion-input mb-4">
                                    <div class="input-control">
                                        <label class="form-label" for="UserEmployeeIsActive">
                                            ¿Activo?
                                        </label>
                                        <select class="form-control" id="UserEmployeeIsActive"
                                            name="UserEmployeeIsActive" formControlName="isActiveSelect">
                                            <option value="true">
                                                Si
                                            </option>
                                            <option value="false">
                                                No
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </ng-container>

                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="UserJob" class="form-label"
                                        [ngClass]="{'ng-invalid' : (job.invalid && (job.touched || job.dirty))}">
                                        Cargo
                                    </label>

                                    <input type="text" class="form-control" id="UserJob" name="UserJob"
                                        formControlName="jobInput" maxlength="255">
                                </div>
                            </div>
                        </div>

                    </div>

                </p-tabPanel>

                <p-tabPanel header="Roles">
                    <div class="row">
                        <p-tree class="one-parent" [value]="roleData" [(selection)]="selectedRoles"
                            selectionMode="checkbox" [propagateSelectionUp]="false"
                            emptyMessage="No hay roles registrados">
                            <ng-template let-node pTemplate="default">
                                <b>{{ node.label }}</b>
                            </ng-template>
                        </p-tree>
                    </div>
                </p-tabPanel>

                <p-tabPanel *ngIf="id" header="Código QR">

                    <div class="row">
                        <div class="col-12 text-end">
                            <ion-button (click)="downloadUserCode()" title="Descargar" color="primary" fill="solid">
                                <ion-icon name="cloud-download" slot="icon-only"></ion-icon>
                            </ion-button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="d-flex flex-column text-center">
                            <div class="mx-auto">
                                <div id="qr-row" *ngIf="item && id" [innerHTML]="userCode | safeHtml">
                                </div>
                            </div>
                            <ion-note class="d-block fz-note text-muted">
                                El código es utilizado para localizar el usuario de forma más rápida y sencilla
                            </ion-note>
                        </div>
                    </div>

                </p-tabPanel>

            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>