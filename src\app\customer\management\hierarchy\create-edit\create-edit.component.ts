import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { icons } from '@core/utils/icons';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { HierarchyDto, HierarchyServiceProxy } from '@proxies/hierarchy.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-hierarchy-create-edit.component',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class HierarchyCreateEditComponent extends ViewComponent implements OnInit {

    private hierarchyServiceProxy: HierarchyServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;
    @Input() parentId: number;
    @Input() parent: HierarchyDto;

    item!: HierarchyDto;
    disabled: boolean = false;
    modalForm!: FormGroup;
    icon!: string;
    icons: string[] = icons();

    get hierarchyName(): AbstractControl {
        return this.modalForm.controls['hierarchyNameInput'];
    };

    get hierarchyEnabled(): AbstractControl {
        return this.modalForm.controls['hierarchyEnabledSelect'];
    };

    get hierarchyIcon(): AbstractControl {
        return this.modalForm.controls['hierarchyIconSelect'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.hierarchyServiceProxy = _injector.get(HierarchyServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            hierarchyNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            hierarchyEnabledSelect: ['false', [Validators.required]],
            hierarchyIconSelect: ['none', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.hierarchyServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.hierarchyName.setValue(this.item.name);
                        this.hierarchyEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.hierarchyIcon.setValue(this.item.icon);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new HierarchyDto();
            this.item.parentId = this.parentId;
            this.item.parent = this.parent;
        }
    }

    iconChange(event: any) {
        this.hierarchyIcon.setValue(event.value);
    }

    iconClear() {
        this.hierarchyIcon.setValue('none');
    }

    async save(): Promise<void> {

        this.item.name = this.hierarchyName.value;
        this.item.enabled = this.hierarchyEnabled.value == 'true';
        this.item.icon = this.hierarchyIcon.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.hierarchyServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Jerarquía actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.hierarchyServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Jerarquía creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}