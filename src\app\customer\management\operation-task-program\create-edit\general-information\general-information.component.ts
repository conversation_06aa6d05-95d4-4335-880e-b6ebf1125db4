import { Component, EventEmitter, Injector, Input, OnInit, Output } from '@angular/core';
import { AppFindOperationTaskComponent } from '@components/find-operation-task/find-operation-task.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { OperationTaskProgramDetailDto, OperationTaskProgramDto, OperationTaskProgramOperationTaskDto, OperationTaskProgramPersonDto } from '@proxies/operation-task-program.proxy';
import { OperationTaskDto, OperationTaskServiceProxy } from '@proxies/operation-task.proxy';
import { DateTime } from 'luxon';
import { OperationTaskProgramService } from '../../operation-task.service';

@Component({
    selector: 'app-operation-task-program-general-information',
    templateUrl: 'general-information.component.html',
    styleUrls: [
        'general-information.component.scss'
    ]
})
export class OperationTaskProgramGeneralInformationComponent extends ViewComponent {

    private _operationTime!: Date;

    private readonly operationTaskServiceProxy: OperationTaskServiceProxy;
    private readonly operationTaskProgramService: OperationTaskProgramService;

    @Input() editable!: boolean;
    @Input() item!: OperationTaskProgramDto;
    @Input() get operationTime(): Date {
        return this._operationTime;
    }

    set operationTime(value: Date) {
        this._operationTime = value;
        this.operationTimeChange.emit(value);
    }

    @Output() operationTimeChange: EventEmitter<Date> = new EventEmitter<Date>();

    constructor(injector: Injector) {
        super(injector);

        this.operationTaskServiceProxy = injector.get(OperationTaskServiceProxy);
        this.operationTaskProgramService = injector.get(OperationTaskProgramService);
    }

    showFindProject(): void {
        this.dialog.showWithData<OperationTaskDto>({
            component: AppFindOperationTaskComponent
        }).then(async (response) => {
            if (response.data.result) {
                const loading = await this.loader.show();

                try {
                    const operationTask: OperationTaskDto = await this.getOperationTaskProgram(response.data.result.id);

                    this.item.operationTask = new OperationTaskProgramOperationTaskDto().fromJS(operationTask);
                    let currentTime: DateTime = DateTime.now();
                    
                    for (let operationTaskPerson of operationTask.operationTaskPersons) {

                        if (operationTaskPerson.person !== null && operationTaskPerson.person !== undefined) {

                            if (this.item.operationTaskProgramDetails.findIndex(p => !p.remove && p.person?.id === operationTaskPerson?.person?.id) === -1) {

                                this.item.operationTaskProgramDetails.push(new OperationTaskProgramDetailDto({
                                    person: new OperationTaskProgramPersonDto().fromJS(operationTaskPerson.person),
                                    startTime: DateTime.fromObject({
                                        year: currentTime.year,
                                        month: currentTime.month,
                                        day: currentTime.day,
                                        hour: 8,
                                        minute: 0,
                                        second: 0,
                                        millisecond: 0
                                    }),
                                    endTime: DateTime.fromObject({
                                        year: currentTime.year,
                                        month: currentTime.month,
                                        day: currentTime.day,
                                        hour: 18,
                                        minute: 0,
                                        second: 0,
                                        millisecond: 0
                                    })
                                }));
                                
                                this.operationTaskProgramService.onPagination.next();
                            }
                        }
                    }
                } catch {

                }

                await loading.dismiss();
            }
        });
    }

    private getOperationTaskProgram(id: number): Promise<OperationTaskDto> {
        return new Promise<OperationTaskDto>((resolve, reject) => {
            this.operationTaskServiceProxy.get(id).subscribe({
                next: (result) => resolve(result),
                error: () => reject()
            });
        });
    }
}