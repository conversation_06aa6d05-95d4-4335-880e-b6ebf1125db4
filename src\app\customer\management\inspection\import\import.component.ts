import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { UploadResource } from '@core/utils/core.request';
import { isNullEmptyOrWhiteSpace, trim } from '@core/utils/tools';
import { DocumentTypeServiceProxy } from '@proxies/document-type.proxy';
import { InspectionTypeServiceProxy } from '@proxies/inspection-type.proxy';
import { InspectionDocumentTypeDto, InspectionDto, InspectionImportDto, InspectionInspectionTypeDto, InspectionServiceProxy, InspectionUserDto } from '@proxies/inspection.proxy';
import { IntegrationBusinessPartnerDto, IntegrationPropertyDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { UserServiceProxy } from '@proxies/user.proxy';
import { DateTime } from 'luxon';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import * as XLSX from 'xlsx';

@Component({
    selector: 'app-inspection-import',
    templateUrl: 'import.component.html',
    styleUrls: [
        'import.component.scss'
    ]
})
export class InspectionImportComponent extends ViewComponent implements OnInit {

    private readonly inspectionServiceProxy: InspectionServiceProxy;
    private readonly inspectionTypeServiceProxy: InspectionTypeServiceProxy;
    private readonly integrationServiceProxy: IntegrationServiceProxy;
    private readonly userServiceProxy: UserServiceProxy;
    private readonly documentTypeServiceProxy: DocumentTypeServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    saving: boolean = false;

    resource: UploadResource;
    totalRecordsCount: number = 0;
    records: InspectionImportDto[] = [];

    private inspectionTypes: InspectionInspectionTypeDto[];
    private inspectionProperties: IntegrationPropertyDto[];
    private inspectionUsers: InspectionUserDto[];
    private inspectionDocumentTypes: InspectionDocumentTypeDto[];

    private skipCount: number;
    private maxResultCount: number;

    private properties = {
        property: '0',
        startTime: '1',
        documentType: '2',
        document: '3',
        inspectionType: '4'
    };

    constructor(injector: Injector) {
        super(injector);

        this.inspectionServiceProxy = injector.get(InspectionServiceProxy);
        this.inspectionTypeServiceProxy = injector.get(InspectionTypeServiceProxy);
        this.integrationServiceProxy = injector.get(IntegrationServiceProxy);
        this.userServiceProxy = injector.get(UserServiceProxy);
        this.documentTypeServiceProxy = injector.get(DocumentTypeServiceProxy);
        this.fileDownloadService = injector.get(AppFileDownloadService);

        this.table.defaultRecordsCountPerPage = 10;
        this.skipCount = 0;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;
    }

    ngOnInit(): void {

    }

    onFileSelected(resource: UploadResource): void {
        if (this.fileIsValid(resource.name)) {
            this.resource = resource;
        } else {
            this.message.info('El archivo seleccionado es inválido. Por favor verifique la información antes de continuar.', 'Aviso');
        }
    }

    onRemoveResource(): void {
        this.resource = undefined;
    }

    getData(event?: TableLazyLoadEvent) {
        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    async import(): Promise<void> {
        const loading = await this.loader.show();
        const loadFilters = await this.loadFilters();

        if (loadFilters) {
            try {
                let workBook = XLSX.read(await this.resource.file.arrayBuffer(), { type: 'binary' });
                const sheetsCount = workBook.SheetNames.length;

                if (sheetsCount == 0) {
                    this.message.error('Lo sentimos, no se puede procesar el excel debido a que no posee hojas. Por favor verifique que sea el formato descargado desde el sitio web.', 'Aviso');
                    this.onRemoveResource();
                    return;
                }

                const sheet = workBook.Sheets[workBook.SheetNames[0]];
                const data = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '', blankrows: false, range: 1 });
                const rowsCount = data?.length || 0;

                if (rowsCount == 0) {
                    this.message.error('Lo sentimos, no se puede procesar el excel debido a que no posee ninguna fila', 'Aviso');
                    this.onRemoveResource();
                    return;
                }

                let items: InspectionImportDto[] = [];

                for (let pos = 0; pos < rowsCount; pos++) {

                    const propertyCode = trim(data[pos][this.properties.property]);
                    const startTime = trim(data[pos][this.properties.startTime]);
                    const documentTypeName = trim(data[pos][this.properties.documentType]);
                    const document = trim(data[pos][this.properties.document]);
                    const inspectionTypeName = trim(data[pos][this.properties.inspectionType]);

                    const property = this.inspectionProperties.find(p => p.code == propertyCode);
                    const documentType = this.inspectionDocumentTypes.find(p => p.name == documentTypeName);
                    const inspectionType = this.inspectionTypes.find(p => p.name == inspectionTypeName);

                    const inspection: InspectionImportDto = new InspectionImportDto({
                        property: property,
                        document: document,
                        documentType: documentType,
                        inspectionTime: DateTime.fromFormat(startTime, 'dd-MM-yyyy HH:mm'),
                        inspectionType: inspectionType
                    });

                    if (!this.validateInspection(inspection, pos)) {
                        this.onRemoveResource();
                        await loading.dismiss();
                        return;
                    }

                    items.push(inspection);
                }

                this.records = items;
                this.totalRecordsCount = items.length;

                this.skipCount = 0;
                this.maxResultCount = 5;
                this.formatPagination(this.skipCount, this.maxResultCount);

                this.notify.success('Datos cargados exitosamente. Debe presionar "Guardar cambios" para iniciar la importación', 5000);
                this.onRemoveResource();
                await loading.dismiss();
            } catch (e) {
                this.message.error('Hubo un error al procesar el documento excel. Por favor verifique la información del mismo', 'Aviso');
                this.onRemoveResource();
                await loading.dismiss();
            }
        }
    }

    async downloadTemplate(): Promise<void> {
        const loading = await this.loader.show();

        this.inspectionServiceProxy
            .template()
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    async save(): Promise<void> {
        const loading = await this.loader.show();

        this.processNextCreateInspection(async () => {
            await loading.dismiss()
            await this.notify.success('Se ha importado todos los registros de forma satisfactoria');
        }, async () => await loading.dismiss());
    }

    onBackButtonPressed(): void {
        this.navigation.back('/customer/management/inspections/dashboard');
    }

    private processNextCreateInspection(completed: () => void, exception: () => void) {
        if (this.records.length == 0) {
            completed();
        } else {

            const inspection: InspectionDto = new InspectionDto();
            inspection.inspectionType = this.records[0].inspectionType;
            inspection.property = this.records[0].property;
            inspection.customer = new IntegrationBusinessPartnerDto();
            inspection.customer.cardCode = this.records[0].property.cardCode;
            inspection.inspectionTime = this.records[0].inspectionTime;
            inspection.assignedUser = this.records[0].assignedUser;

            this.inspectionServiceProxy
                .create(inspection)
                .subscribe({
                    next: () => {
                        this.records.splice(0, 1);
                        this.processNextCreateInspection(completed, exception);
                    },
                    error: () => exception()
                });
        }
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        for (let item of this.records) {
            item.isHidden = true;
            if (index >= skipCount && result < maxResultCount) {
                item.isHidden = false;
                result++;
            }
            index++;
        }
    }


    private validateInspection(inspection: InspectionImportDto, pos: number): boolean {
        if (isNullEmptyOrWhiteSpace(inspection.document)) {
            this.message.info(`El nº de documento de la persona es obligatorio. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }
        if (!inspection.documentType) {
            this.message.info(`El tipo de documento es inválido los valores permitidos son ${this.inspectionDocumentTypes.map(p => '"' + p.name + '"').join(', ')}. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }
        if (inspection.documentType.hasMinLength && inspection.document.length < inspection.documentType.minLength) {
            this.message.info(`El tipo de documento ${inspection.documentType.name} de la persona debe tener una longitud mínima de ${inspection.documentType.minLength} caracteres. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }
        if (inspection.documentType.hasMaxLength && inspection.document.length > inspection.documentType.maxLength) {
            this.message.info(`El tipo de documento ${inspection.documentType.name} de la persona debe tener una longitud máxima de ${inspection.documentType.maxLength} caracteres. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }
        if (inspection.documentType.hasRegex && !new RegExp(inspection.documentType.regex).test(inspection.document)) {
            this.message.info(`El tipo de documento ${inspection.documentType.name} de la persona es inválido. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }
        if (!inspection.property) {
            this.message.info(`La bodega ingresada es inválida. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }
        if (!inspection.inspectionType) {
            this.message.info(`El tipo de inspección es inválida los valores permitidos son ${this.inspectionTypes.map(p => '"' + p.name + '"').join(', ')}. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }
        if (!inspection.inspectionTime.isValid) {
            this.message.info(`La fecha y hora de la inspección es inválida el formato permitido es día-mes-año ejemplo 01-01-2021 16:51. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }

        const user = this.inspectionUsers.find(p => p.documentType != undefined && p.documentType !== null && p.documentType.id == inspection.documentType.id && p.document == inspection.document);

        if (!user) {
            this.message.info(`No se ha encontrado a ningún usuario con el ${inspection.documentType.name} ${inspection.document}. Debe revisar la fila ${pos + 2}.`, 'Aviso');
            return false;
        }

        inspection.assignedUser = user;

        return true;
    }

    private fileIsValid(value: string): boolean {

        if (value.lastIndexOf('.') == -1)
            return false;

        const extension: string = value.substring(value.lastIndexOf('.') + 1).toLocaleLowerCase();

        switch (extension) {
            case 'xlsx': return true;
        }

        return false;
    }

    private async loadFilters(): Promise<boolean> {
        return new Promise<boolean>((resolve) => {

            this.loadInspectionTypes(() => {

                this.loadProperties(() => {

                    this.loadDocumentTypes(() => {

                        this.loadUsers(
                            () => resolve(true),
                            () => resolve(false)
                        );

                    }, () => resolve(false));

                }, () => resolve(false));

            }, () => resolve(false));

        });
    }

    private loadProperties(callback: () => void, exception: () => void): void {
        this.inspectionProperties = [];
        this.loadPagedProperties(0, 1000, callback, exception);
    }

    private loadPagedProperties(skipCount: number, maxResultCount: number, callback: () => void, exception: () => void): void {
        this.integrationServiceProxy
            .getAllProperties(undefined, undefined, undefined, 'Name DESC', maxResultCount, skipCount)
            .subscribe({
                next: (response) => {
                    for (let item of response.items)
                        this.inspectionProperties.push(item);

                    if (response.totalCount > this.inspectionProperties.length) {
                        this.loadPagedProperties(this.inspectionProperties.length, 1000, callback, exception);
                    } else {
                        callback();
                    }
                },
                error: () => exception()
            });
    }

    private loadUsers(callback: () => void, exception: () => void): void {
        this.inspectionUsers = [];
        this.loadPagedUsers(0, 1000, callback, exception);
    }

    private loadPagedUsers(skipCount: number, maxResultCount: number, callback: () => void, exception: () => void): void {
        this.userServiceProxy
            .getAll(
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                'Name DESC',
                maxResultCount,
                skipCount)
            .subscribe({
                next: (response) => {

                    for (let item of response.items)
                        this.inspectionUsers.push(new InspectionUserDto().fromJS(item.user));

                    if (response.totalCount > this.inspectionProperties.length) {
                        this.loadPagedUsers(this.inspectionProperties.length, 1000, callback, exception);
                    } else {
                        callback();
                    }
                },
                error: () => exception()
            });
    }

    private loadInspectionTypes(callback: () => void, exception: () => void): void {
        this.inspectionTypeServiceProxy
            .getAll(undefined, true, undefined, 'Name DESC', 1000, 0)
            .subscribe({
                next: (response) => {

                    this.inspectionTypes = response.items.map((p) => new InspectionInspectionTypeDto().fromJS(p));

                    callback();
                },
                error: () => exception()
            });
    }

    private loadDocumentTypes(callback: () => void, exception: () => void): void {
        this.documentTypeServiceProxy
            .getAll({ maxResultCount: 1000, skipCount: 0 })
            .subscribe({
                next: (response) => {

                    this.inspectionDocumentTypes = response.items.map((p) => new InspectionDocumentTypeDto().fromJS(p));

                    callback();
                },
                error: () => exception()
            });
    }
}