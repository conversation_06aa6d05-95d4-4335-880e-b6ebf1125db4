import { ChangeDetector<PERSON><PERSON>, Component, Injector, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppAlarmTreatmentSignalRService } from '@core/services/alarm-treatment-signalr.service';
import { MonitoringAlarmStatus, MonitoringDto, MonitoringServiceProxy, MonitoringStatisticDto } from '@proxies/monitoring.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Subscription, finalize } from 'rxjs';
import { MonitoringEditComponent } from '../edit/edit.component';
import { AppConsts } from '@core/inheritance/app-core-consts';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { AppFileDownloadService } from '@core/services/file-download.service';

@Component({
    selector: 'app-monitoring-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class MonitoringDashboardComponent extends ViewComponent implements OnInit, OnD<PERSON>roy {

    private readonly monitoringServiceProxy: MonitoringServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;
    private readonly alarmTreatmentService: AppAlarmTreatmentSignalRService;
    private readonly ngZone: NgZone;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    status: 'starting' | 'connected' | 'disconnected' | 'reconnecting';
    description: string = '';
    statistics: MonitoringStatisticDto[];
    monitoringAlarmStatuses = {
        pending: MonitoringAlarmStatus.Pending,
        process: MonitoringAlarmStatus.Process,
        completed: MonitoringAlarmStatus.Completed
    };

    private subscription: Subscription;
    private statusSubscription: Subscription;
    private descriptionSubscription: Subscription;
    private notificationSubscription: Subscription;
    private advertisementSubscription: Subscription;
    private audio: any;
    private timer: ReturnType<typeof setTimeout>;

    constructor(injector: Injector) {
        super(injector);

        this.monitoringServiceProxy = injector.get(MonitoringServiceProxy);
        this.fileDownloadService = injector.get(AppFileDownloadService);
        this.alarmTreatmentService = injector.get(AppAlarmTreatmentSignalRService);
        this.ngZone = injector.get(NgZone);

        this.status = this.alarmTreatmentService.status;
    }

    ngOnInit(): void {
        this.statusSubscription = this.alarmTreatmentService.statusSubscription.subscribe((newValue) => {
            this.ngZone.run(() => {
                this.status = newValue;

                if (newValue == 'connected')
                    this.alarmTreatmentService.send();
            });
        });

        this.descriptionSubscription = this.alarmTreatmentService.exceptionSubscription.subscribe((newValue) => {
            this.ngZone.run(() => {
                this.description = newValue;
            });
        });

        this.notificationSubscription = this.alarmTreatmentService.notificationSubscription.subscribe((newValue) => {
            this.ngZone.run(() => {
                this.statistics = newValue;
                this.changeDetectorRef.detectChanges();
            });
        });

        this.advertisementSubscription = this.alarmTreatmentService.advertisementSubscription.subscribe((newValue) => {
            this.ngZone.run(() => {
                if (!(isNullEmptyOrWhiteSpace(newValue.sound) || newValue.sound == 'none')) {

                    if (this.audio === undefined || this.audio === null)
                        this.audio = new Audio(`${AppConsts.baseUrl}/assets/sounds/${newValue.sound}`);

                    this.audio.pause();
                    this.audio.src = `${AppConsts.baseUrl}/assets/sounds/${newValue.sound}`;
                    this.audio.play();
                }

                this.getData();
            });
        });

        this.alarmTreatmentService.init();
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
        this.statusSubscription?.unsubscribe();
        this.descriptionSubscription?.unsubscribe();
        this.notificationSubscription?.unsubscribe();
        this.advertisementSubscription?.unsubscribe();
        this.alarmTreatmentService.stop();
        this.audio?.pause();
        this.stopTimer();
    }

    editItem(item: MonitoringDto, status: MonitoringAlarmStatus): void {
        this.dialog.showWithData<boolean>({
            component: MonitoringEditComponent,
            componentProps: {
                id: item.id,
                status: status
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    completeItem(item: MonitoringDto): void {
        this.message.confirm('¿Está seguro de finalizar es estado real del tratamiento de la alarma seleccionada?', 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();

                this.monitoringServiceProxy
                    .complete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha finalizado es estado real del tratamiento de la alarma');
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.subscription?.unsubscribe();
        this.subscription = this.monitoringServiceProxy
            .getAll(
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                    this.startTimer();
                }
            });
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.monitoringServiceProxy
            .export(
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    showActions(event: any, item: MonitoringDto): void {
        this.popover.show(event, [
            {
                label: 'Dar tratamiento',
                permissions: [
                    'Pages.Management.Monitoring.Modify'
                ],
                hide: item.status == this.monitoringAlarmStatuses.completed,
                callback: () => this.editItem(item, MonitoringAlarmStatus.Process)
            },
            {
                label: 'Finalizar estado real',
                permissions: [
                    'Pages.Management.Monitoring.Modify'
                ],
                hide: item.canceled,
                callback: () => this.completeItem(item)
            },
            {
                label: 'Finalizar',
                permissions: [
                    'Pages.Management.Monitoring.Modify'
                ],
                hide: item.status == this.monitoringAlarmStatuses.completed,
                callback: () => this.editItem(item, MonitoringAlarmStatus.Completed)
            }
        ]);
    }

    private startTimer(): void {
        this.stopTimer();
        this.timer = setTimeout(() => this.getData(), 10_000);
    }

    private stopTimer(): void {
        clearTimeout(this.timer);
    }
}