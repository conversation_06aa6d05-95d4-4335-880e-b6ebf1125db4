import { Component, Injector, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { OperationTaskCreateEditComponent } from '../create-edit/create-edit.component';
import { finalize, Subscription } from 'rxjs';
import { OperationTaskDto, OperationTaskServiceProxy } from '@proxies/operation-task.proxy';
import { DateTime } from 'luxon';
import { Paginator } from 'primeng/paginator';
import { IntegrationServiceProxy } from '@proxies/integration.proxy';
import { IFilterOption } from '@core/models/filters';
import { AppFileDownloadService } from '@core/services/file-download.service';

@Component({
    selector: 'app-operation-task-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class OperationTaskDashboardComponent extends ViewComponent implements OnInit, On<PERSON><PERSON>roy {

    private readonly operationTaskServiceProxy: OperationTaskServiceProxy;
    private readonly integrationServiceProxy: IntegrationServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable!: Table;
    @ViewChild('paginator', { static: true }) paginator!: Paginator;

    areas!: IFilterOption<string>[];
    centers!: IFilterOption<string>[];
    managements!: IFilterOption<string>[];
    movements!: IFilterOption<string>[];
    hasIntegrationArray: IFilterOption<boolean>[] = [
        { label: 'Sí', value: true },
        { label: 'No', value: false },
    ];

    private subscription!: Subscription;

    constructor(injector: Injector) {
        super(injector);

        this.operationTaskServiceProxy = injector.get(OperationTaskServiceProxy);
        this.integrationServiceProxy = injector.get(IntegrationServiceProxy);
        this.fileDownloadService = injector.get(AppFileDownloadService);
    }

    async ngOnInit(): Promise<void> {

        this.integrationServiceProxy.getAllCostCenters().subscribe({
            next: (response) => this.areas = response.items.map((p) => ({ label: p.prcName, value: p.prcCode }))
        });

        this.integrationServiceProxy.getAllServiceCallLogisticCenters().subscribe({
            next: (response) => this.centers = response.items.map((p) => ({ label: p.name, value: p.code }))
        });

        this.integrationServiceProxy.getAllMasterManagements().subscribe({
            next: (response) => this.managements = response.items.map((p) => ({ label: p.prcName, value: p.prcCode }))
        });

        this.integrationServiceProxy.getAllMasterIncomeExpenses().subscribe({
            next: (response) => this.movements = response.items.map((p) => ({ label: p.prcName, value: p.prcCode }))
        });

    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    resetFilters(): void {
        this.dataTable?.clear();
        this.getData();
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.subscription?.unsubscribe();
        this.subscription = this.operationTaskServiceProxy
            .getAll({
                fromCreationTime: DateTime.fromJSDate(this.dataTable?.filters?.['fromCreationTime']?.['value']),
                toCreationTime: DateTime.fromJSDate(this.dataTable?.filters?.['toCreationTime']?.['value']),
                code: this.dataTable?.filters?.['code']?.['value'],
                projectName: this.dataTable?.filters?.['projectName']?.['value'],
                projectCode: this.dataTable?.filters?.['projectCode']?.['value'],
                locationName: this.dataTable?.filters?.['locationName']?.['value'],
                customerCode: this.dataTable?.filters?.['customerCode']?.['value'],
                customerName: this.dataTable?.filters?.['customerName']?.['value'],
                centerCode: this.dataTable?.filters?.['centerCode']?.['value'],
                centerName: this.dataTable?.filters?.['centerName']?.['value'],
                managementCode: this.dataTable?.filters?.['managementCode']?.['value'],
                managementName: this.dataTable?.filters?.['managementName']?.['value'],
                areaCode: this.dataTable?.filters?.['areaCode']?.['value'],
                areaName: this.dataTable?.filters?.['areaName']?.['value'],
                movementCode: this.dataTable?.filters?.['movementCode']?.['value'],
                movementName: this.dataTable?.filters?.['movementName']?.['value'],
                hasIntegration: this.dataTable?.filters?.['hasIntegration']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator),
                skipCount: this.table.getSkipCount(this.paginator)
            }).subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.operationTaskServiceProxy
            .export({
                fromCreationTime: DateTime.fromJSDate(this.dataTable?.filters?.['fromCreationTime']?.['value']),
                toCreationTime: DateTime.fromJSDate(this.dataTable?.filters?.['toCreationTime']?.['value']),
                code: this.dataTable?.filters?.['code']?.['value'],
                projectName: this.dataTable?.filters?.['projectName']?.['value'],
                projectCode: this.dataTable?.filters?.['projectCode']?.['value'],
                locationName: this.dataTable?.filters?.['locationName']?.['value'],
                customerCode: this.dataTable?.filters?.['customerCode']?.['value'],
                customerName: this.dataTable?.filters?.['customerName']?.['value'],
                centerCode: this.dataTable?.filters?.['centerCode']?.['value'],
                centerName: this.dataTable?.filters?.['centerName']?.['value'],
                managementCode: this.dataTable?.filters?.['managementCode']?.['value'],
                managementName: this.dataTable?.filters?.['managementName']?.['value'],
                areaCode: this.dataTable?.filters?.['areaCode']?.['value'],
                areaName: this.dataTable?.filters?.['areaName']?.['value'],
                movementCode: this.dataTable?.filters?.['movementCode']?.['value'],
                movementName: this.dataTable?.filters?.['movementName']?.['value'],
                hasIntegration: this.dataTable?.filters?.['hasIntegration']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator),
                skipCount: this.table.getSkipCount(this.paginator)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    showActions(event: any, data: OperationTaskDto) {
        this.popover.show(event, [
            {
                label: 'Visualizar',
                permissions: [
                    'Pages.Management.OperationTask'
                ],
                callback: () => this.showItem(data)
            },
            {
                label: 'Editar',
                permissions: [
                    'Pages.Management.OperationTask.Modify'
                ],
                callback: () => this.editItem(data)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Management.OperationTask.Delete'
                ],
                callback: () => this.deleteItem(data)
            }
        ]);
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: OperationTaskCreateEditComponent,
            componentProps: {
                editable: true
            }
        }).then((response) => {
            if (response.data.result)
                this.getData();
        });
    }

    showItem(item: OperationTaskDto): void {
        this.dialog.showWithData<boolean>({
            component: OperationTaskCreateEditComponent,
            componentProps: {
                id: item.id,
                editable: false
            }
        }).then((response) => {
            if (response.data.result)
                this.getData();
        });
    }

    editItem(item: OperationTaskDto): void {
        this.dialog.showWithData<boolean>({
            component: OperationTaskCreateEditComponent,
            componentProps: {
                id: item.id,
                editable: true
            }
        }).then((response) => {
            if (response.data.result)
                this.getData();
        });
    }

    deleteItem(item: OperationTaskDto): void {
        this.message.confirm('¿Está seguro de eliminar el registro seleccionado?', 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();

                this.operationTaskServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('El registro ha sido eliminado correctamente.');
                            this.getData();
                        }
                    });
            }
        });
    }
}