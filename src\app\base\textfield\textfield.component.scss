.form-group  {
  padding-bottom: 10px;
}
input {
  padding: 0;
  max-height: 20px!important;
}
.floating-label {
  position: relative;
  margin-bottom: 15px;
}

.floating-label input {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 8px 8px 4px;
  display: block;
  width: 100%;
  font-size: 14px;
  background: #fff;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.floating-label label {
  color: #6c757d;
  font-size: 14px;
  font-weight: normal;
  position: absolute;
  pointer-events: none;
  left: 4px;
  top: 8px;
  transition: 0.2s ease all;
}

.floating-label input:focus {
  border-color: #6c757d;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

.floating-label input:focus ~ label,
.floating-label input:not(:placeholder-shown) ~ label {
  top: -18px;
  font-size: 13px;
  color: #6c757d;
}

.floating-label input:not(:placeholder-shown) {
  padding-top: 20px;
}
