import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { AlarmTypeDto, AlarmTypeServiceProxy } from '@proxies/alarm-type.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { AlarmTypeCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-alarm-type-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class AlarmTypeDashboardComponent extends ViewComponent {

    private alarmTypeServiceProxy: AlarmTypeServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    hasProcessArray: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    hasRealArray: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    hasReasonArray: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    hasDescriptionArray: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    hasServiceCallArray: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmTypeServiceProxy = _injector.get(AlarmTypeServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: AlarmTypeCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: AlarmTypeDto) {
        this.dialog.showWithData<boolean>({
            component: AlarmTypeCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: AlarmTypeDto) {
        this.message.confirm(`¿Estas seguro de eliminar el tipo de alarma "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.alarmTypeServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el tipo de alarma satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.alarmTypeServiceProxy
            .getAll(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['hasProcess']?.['value'],
                this.dataTable?.filters?.['hasReal']?.['value'],
                this.dataTable?.filters?.['hasReason']?.['value'],
                this.dataTable?.filters?.['hasDescription']?.['value'],
                this.dataTable?.filters?.['hasServiceCall']?.['value'],
                this.dataTable?.filters?.['color']?.['value'],
                this.dataTable?.filters?.['background']?.['value'],
                this.dataTable?.filters?.['icon']?.['value'],
                this.dataTable?.filters?.['sound']?.['value'],
                this.dataTable?.filters?.['closeCode']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: AlarmTypeDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.AlarmType.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.AlarmType.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.alarmTypeServiceProxy
            .export(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['hasProcess']?.['value'],
                this.dataTable?.filters?.['hasReal']?.['value'],
                this.dataTable?.filters?.['hasReason']?.['value'],
                this.dataTable?.filters?.['hasDescription']?.['value'],
                this.dataTable?.filters?.['hasServiceCall']?.['value'],
                this.dataTable?.filters?.['color']?.['value'],
                this.dataTable?.filters?.['background']?.['value'],
                this.dataTable?.filters?.['icon']?.['value'],
                this.dataTable?.filters?.['sound']?.['value'],
                this.dataTable?.filters?.['closeCode']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}