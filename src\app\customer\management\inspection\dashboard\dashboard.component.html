<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Inspecciones
        </ion-title>

        <ion-buttons slot="end">

            <ion-button *ngIf="'Pages.Management.Inspection' | permission" (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.Inspection' | permission" (click)="getData()" class="ion-option me-2" color="tertiary" fill="solid">
                <ion-icon name="search"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.Inspection' | permission" id="excel-options__inspection" class="ion-option me-2" color="success" mode="ios" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

            <ion-popover #excelPopover class="dropdown-options" trigger="excel-options__inspection"
                triggerAction="click" mode="ios">
                <ng-template>
                    <ion-content mode="md">
                        <ion-list class="ion-no-padding">
                            <ion-item (click)="importInspections();excelPopover.dismiss();" button color="light" lines="full">
                                <ion-label class="fz-small" color="dark">
                                    Importar inspecciones
                                </ion-label>
                            </ion-item>
                            <ion-item (click)="export();excelPopover.dismiss();" button color="light"
                                lines="full">
                                <ion-label class="fz-small" color="dark">
                                    Exportar inspecciones
                                </ion-label>
                            </ion-item>
                            <ion-item (click)="exportProperties();excelPopover.dismiss();" button color="light"
                                lines="full">
                                <ion-label class="fz-small" color="dark">
                                    Exportar bodegas con contrato y sin inspección programada
                                </ion-label>
                            </ion-item>
                        </ion-list>
                    </ion-content>
                </ng-template>
            </ion-popover>

            <ion-button *ngIf="'Pages.Management.Inspection.Modify' | permission" (click)="createItem()" class="ion-option"
                color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar inspección
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>
                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px">
                                    Acciones
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    Código
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Centro logístico
                                </th>
                                <th style="min-width: 300px;">
                                    Cliente
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    Almacén
                                </th>
                                <th style="min-width: 250px; width: 250px; max-width: 250px;"
                                    pSortableColumn="InspectionTime">
                                    Fecha y hora programada
                                    <p-sortIcon field="InspectionTime"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px"
                                    pSortableColumn="AssignedUser.Name,AssignedUser.Surname,AssignedUser.SecondSurname">
                                    Asignado a
                                    <p-sortIcon
                                        field="AssignedUser.Name,AssignedUser.Surname,AssignedUser.SecondSurname"></p-sortIcon>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;"
                                    pSortableColumn="InspectionType.Name">
                                    Tipo de inspección
                                    <p-sortIcon field="InspectionType.Name"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    Estado
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    ¿Existe Riesgo?
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px" pSortableColumn="CreationTime">
                                    Creado el
                                    <p-sortIcon field="CreationTime"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    Creado por
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px" pSortableColumn="LastModificationTime">
                                    Actualizado el
                                    <p-sortIcon field="LastModificationTime"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    Actualizado por
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px">
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter type="text" field="code"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="centerIds" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="centers"
                                                [showHeader]="false" [showClear]="true" optionLabel="label"
                                                appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 300px;">
                                    <p-columnFilter type="text" field="customerName"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter type="text" field="propertyName"></p-columnFilter>
                                </th>
                                <th style="min-width: 250px; width: 250px; max-width: 250px;">
                                    <p-columnFilter field="inspectionStartTime" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <app-date-input name="inspectionStartTime" [value]="value" [showLabel]="false"
                                                placeholder="Desde" (onDateChange)="filter($event)" />
                                        </ng-template>
                                    </p-columnFilter>
                                    <div class="d-block my-1"></div>
                                    <p-columnFilter field="inspectionEndTime" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <app-date-input name="inspectionEndTime" [value]="value" [showLabel]="false"
                                                placeholder="Hasta" (onDateChange)="filter($event)" />
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px">
                                    <p-columnFilter type="text" field="assignedUser"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter field="inspectionTypeIds" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="inspectionTypes"
                                                [showHeader]="false" [showClear]="true" optionLabel="label"
                                                appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="inspectionModes" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="inspectionModeTypes"
                                                [showHeader]="false" [showClear]="true" optionLabel="label"
                                                appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="hasRiskInspection" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="hasRiskInspection"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    {{record?.code}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record?.center?.name}}
                                </td>
                                <td style="min-width: 300px; width: 300px; max-width: 300px;">
                                    {{record?.customer?.cardName}}
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record?.property?.name}}
                                </td>
                                <td style="min-width: 250px; width: 250px; max-width: 250px;">
                                    {{record?.inspectionTime | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record?.assignedUser?.name}} {{record?.assignedUser?.surname}} {{record?.assignedUser?.secondSurname}}
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    {{record?.inspectionType?.name}}
                                </td>
                                <td class="text-center" style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <ion-badge color="secondary" *ngIf="record.mode == inspectionModes.programed">
                                        Programada
                                    </ion-badge>
                                    <ion-badge color="warning" *ngIf="record.mode == inspectionModes.modified">
                                        Reprogramada
                                    </ion-badge>
                                    <ion-badge color="warning" *ngIf="record.mode == inspectionModes.modifiedWithoutDate">
                                        Reprogramada sin fecha
                                    </ion-badge>
                                    <ion-badge color="success" *ngIf="record.mode == inspectionModes.process">
                                        En proceso
                                    </ion-badge>
                                    <ion-badge color="tertiary" *ngIf="record.mode == inspectionModes.executed">
                                        Pendiente de revisión
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="record.mode == inspectionModes.canceled">
                                        Cancelada
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="record.mode == inspectionModes.contract">
                                        Cancelada por contrato
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="record.mode == inspectionModes.rejected">
                                        Rechazada
                                    </ion-badge>
                                    <ion-badge color="success" *ngIf="record.mode == inspectionModes.completed">
                                        Finalizada
                                    </ion-badge>
                                </td>
                                
								
								<td style="min-width: 150px; width: 150px; max-width: 150px; text-align: center;">
                                    {{ record?.hasRisk ? 'Sí' : 'No' }}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record?.creationTime | luxonFormat: 'dd/MM/yyyy HH:mm:ss'}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record?.creationUser?.name}} {{record?.creationUser?.surname}} {{record?.creationUser?.secondSurname}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record?.lastModificationTime | luxonFormat: 'dd/MM/yyyy HH:mm:ss'}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record?.lastModificationUser?.name}} {{record?.lastModificationUser?.surname}} {{record?.lastModificationUser?.secondSurname}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>
            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>