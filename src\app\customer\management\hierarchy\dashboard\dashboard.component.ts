import { Component, Injector, OnInit } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { HierarchyDto, HierarchyServiceProxy } from '@proxies/hierarchy.proxy';
import { MenuItem, TreeNode } from 'primeng/api';
import { HierarchyCreateEditComponent } from '../create-edit/create-edit.component';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-hierarchy-dashboard.component',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class HierarchyDashboardComponent extends ViewComponent implements OnInit {

    private hierarchyServiceProxy: HierarchyServiceProxy;

    nodes: TreeNode<HierarchyDto>[] = [];
    nodeCount: number;
    selectedNode: any;
    contextMenuItems: MenuItem[] = [
        {
            label: 'Editar',
            disabled: !this.permissions.isGranted('Pages.Management.Hierarchy.Modify'),
            command: (event) => {
                this.dialog.showWithData<boolean>({
                    component: HierarchyCreateEditComponent,
                    componentProps: {
                        id: this.selectedNode.data.id
                    }
                }).then((response) => {
                    if (response.data.result) {
                        this.getData();
                    }
                });
            },
        },
        {
            label: 'Agregar subjerarquía',
            disabled: !this.permissions.isGranted('Pages.Management.Hierarchy.Modify'),
            command: () => {
                this.dialog.showWithData<boolean>({
                    component: HierarchyCreateEditComponent,
                    componentProps: {
                        parentId: this.selectedNode.data.id,
                        parent: this.selectedNode.data
                    }
                }).then((response) => {
                    if (response.data.result) {
                        this.getData();
                    }
                });
            },
        },
        {
            label: 'Eliminar',
            disabled: !this.permissions.isGranted('Pages.Management.Hierarchy.Delete'),
            command: () => {
                this.message.confirm(
                    `¿Está seguro de eliminar la jerarquía "${this.selectedNode.data.name}"? Esta operación eliminará todos las subjerarquías de la jerarquía.`,
                    'Aviso',
                    (isConfirmed) => {
                        if (isConfirmed) {
                            this.hierarchyServiceProxy
                                .delete(this.selectedNode.data.id)
                                .subscribe(() => {
                                    this.notify.success('Jerarquía eliminada satisfactoriamente');
                                    this.selectedNode = null;
                                    this.getData();
                                });
                        }
                    }
                );
            },
        },
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.hierarchyServiceProxy = _injector.get(HierarchyServiceProxy);
    }

    ngOnInit(): void {
        this.getData();
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: HierarchyCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    async getData(): Promise<void> {
        this.hierarchyServiceProxy
            .getAll(undefined, undefined, "Name ASC", 1000, 0)
            .subscribe({
                next: (response) => {
                    this.nodes = [];
                    this.nodeCount = response.totalCount;

                    const parentNodes = response.items.filter(p => !p.parentId);

                    for (let node of parentNodes) {

                        this.nodes.push({
                            label: node.name,
                            icon: node.icon == 'none' ? null : `pi pi-${node.icon}`,
                            data: node,
                            expanded: true,
                            children: this.getChilds(response.items, node)
                        });

                    }
                }
            });
    }

    resetFilters(): void {
        this.selectedNode = undefined;
        this.getData();
    }

    async nodeDropped(event: any): Promise<void> {
        const loading = this.loader.show();

        this.hierarchyServiceProxy
            .change(
                event?.dragNode?.data?.id,
                this.isDroppingBetweenTwoNodes(event) ?
                    event?.dropNode?.parent?.data?.id :
                    event?.dropNode?.data?.id
            ).pipe(finalize(async () => (await loading).dismiss()))
            .subscribe({
                next: () => this.getData(),
                error: () => this.getData()
            });
    }

    nodeSelected(event: any) {

    }

    private getChilds(items: HierarchyDto[], parent: HierarchyDto): TreeNode<HierarchyDto>[] {
        let result: TreeNode<HierarchyDto>[] = [];
        let filterItems = items.filter(p => p.parentId == parent.id);

        for (let node of filterItems) {
            result.push({
                label: node.name,
                icon: node.icon == 'none' ? null : `pi pi-${node.icon}`,
                data: node,
                expanded: true,
                children: this.getChilds(items, node)
            });
        }

        return result;
    }

    isDroppingBetweenTwoNodes(event: any): boolean {
        return event.originalEvent.target.nodeName === 'LI';
    }
}