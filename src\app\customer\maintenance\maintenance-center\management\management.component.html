<ion-header mode="md">
    <ion-toolbar>

        <ion-buttons slot="start">
            <ion-button (click)="onBackButtonPressed()" class="ion-option me-2" fill="clear">
                <ion-icon name="chevron-back"></ion-icon>
            </ion-button>
        </ion-buttons>

        <ion-title class="fz-normal fw-bold">
            Asignar área de ubicación del centro<ng-container *ngIf="item && item.center">: 
                {{item?.center?.name}} ({{item?.center?.code}})
            </ng-container>
        </ion-title>

        <ion-buttons slot="end">

            <ion-button *ngIf="polygons.length > 0" (click)="clearPolygons()" class="ion-option me-2" color="danger" fill="solid">
                <ion-icon class="me-1" name="trash"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Eliminar área
                </ion-label>
            </ion-button>

            <ion-button *ngIf="'Pages.Maintenance.MaintenanceCenter.Path' | permission" (click)="save()" class="ion-option" color="primary" fill="solid">
                <ion-icon class="me-1" name="save"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Guardar
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <app-maintenance-center-detail 
        *ngIf="loaded" 
        [center]="item" 
        [apiKey]="apiKey" 
        [(polygons)]="polygons" />
</ion-content>