import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InspectionDashboardComponent } from './dashboard/dashboard.component';
import { InspectionImportComponent } from './import/import.component';

const routes: Routes = [
    {
        path: '',
        children: [
            { path: 'dashboard', component: InspectionDashboardComponent },
            { path: 'import', component: InspectionImportComponent },
            { path: '', pathMatch: 'full', redirectTo: 'dashboard' },
            { path: '**', redirectTo: 'dashboard' }
        ]
    }
];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class InspectionRoutingModule { }