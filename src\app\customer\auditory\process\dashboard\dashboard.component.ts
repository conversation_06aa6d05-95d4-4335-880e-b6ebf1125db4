import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { ProcessCreateEditComponent } from '../create-edit/create-edit-component';
import { IFilterOption } from '@core/models/filters';
import { MacroProcessServiceProxy } from '@proxies/auditory/macro-process.proxy';
import { ProcessDto, ProcessServiceProxy } from '@proxies/auditory/process.proxy';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class ProcessDashboardComponent extends ViewComponent implements OnInit {

    private macroProcessServiceProxy: MacroProcessServiceProxy;
    private processServiceProxy: ProcessServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    macroProcessArray: IFilterOption<string>[];

    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.processServiceProxy = _injector.get(ProcessServiceProxy);
        this.macroProcessServiceProxy = _injector.get(MacroProcessServiceProxy);
    }

    ngOnInit() {

    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: ProcessCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: ProcessDto): void {
        this.dialog.showWithData<boolean>({
            component: ProcessCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(obj: ProcessDto) {
        this.message.confirm(`¿Estas seguro de eliminar el proceso "${obj.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.processServiceProxy
                    .delete(obj.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: (response) => {
                            if(response.IsSuccess) {
                                this.notify.success('Se ha eliminado satisfactoriamente', 5000);
                                this.getData();   
                            }
                            else {
                                this.notify.error('Ocurrio un error en la eliminación.', 5000);
                            }
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.processServiceProxy
            .getAll(
                this.dataTable?.filters?.['Code']?.['value'],
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['MacroProcessId']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: ProcessDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Auditory.Process'
                ],
                callback: () => this.editItem(role)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Auditory.Process'
                ],
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    private loadFilters(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.macroProcessServiceProxy.getAllFilter().subscribe({
                next: (response) => {
                    this.macroProcessArray = this.parseFilter(response.items);
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }
}
