<div class="row">

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de reprogramaciones
        </label>
        <hr>
    </div>

    <div class="col-12">
        <p-table #dataTable sortMode="multiple" [value]="inspection?.inspectionModifieds"
            [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
            ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width: 130px; max-width: 130px; width: 130px">
                        Acciones
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        Motivo 
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        Fecha
                    </th>
                    <th style="min-width: 200px">
                        Comentarios
                    </th>
                    <th style="min-width: 200px; max-width: 200px; width: 200px">
                        Reprogramado el
                    </th>
                    <th style="min-width: 200px; max-width: 200px; width: 200px">
                        Reprogramado por
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr [hidden]="record.isHidden">
                    <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px">
                        <ion-button (click)="showActions($event, record, rowIndex)" class="ion-action" mode="ios" color="primary"
                            fill="solid" size="small">
                            <ion-label class="fz-small">
                                Acciones
                            </ion-label>
                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                        </ion-button>
                    </td>
                    <td style="min-width: 150px; max-width: 150px; width: 150px">
                        <ion-badge color="primary" *ngIf="record.type == inspectionModes.internal">
                            Por BSF
                        </ion-badge>
                        <ion-badge color="secondary" *ngIf="record.type == inspectionModes.external">
                            A solicitud del cliente
                        </ion-badge>
                        <ion-badge color="tertiary" *ngIf="record.type == inspectionModes.propertyUnavailable">
                            Bodega cerrada
                        </ion-badge>
                    </td>
                    <td class="action-column" style="min-width: 150px; max-width: 150px; width: 150px">
                        {{record?.inspectionTime | luxonFormat: 'dd/MM/yyyy'}}
                    </td>
                    <td style="min-width: 200px">
                        {{record?.observation}}
                    </td>
                    <td style="min-width: 200px; max-width: 200px; width: 200px">
                        {{record?.creationTime | luxonFormat: 'yyyy/MM/dd HH:mm:ss'}}
                    </td>
                    <td style="min-width: 200px; max-width: 200px; width: 200px">
                        {{record?.creationUser?.name}} {{record?.creationUser?.surname}} {{record?.creationUser?.secondSurname}}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
        <div class="primeng-paging-container">
            <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator (onPageChange)="getData($event)"
                [totalRecords]="table.totalRecordsCount" [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                [showCurrentPageReport]="true" dropdownAppendTo="body"
                currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
            </p-paginator>
        </div>
    </div>

</div>