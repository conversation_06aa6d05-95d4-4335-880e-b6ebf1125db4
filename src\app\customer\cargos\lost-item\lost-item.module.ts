import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LostItemRoutingModule } from './lost-item.routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { ThemeModule } from '@theme/theme.module';
import { IonicModule } from '@ionic/angular';
import { CoreModule } from '@core/core.module';
import { LostItemDashboardComponent } from './dashboard/dashboard.component';
import { LostItemCreateComponent } from './create/create.component';
import { PersonRoutingModule } from '../../management/person/person.routing.module';
import { CargoGeneralInformationComponent } from './create/general-information/general-information.component';
import { CargoDocumentInformationComponent } from './create/document-information/document-information.component';
import { ComponentModule } from '@components/component.module';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { PersonModule } from '../../management/person/person.module';

@NgModule({
  imports: [
    LostItemRoutingModule,
    PersonRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule,
    DialogModule,
    ButtonModule,
    PersonModule
  ],
  declarations: [
    LostItemDashboardComponent,
    LostItemCreateComponent,
    CargoGeneralInformationComponent,
    CargoDocumentInformationComponent
  ]
})
export class LostItemModule { }
