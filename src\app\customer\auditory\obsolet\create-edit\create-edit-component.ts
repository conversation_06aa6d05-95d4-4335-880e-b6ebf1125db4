import { Component, Injector, Input, OnInit, NgModule, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AbstractControl, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Status, DocumentDto, DocumentStatusDto, TagDto, DocumentTypeDto, StatusDto, MacroProcessDto, ProcessDto, SubProcessDto, DocumentServiceProxy, DocumentModifierDto, DocumentTagDto, DocumentResource, DocumentTypeFormat } from '@proxies/auditory/document.proxy';
import { AreaPositionDto, ProcessTierDto, Tier } from '@proxies/auditory/area-position.proxy'
import { UserDto } from '@proxies/user.proxy';
import { IntegrationServiceProxy, IntegrationAreaSigDto, IntegrationManagementSigDto } from '@proxies/integration.proxy';
import { UploadResource } from '@core/utils/core.request';
import { finalize } from 'rxjs';
import { AppDocumentPreviewComponent } from '@components/file-preview-pdf/file-preview-pdf.component';
import { AppAuditoryService } from '@core/services/auditory/auditory.service';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { DocumentNotificationDto } from '@proxies/auditory/document-notification.proxy';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class DocumentCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;
    @Input() code: string;
    @Input() onlyview: boolean = false;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    private documentServiceProxy: DocumentServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private auditoryDocumentService: AppAuditoryService;
    private formBuilder: FormBuilder;

    title: string = "Visualizar";
    size: number = 15_728_640;
    uploadResources: UploadResource[] = [];
    item: DocumentDto = new DocumentDto();
    disabled: boolean = false;
    types: DocumentTypeDto[] = [];
    statuses: StatusDto[];
    macroProcesses: MacroProcessDto[] = [];
    processes: ProcessDto[] = [];
    subProcesses: SubProcessDto[] = [];
    users: UserDto[] = [];
    areas: IntegrationAreaSigDto[] = [];
    tags: TagDto[];
    managements: IntegrationManagementSigDto[];
    loaded: boolean = false;
    isOnPreview: boolean = false;

    modalForm!: FormGroup;

    processControl: boolean
    processTier: ProcessTierDto | undefined;
    availableUsers: DocumentModifierDto[] = [];
    availableNotification: DocumentNotificationDto[] = [];
    availableTags: DocumentTagDto[] = [];
    tableStatuses: DocumentStatusDto[] = [];
    locationTypes: string[] = ["VIRTUAL", "FÍSICO"];
    provisionTypes: string[] = ["ARCHIVAMIENTO", "ELIMINACIÓN"];

    get documentName(): AbstractControl {
        return this.modalForm.controls['documentNameInput'];
    };

    get documentCode(): AbstractControl {
        return this.modalForm.controls['documentCodeInput'];
    };

    get documentVersion(): AbstractControl {
        return this.modalForm.controls['documentVersionInput'];
    };

    get documentCreatedBy(): AbstractControl {
        return this.modalForm.controls['documentCreatedByInput'];
    };

    get documentStatus(): AbstractControl {
        return this.modalForm.controls['documentStatusSelect'];
    };

    get documentArea(): AbstractControl {
        return this.modalForm.controls['documentAreaSelect'];
    };

    get documentManagement(): AbstractControl {
        return this.modalForm.controls['documentManagementSelect'];
    };

    get documentType(): AbstractControl {
        return this.modalForm.controls['documentTypeSelect'];
    };

    get documentMacroProcess(): AbstractControl {
        return this.modalForm.controls['documentMacroProcessSelect'];
    };

    get documentProcess(): AbstractControl {
        return this.modalForm.controls['documentProcessSelect'];
    };

    get documentSubProcess(): AbstractControl {
        return this.modalForm.controls['documentSubProcessSelect'];
    };

    get documentModifier(): AbstractControl {
        return this.modalForm.controls['documentModifierSelect'];
    };

    get documentNotification(): AbstractControl {
        return this.modalForm.controls['documentNotificationSelect'];
    };

    get documentTag(): AbstractControl {
        return this.modalForm.controls['documentTagSelect'];
    };

    get documentLocation(): AbstractControl {
        return this.modalForm.controls['documentLocationInput'];
    };

    get documentLocationType(): AbstractControl {
        return this.modalForm.controls['documentLocationTypeSelect'];
    };

    get documentProvision(): AbstractControl {
        return this.modalForm.controls['documentProvisionSelect'];
    };

    get documentRetentionTime(): AbstractControl {
        return this.modalForm.controls['documentRetentionTimeInput'];
    };

    constructor(_injector: Injector, private router: Router) {
        super(_injector);

        this.documentServiceProxy = _injector.get(DocumentServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.auditoryDocumentService = _injector.get(AppAuditoryService);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            documentRelInput: [{ value: '', disabled: true }],
            documentTypeSelect: [{ value: '-1', disabled: true }, Validators.compose([Validators.required])],
            documentStatusSelect: [{ value: '1', disabled: true }, Validators.compose([Validators.required])],
            documentManagementSelect: [{ value: '-1', disabled: true }, Validators.compose([Validators.required])],
            documentMacroProcessSelect: [{ value: '-1', disabled: true }, Validators.compose([Validators.required])],
            documentProcessSelect: [{ value: '-1', disabled: true }, Validators.compose([Validators.required])],
            documentSubProcessSelect: [{ value: '-1', disabled: true }, Validators.compose([Validators.required])],
            documentAreaSelect: [{ value: '-1', disabled: true }, Validators.compose([Validators.required])],
            documentNameInput: [{ value: '', disabled: true }, Validators.compose([Validators.required, Validators.maxLength(100)])],
            documentCodeInput: [{ value: '', disabled: true }, Validators.compose([])],
            documentVersionInput: [{ value: '1', disabled: true }, Validators.compose([])],
            documentLocationTypeSelect: ['-1'],
            documentLocationInput: [''],
            documentRetentionTimeInput: [''],
            documentProvisionSelect: ['-1'],
            documentTagSelect: [this.availableTags],
            documentNotificationSelect: [this.availableNotification],
            documentModifierSelect: [this.availableUsers],
            documentCreatedByInput: [{ value: this.session?.user?.name + ' ' + this.session?.user?.surname, disabled: true }, Validators.compose([])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.processControl = this.session?.processTier?.find(x => x.processid == 0) != null;
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (this.onlyview) {
            this.modalForm.disable();
        }

        this.documentServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    this.item = response;
                    this.processTier = this.session?.processTier?.find(x => x.processid.toString() == this.item.documentProcess.processid);
                    const documentprocess = this.processes.filter(x => x.id == this.item.documentProcess.processid)[0];
                    this.documentName.setValue(this.item.name || '');
                    this.documentCode.setValue(this.item.code || '');
                    this.documentType.setValue(this.item.documentTypeId || '-1');
                    this.documentStatus.setValue(Status.WorkingOn);
                    this.documentManagement.setValue(this.item.documentManagementCode || '-1');
                    this.documentArea.setValue(this.item.documentAreaCode || '-1');
                    this.documentMacroProcess.setValue(documentprocess.macroprocessid || '-1');
                    this.documentProcess.setValue(documentprocess.id || '-1');
                    this.documentSubProcess.setValue(this.item.documentProcessId || '-1');
                    this.documentVersion.setValue(this.id && !this.onlyview ? (this.item.version + 1) : this.item.version || '1');
                    this.documentLocation.setValue(this.item.location)
                    this.documentLocationType.setValue(this.item.locationType)
                    this.documentProvision.setValue(this.item.provision)
                    this.documentRetentionTime.setValue(this.item.retentionTime)

                    if (this.processTier?.tiers?.includes(Tier.Owner) || this.processControl) {
                        this.table.records = this.getDataStatus(this.item.documentStatuses.slice(0, 10))
                        this.table.totalRecordsCount = this.item.documentStatuses.length
                    }
                    else {
                        let resultado = Object.values(
                            this.item.documentStatuses.reduce((acc, item) => {
                                if (
                                    !acc[item.statusid] ||
                                    item.createdat > acc[item.statusid].createdat
                                ) {
                                    acc[item.statusid] = item;
                                }
                                return acc;
                            }, {} as Record<number, DocumentStatusDto>)
                        );
                        resultado = resultado.filter(x =>
                            x.statusid == Status.Approved ||
                            x.statusid == Status.Reviewed ||
                            x.statusid == Status.WorkingOn)
                        this.item.documentStatuses = resultado;
                        this.table.records = this.getDataStatus(this.item.documentStatuses.slice(0, 10))
                        this.table.totalRecordsCount = this.item.documentStatuses.length
                    }

                    for (let tagdata of this.item.documentTags) {
                        tagdata.name = this.tags.find(tag => tag.id === tagdata.tagid)?.name || '';
                        this.availableTags.push(tagdata);
                    }
                    this.documentTag.setValue(this.availableTags);
                    for (let modifierData of this.item.documentModifiers) {
                        modifierData.name = this.users.find(user => user.id === modifierData.id)?.emailAddress || '';
                        this.availableUsers.push(modifierData);
                    }
                    this.documentModifier.setValue(this.availableUsers);

                    this.availableNotification = this.item.documentNotifications;
                    this.documentNotification.setValue(this.availableNotification);

                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    getData(event?: TableLazyLoadEvent): void {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        let take = this.table.getMaxResultCount(this.paginator, event);
        let skip = this.table.getSkipCount(this.paginator, event);

        this.tableStatuses = [...this.item.documentStatuses];
        this.table.totalRecordsCount = this.tableStatuses.length;
        this.table.records = this.getDataStatus(this.tableStatuses.slice(skip, take + skip));
    }

    getDataStatus(data: DocumentStatusDto[]): any {
        return data.map(x => {
            let usr = this.auditoryDocumentService.users?.find(y => y.emailAddress == x.createdby)
            return {
                ...x,
                status: this.auditoryDocumentService.filter?.statuses?.find(y => y.id == x.statusid)?.name,
                user: usr?.name + ' ' + usr?.surname
            }
        });
    }

    private getInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.documentServiceProxy
                .getInputs()
                .subscribe({
                    next: (response) => {
                        this.macroProcesses = response.macroProcess;
                        this.subProcesses = response.subProcess;
                        this.processes = response.process;
                        this.tags = response.tags;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private getAreasAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllAreaSig().subscribe({
                next: (response) => {
                    this.areas = response.items;
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private getManagementAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllManagementSig().subscribe({
                next: (response) => {
                    this.managements = response.items;
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private async loadInputs(): Promise<void> {
        await Promise.all([
            this.getInputs(),
            this.getAreasAsync(),
            this.getManagementAsync()
        ]);
        this.types = this.auditoryDocumentService.filter.documentTypes;
        this.statuses = this.auditoryDocumentService.filter.statuses;
        this.users = this.auditoryDocumentService.users;
    }

    loadResource(): void {
        let filename = this.item.code + " - " + this.item.name + '.' + this.item.filePath.split('.').pop();
        this.onPreviewFile(this.documentServiceProxy
            .getResource(this.item.fileName, filename, DocumentResource.Pdf));
    }

    downloadResource(): void {
        let filename = this.item.code + " - " + this.item.name + '.' + this.item.filePath.split('.').pop();
        window.open(this.documentServiceProxy
            .getResource(this.item.fileName, filename, DocumentResource.Original), '_blank');
    }

    get viewDocumentRelWithId() {
        const selectedValue = this.documentType?.value;
        return this.types.find(x => x.id == selectedValue)?.format == DocumentTypeFormat.Related.toString();
    }

    get processSelect() {
        const selectedValue = this.documentMacroProcess?.value;
        return this.processes.filter(x => x.macroprocessid == selectedValue);
    }

    get subProcessSelect() {
        const selectedValue = this.documentProcess?.value;
        return this.subProcesses.filter(x => x.processid == selectedValue);
    }

    get areasSelect() {
        const selectedValue = this.documentManagement?.value;
        return this.areas.filter(x => x.managementSigQuery?.code == selectedValue);
    }

    showDownload(): boolean {
        return this.processControl ||
            this.processTier != null ||
            this.item.documentModifiers.find(x => x.email == this.session?.user?.emailAddress) != null
    }

    onPreviewFile(url: string): void {
        if (this.isOnPreview)
            return;

        this.isOnPreview = true;
        this.dialog.show({
            component: AppDocumentPreviewComponent,
            cssClass: 'transparent',
            componentProps: {
                url: url,
                fileType: 'pdf',
                disableRightClick: !this.processControl && !this.processTier?.tiers?.includes(Tier.Owner)
            }
        }).then(() => this.isOnPreview = false)
            .catch(() => this.isOnPreview = false);
    }

}