import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { AlarmTypeRoutingModule } from './alarm-type.routing.module';
import { AlarmTypeDashboardComponent } from './dashboard/dashboard.component';
import { AlarmTypeCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    AlarmTypeRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    AlarmTypeDashboardComponent,
    AlarmTypeCreateEditComponent
  ]
})
export class AlarmTypeModule { }
