import { Component, Injector, Input, OnInit, NgModule, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { UserDto } from '@proxies/user.proxy';
import { IntegrationAreaSigDto, IntegrationManagementSigDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { AreaPositionDto, ProcessTierDto, Tier } from '@proxies/auditory/area-position.proxy'
import { finalize } from 'rxjs';
import { DocumentExternalDto, DocumentExternalServiceProxy, DocumentTypeDto, MacroProcessDto, ProcessDto, SubProcessDto } from '@proxies/auditory/document-external.proxy';

@Component({
    selector: 'app-create-edit-document-external',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class DocumentExternalCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;
    @Input() onlyview: boolean = false;

    private documentExternalServiceProxy: DocumentExternalServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private formBuilder: FormBuilder;

    processControl: boolean
    processTier: ProcessTierDto | undefined;

    item: DocumentExternalDto = new DocumentExternalDto();
    disabled: boolean = false;
    macroProcesses: MacroProcessDto[] = [];
    processes: ProcessDto[] = [];
    subProcesses: SubProcessDto[] = [];
    users: UserDto[];
    loaded: boolean = false;
    isOnPreview: boolean = false;
    locationTypes: string[] = ["VIRTUAL", "FÍSICO"];
    provisionTypes: string[] = ["ARCHIVAMIENTO", "ELIMINACIÓN"];

    modalForm!: FormGroup;

    get documentName(): AbstractControl {
        return this.modalForm.controls['documentNameInput'];
    };

    get documentEdition(): AbstractControl {
        return this.modalForm.controls['documentEditionInput'];
    };

    get documentLdr(): AbstractControl {
        return this.modalForm.controls['documentLdrCheck'];
    };

    get documentRetentionTime(): AbstractControl {
        return this.modalForm.controls['documentRetentionTimeInput'];
    };

    get documentLocation(): AbstractControl {
        return this.modalForm.controls['documentLocationInput'];
    };

    get documentLocationType(): AbstractControl {
        return this.modalForm.controls['documentLocationTypeSelect'];
    };

    get documentProvision(): AbstractControl {
        return this.modalForm.controls['documentProvisionSelect'];
    };

    get documentMacroProcess(): AbstractControl {
        return this.modalForm.controls['documentMacroProcessSelect'];
    };

    get documentProcess(): AbstractControl {
        return this.modalForm.controls['documentProcessSelect'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.documentExternalServiceProxy = _injector.get(DocumentExternalServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            documentManagementSelect: ['-1', Validators.compose([Validators.required])],
            documentAreaSelect: ['-1', Validators.compose([Validators.required])],
            documentMacroProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentTypeSelect: ['-1'],
            documentProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(100)])],
            documentEditionInput: ['', Validators.compose([Validators.required, Validators.maxLength(100)])],
            documentRetentionTimeInput: ['', Validators.compose([Validators.required])],
            documentLocationTypeSelect: ['-1'],
            documentLocationInput: [''],
            documentProvisionSelect: ['-1'],
            documentLdrCheck: [false]
        });
    }

    async ngOnInit(): Promise<void> {
        this.processControl = this.session?.processTier?.find(x => x.processid == 0) != null;
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (this.onlyview) {
            this.modalForm.disable();
        }

        if (!this.id) {
            if (!this.processControl) {
                let currentProcessForTier: number[] = []
                currentProcessForTier = this.session?.processTier?.filter(x => x.tiers.find(y => y == Tier.Owner) != null)?.map(x => x.processid);
                this.processes = this.processes.filter(x => currentProcessForTier.includes(parseInt(x.id)))
                this.macroProcesses = this.macroProcesses.filter(x => this.processes.find(y => y.macroprocessid == x.id) != null);    
            }
            this.disabled = false;
            await loading.dismiss();
            this.loaded = true;
        }

        this.documentExternalServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    this.item = response;
                    this.processTier = this.session?.processTier?.find(x => x.processid == this.item.processId && x.tiers.length > 0);
                    const documentprocess = this.processes.filter(x => x.id == this.item.processId.toString())[0];
                    this.documentName.setValue(this.item.name.toUpperCase() || '');
                    this.documentMacroProcess.setValue(documentprocess.macroprocessid || '-1');
                    this.documentProcess.setValue(documentprocess.id || '-1');
                    this.documentLdr.setValue(this.item.ldr);
                    this.documentEdition.setValue(this.item.edition);
                    this.documentLocation.setValue(this.item.location);
                    this.documentLocationType.setValue(this.item.locationType)
                    this.documentProvision.setValue(this.item.provision);
                    this.documentRetentionTime.setValue(this.item.retentionTime);
                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {
        this.item.name = this.documentName.value.toUpperCase();
        this.item.edition = this.documentEdition.value.toUpperCase();
        this.item.processId = this.documentProcess.value;
        this.item.location = (this.documentLocation.value ?? "").toUpperCase();
        this.item.locationType = this.documentLocationType.value;
        this.item.provision = this.documentProvision.value;
        this.item.retentionTime = this.documentRetentionTime.value;
        this.item.ldr = this.documentLdr.value;

        if (!this.item.processId) {
            this.message.info('El proceso es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.edition) {
            this.message.info('La edición es obligatoria', 'Aviso');
            return;
        }

        if (!this.item.retentionTime) {
            this.message.info('El tiempo de retención es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.provision) {
            this.message.info('La disposición final es obligatoria', 'Aviso');
            return;
        }

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.documentExternalServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Documento actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.documentExternalServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Documento creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private getInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.documentExternalServiceProxy
                .getFilters()
                .subscribe({
                    next: (response) => {
                        this.macroProcesses = response.macroprocess;
                        this.subProcesses = response.subprocess;
                        this.processes = response.process;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private async loadInputs(): Promise<void> {
        await Promise.all([
            this.getInputs()
        ]);
    }

    get processSelect() {
        const selectedValue = this.documentMacroProcess?.value;
        return this.processes.filter(x => x.macroprocessid == selectedValue);
    }
}