import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { InspectionDto, InspectionModifiedType, InspectionModifyDto } from '@proxies/inspection.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { InspectionShowModifiedInformationComponent } from './show-modified-information/show-modified-information.component';

@Component({
    selector: 'app-inspection-modified-information',
    templateUrl: 'modified-information.component.html',
    styleUrls: [
        'modified-information.component.scss'
    ]
})
export class InspectionModifiedInformationComponent extends ViewComponent implements OnInit {

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;
    
    @Input() inspection!: InspectionDto;
    @Input() completed!: boolean;
    
    inspectionModes = {
        none: InspectionModifiedType.None,
        internal: InspectionModifiedType.Internal,
        external: InspectionModifiedType.External,
        propertyUnavailable: InspectionModifiedType.PropertyUnavailable
    };

    private skipCount: number;
    private maxResultCount: number;

    constructor(_injector: Injector) {
        super(_injector);

        this.skipCount = 0;
        this.table.defaultRecordsCountPerPage = 5;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;
    }

    ngOnInit(): void {
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    showItem(item: InspectionModifyDto, index: number): void {
        this.dialog.show({
            component: InspectionShowModifiedInformationComponent,
            componentProps: {
                inspection: this.inspection,
                inspectionModify: item,
                index: index
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    showActions(event: any, item: InspectionModifyDto, index: number) {
        this.popover.show(event, [
            {
                label: 'Ver detalles',
                callback: () => this.showItem(item, index)
            }
        ]);
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        for (let item of this.inspection.inspectionModifieds) {
            item.isHidden = true;

            if (index >= skipCount && result < maxResultCount) {
                item.isHidden = false;
                result++;
            }

            index++;
        }

        this.table.totalRecordsCount = index;
    }
}