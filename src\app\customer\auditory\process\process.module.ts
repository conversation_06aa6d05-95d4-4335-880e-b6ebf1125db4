import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProcessRoutingModule } from './process.routing.module';
import { ProcessDashboardComponent } from './dashboard/dashboard.component';
import { ProcessCreateEditComponent } from './create-edit/create-edit-component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';

@NgModule({
  imports: [
    ProcessRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    ProcessDashboardComponent,
    ProcessCreateEditComponent
  ]
})
export class ProcessModule { }