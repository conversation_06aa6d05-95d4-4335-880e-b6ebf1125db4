<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar familia' : 'Crear familia'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceFamilyName" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceFamilyName.invalid && (maintenanceFamilyName.touched || maintenanceFamilyName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceFamilyName" name="MaintenanceFamilyName"
                                formControlName="maintenanceFamilyNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceFamilyCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="MaintenanceFamilyCode" name="MaintenanceFamilyCode"
                                formControlName="maintenanceFamilyCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>