import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace, isValidNumber } from '@core/utils/tools';
import { MaintenanceProgramParameterDto, MaintenanceProgramParameterServiceProxy, MaintenanceProgramParameterType } from '@proxies/maintenance-program-parameter.proxy';
import { finalize, max } from 'rxjs';

const MaintenanceProgramParameterDefaultPrecision = 2;

@Component({
    selector: 'app-maintenance-program-parameter-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MaintenanceProgramParameterCreateEditComponent extends ViewComponent implements OnInit {

    private maintenanceProgramParameterServiceProxy: MaintenanceProgramParameterServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: MaintenanceProgramParameterDto = new MaintenanceProgramParameterDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get maintenanceProgramParameterName(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterNameInput'];
    };

    get maintenanceProgramParameterEnabled(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterEnabledSelect'];
    };

    get maintenanceProgramParameterRequired(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterRequiredSelect'];
    };

    get maintenanceProgramParameterHasPrecision(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterHasPrecisionSelect'];
    };

    get maintenanceProgramParameterPrecision(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterPrecisionSelect'];
    };

    get maintenanceProgramParameterType(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterTypeSelect'];
    };

    get maintenanceProgramParameterMin(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterMinInput'];
    };

    get maintenanceProgramParameterMax(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterMaxInput'];
    };

    get maintenanceProgramParameterQuantity(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterQuantityInput'];
    };

    get maintenanceProgramParameterCode(): AbstractControl {
        return this.modalForm.controls['maintenanceProgramParameterCodeInput'];
    };

    avaliablePrecisions: number[] = [1, 2, 3, 4, 5, 6];

    maintenanceProgramParameters = {
        none: MaintenanceProgramParameterType.None,
        text: MaintenanceProgramParameterType.Text,
        numeric: MaintenanceProgramParameterType.Numeric,
        range: MaintenanceProgramParameterType.Range,
        file: MaintenanceProgramParameterType.File,
        image: MaintenanceProgramParameterType.Image
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceProgramParameterServiceProxy = _injector.get(MaintenanceProgramParameterServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            maintenanceProgramParameterNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            maintenanceProgramParameterEnabledSelect: ['true', [Validators.required]],
            maintenanceProgramParameterRequiredSelect: ['true', [Validators.required]],
            maintenanceProgramParameterHasPrecisionSelect: ['true', [Validators.required]],
            maintenanceProgramParameterPrecisionSelect: [MaintenanceProgramParameterDefaultPrecision, [Validators.required]],
            maintenanceProgramParameterTypeSelect: [this.maintenanceProgramParameters.none, [Validators.required]],
            maintenanceProgramParameterMinInput: ['', []],
            maintenanceProgramParameterMaxInput: ['', []],
            maintenanceProgramParameterQuantityInput: ['', [Validators.required]],
            maintenanceProgramParameterCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.maintenanceProgramParameterServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.maintenanceProgramParameterName.setValue(this.item.name);
                        this.maintenanceProgramParameterEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.maintenanceProgramParameterHasPrecision.setValue(this.item.hasPrecision ? 'true' : 'false');
                        this.maintenanceProgramParameterPrecision.setValue(this.item.precision || 2);
                        this.maintenanceProgramParameterType.setValue(this.item.type || this.maintenanceProgramParameters.none);
                        this.maintenanceProgramParameterMin.setValue(this.item.min);
                        this.maintenanceProgramParameterMax.setValue(this.item.max);
                        this.maintenanceProgramParameterQuantity.setValue(this.item.quantity);
                        this.maintenanceProgramParameterCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new MaintenanceProgramParameterDto().fromJS({});
            this.item.hasPrecision = true;
            this.item.precision = 2;
        }
    }

    onTypeChange(event: any): void {
        this.item.type = event.target.value;
        
        this.maintenanceProgramParameterMin.setValue('');
        this.maintenanceProgramParameterMax.setValue('');
        this.maintenanceProgramParameterQuantity.setValue('');
    }

    onHasPrecisionChange(event: any): void {
        this.item.hasPrecision = event.target.value == 'true';

        if (this.item.hasPrecision) {
            this.item.precision = MaintenanceProgramParameterDefaultPrecision;
            this.maintenanceProgramParameterPrecision.setValue(MaintenanceProgramParameterDefaultPrecision);
        }

        this.maintenanceProgramParameterMin.setValue('');
        this.maintenanceProgramParameterMax.setValue('');
        this.maintenanceProgramParameterQuantity.setValue('');
    }

    onPresicionChange(event: any): void {
        this.item.precision = +event.target.value;

        this.maintenanceProgramParameterMin.setValue('');
        this.maintenanceProgramParameterMax.setValue('');
        this.maintenanceProgramParameterQuantity.setValue('');
    }

    async save(): Promise<void> {

        this.item.name = this.maintenanceProgramParameterName.value;
        this.item.enabled = this.maintenanceProgramParameterEnabled.value == 'true';
        this.item.type = this.maintenanceProgramParameterType.value;
        this.item.code = this.maintenanceProgramParameterCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }
        if (this.item.type == this.maintenanceProgramParameters.none) {
            this.message.info('El tipo es obligatorio', 'Aviso');
            return;
        }
        if (this.item.type == this.maintenanceProgramParameters.numeric || this.item.type == this.maintenanceProgramParameters.range) {

            this.item.hasPrecision = this.maintenanceProgramParameterHasPrecision.value == 'true';

            if (this.item.hasPrecision) {
                const precisionString: string = this.maintenanceProgramParameterPrecision.value;
                let precisionValue: number = +precisionString;

                if (!isValidNumber(precisionString) || isNaN(precisionValue)) {
                    this.message.info('La cantidad de decimales es obligatoria es obligatorio', 'Aviso');
                    return;
                }

                this.item.precision = precisionValue;

            } else {
                this.item.precision = undefined;
            }

            const minString: string = this.maintenanceProgramParameterMin.value;

            if (!isNullEmptyOrWhiteSpace(minString)) {

                let minValue: number = +minString;

                if (!isValidNumber(minString) || isNaN(minValue)) {
                    this.message.info('El valor mínimo ingresado es inválido.', 'Aviso');
                    return;
                }

                this.item.min = minValue;
            }

            const maxString: string = this.maintenanceProgramParameterMax.value;

            if (!isNullEmptyOrWhiteSpace(minString)) {

                let maxValue: number = +maxString;

                if (!isValidNumber(maxString) || isNaN(maxValue)) {
                    this.message.info('El valor máximo ingresado es inválido.', 'Aviso');
                    return;
                }

                this.item.max = maxValue;
            }
        }

        if (this.item.type == this.maintenanceProgramParameters.image || this.item.type == this.maintenanceProgramParameters.file) {

            const quantityString: string = this.maintenanceProgramParameterMin.value;

            let quantityValue: number = +quantityString;

            if (!isValidNumber(quantityString) || isNaN(quantityValue)) {
                this.message.info('La cantidad ingresada es inválida.', 'Aviso');
                return;
            }

            this.item.quantity = quantityValue;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.maintenanceProgramParameterServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.maintenanceProgramParameterServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}