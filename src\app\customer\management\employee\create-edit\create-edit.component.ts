import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { EmployeeDto, EmployeeServiceProxy } from '@proxies/employee.proxy';
import { IntegrationCenterLogisticDto, IntegrationProfitCenterDto } from '@proxies/integration.proxy';
import { finalize } from 'rxjs';

const enum EmployeeIndexes {
    GeneralInformation,
    Permissions
}

interface IActionToggle<T> {
    data: T;
    checked: boolean;
}

@Component({
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class EmployeeCreateEditComponent extends ViewComponent implements OnInit {

    private employeeServiceProxy: EmployeeServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    activeIndex: EmployeeIndexes = EmployeeIndexes.GeneralInformation;
    disabled!: boolean;
    modalForm!: FormGroup;
    item!: EmployeeDto;
    areas!: IntegrationProfitCenterDto[];
    centers!: IActionToggle<IntegrationCenterLogisticDto>[];

    get operationEnabled(): AbstractControl {
        return this.modalForm.controls['operationEnabledSelect'];
    };

    get operationArea(): AbstractControl {
        return this.modalForm.controls['operationAreaSelect'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.formBuilder = _injector.get(FormBuilder);
        this.employeeServiceProxy = _injector.get(EmployeeServiceProxy);

        this.modalForm = this.formBuilder.group({
            operationEnabledSelect: ['false'],
            operationAreaSelect: ['-1'],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.employeeServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response.data;
                    this.areas = response.areas;
                    this.centers = response.centers.map(p => {
                        return {
                            data: p,
                            checked: response.data.centers.findIndex(d => d.code == p.code) !== -1
                        }
                    });

                    this.operationEnabled.setValue(this.item.enabled ? 'true' : 'false');
                    this.operationArea.setValue(this.item.area ? this.item.area.prcCode : '-1');
                },
                error: () => this.dialog.dismiss()
            })
    }

    onCenterChange(event: any, index: number) {
        this.centers[index].checked = event.detail.checked;
    }

    onAreaChange(event: any) {
        const code: string = event.target.value;
        const index: number = this.areas.findIndex(p => p.prcCode == code);
        
        this.item.area = index === -1 ? undefined : this.areas[index];
    }

    async save(): Promise<void> {
        this.item.enabled = this.operationEnabled.value == 'true';
        this.item.centers = this.centers.filter(p => p.checked).map(p => p.data);

        if (this.disabled)
            return;

        this.disabled = true;

        const loading = await this.loader.show();

        this.employeeServiceProxy
            .update(this.item)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: () => {
                    this.notify.success('Se ha actualizado la información satisfactoriamente', 5000);
                    this.dialog.dismiss(true);
                }
            });
    }
}