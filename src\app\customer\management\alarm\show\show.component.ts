import { Component, Injector, Input, OnInit } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AlarmEventDto, AlarmServiceProxy } from '@proxies/alarm-proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-alarm-show',
    templateUrl: 'show.component.html',
    styleUrls: [
        'show.component.scss'
    ]
})

export class AlarmShowComponent extends ViewComponent implements OnInit {

    private readonly alarmServiceProxy: AlarmServiceProxy;

    @Input() id!: number;

    item!: AlarmEventDto;
    
    activeIndex: number = 0; 
    loaded: boolean = false;
    
    constructor(injector: Injector) {
        super(injector);

        this.alarmServiceProxy = injector.get(AlarmServiceProxy);
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.alarmServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }
}