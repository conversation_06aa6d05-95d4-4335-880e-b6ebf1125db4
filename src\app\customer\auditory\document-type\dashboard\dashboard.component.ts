import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { DocumentTypeCreateEditComponent } from '../create-edit/create-edit-component';
import { AuditoryDocumentTypeServiceProxy, DocumentTypeDto } from '@proxies/auditory/document-type.proxy'
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class DocumentTypeDashboardComponent extends ViewComponent implements OnInit {

    private documentTypeServiceProxy: AuditoryDocumentTypeServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    formatArray: IFilterOption<string>[];

    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.documentTypeServiceProxy = _injector.get(AuditoryDocumentTypeServiceProxy);
    }

    ngOnInit() {

    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: DocumentTypeCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: DocumentTypeDto): void {
        this.dialog.showWithData<boolean>({
            component: DocumentTypeCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(obj: DocumentTypeDto) {
        this.message.confirm(`¿Estas seguro de eliminar el tipo de documento "${obj.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.documentTypeServiceProxy
                    .delete(obj.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: (response) => {
                            if(response.IsSuccess) {
                                this.notify.success('Se ha eliminado el tipo de documento satisfactoriamente', 5000);
                                this.getData();   
                            }
                            else {
                                this.notify.error('Ocurrio un error en la eliminación.', 5000);
                            }
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.documentTypeServiceProxy
            .getAll(
                this.dataTable?.filters?.['Code']?.['value'],
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['Format']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: DocumentTypeDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Auditory.DocumentType'
                ],
                callback: () => this.editItem(role)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Auditory.DocumentType'
                ],
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    getFormat(format: string): string {
        return this.formats.find(x => x.code == format)?.name ?? 'No encontrado';
    }

    private async loadFilters(): Promise<void> {
        this.formatArray = this.formats.map(p => {
            return {
                label: p.name,
                value: p.code
            };
        });
    }

    private formats: any[] = [
        { name: "Predeterminado", code: "1"  },
        { name: "Anexo", code: "2"  }
    ]
}
