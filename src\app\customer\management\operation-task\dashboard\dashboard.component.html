<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Proyecto - Personas
        </ion-title>

        <ion-buttons slot="end">

            <ion-button *ngIf="'Pages.Management.OperationTask' | permission" (click)="export()" class="ion-option me-2"
                color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.OperationTask' | permission" (click)="resetFilters()"
                class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.OperationTask' | permission" (click)="getData()"
                class="ion-option me-2" color="tertiary" fill="solid">
                <ion-icon name="search"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.OperationTask.Modify' | permission" (click)="createItem()"
                class="ion-option" color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar proyecto - persona
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>
                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" [value]="table.records" (onLazyLoad)="getData()"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px">
                                    Acciones
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;" pSortableColumn="Code">
                                    Código
                                    <p-sortIcon field="Code"></p-sortIcon>
                                </th>
                                <th style="min-width: 180px; width: 180px; max-width: 180px;"
                                    pSortableColumn="HasIntegration">
                                    ¿Tiene proyecto?
                                    <p-sortIcon field="HasIntegration"></p-sortIcon>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;"
                                    pSortableColumn="ProjectCode">
                                    Cód. Proyecto
                                    <p-sortIcon field="ProjectCode"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;"
                                    pSortableColumn="ProjectName">
                                    Nombre del proyecto
                                    <p-sortIcon field="ProjectName"></p-sortIcon>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;"
                                    pSortableColumn="CustomerCode">
                                    Cód. Cliente
                                    <p-sortIcon field="CustomerCode"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;"
                                    pSortableColumn="CustomerName">
                                    Razón social
                                    <p-sortIcon field="CustomerName"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;"
                                    pSortableColumn="AreaName">
                                    Área
                                    <p-sortIcon field="AreaName"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;"
                                    pSortableColumn="ManagementName">
                                    Gerencia
                                    <p-sortIcon field="ManagementName"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;"
                                    pSortableColumn="CenterName">
                                    Centro logístico
                                    <p-sortIcon field="CenterName"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;"
                                    pSortableColumn="MovementName">
                                    Ingreso/Gasto
                                    <p-sortIcon field="MovementName"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;"
                                    pSortableColumn="LocationName">
                                    Ubicación
                                    <p-sortIcon field="LocationName"></p-sortIcon>
                                </th>
                                <th style="min-width: 220px; width: 220px; max-width: 220px;"
                                    pSortableColumn="CreationTime">
                                    Fecha de creación
                                    <p-sortIcon field="CreationTime"></p-sortIcon>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    Creado por
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px">
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter type="text" field="code"></p-columnFilter>
                                </th>
                                <th style="min-width: 180px; width: 180px; max-width: 180px;">
                                    <p-columnFilter field="hasIntegration" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="hasIntegrationArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter type="text" field="projectCode"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter type="text" field="projectName"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter type="text" field="customerCode"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter type="text" field="customerName"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="areaCode" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="areas"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="managementCode" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="managements"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="centerCode" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="centers"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="movementCode" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="movements"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter type="text" field="locationName"></p-columnFilter>
                                </th>
                                <th style="min-width: 220px; width: 220px; max-width: 220px;">
                                    <p-columnFilter field="fromCreationTime" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <app-date-input name="FromCreationTime" [value]="value" [showLabel]="false"
                                                placeholder="Desde" (valueChange)="filter($event)" />
                                        </ng-template>
                                    </p-columnFilter>
                                    <div class="d-block my-1"></div>
                                    <p-columnFilter field="toCreationTime" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <app-date-input name="ToCreationTime" [value]="value" [showLabel]="false"
                                                placeholder="Hasta" (valueChange)="filter($event)" />
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    {{record.code}}
                                </td>
                                <td class="text-center" style="min-width: 180px; max-width: 180px; width: 180px">
                                    <ion-badge color="tertiary" *ngIf="record.hasIntegration">
                                        Si
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record.hasIntegration">
                                        No
                                    </ion-badge>
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    {{record.projectCode}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.projectName}}
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    {{record.customerCode}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.customerName}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.areaName}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.managementName}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.centerName}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.movementName}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.locationName}}
                                </td>
                                <td style="min-width: 220px; width: 220px; max-width: 220px;">
                                    {{record.creationTime | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    {{record?.creationUser?.name}} {{record?.creationUser?.surname}}
                                    {{record?.creationUser?.secondSurname}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>
            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>