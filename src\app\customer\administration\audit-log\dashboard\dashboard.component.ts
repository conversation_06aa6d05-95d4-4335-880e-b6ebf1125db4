import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { AuditLogDto, AuditLogRequestType, AuditLogServiceProxy } from '@proxies/audit-log.proxy';
import { AuditLogDetailComponent } from '../detail/detail.component';
import { Paginator } from 'primeng/paginator';
import { DateTime } from 'luxon';
import { finalize } from 'rxjs';
import { AppFileDownloadService } from '@core/services/file-download.service';

@Component({
    selector: 'app-audit-log-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class AuditLogDashboardComponent extends ViewComponent {

    private auditLogServiceProxy: AuditLogServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    activeIndex: number | number[] = undefined;

    userName: string;
    serviceName: string;
    methodName: string;
    requestStatus: AuditLogRequestType = AuditLogRequestType.All;
    startDate: Date = DateTime.now().startOf('day').toJSDate();
    endDate: Date = DateTime.now().startOf('day').toJSDate();

    requestStatuses = {
        all: AuditLogRequestType.All,
        exception: AuditLogRequestType.Exception,
        succeed: AuditLogRequestType.Succeed
    }

    get showAdvanceFilters(): boolean {
        return this.activeIndex !== undefined && this.activeIndex !== null;
    }

    constructor(_injector: Injector) {
        super(_injector);

        this.auditLogServiceProxy = _injector.get(AuditLogServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    onStartDateChange(event: any) {
        let startDate = DateTime.fromJSDate(event);

        if (startDate.isValid) {
            let endDate = DateTime.fromJSDate(this.endDate);

            if (endDate.isValid && startDate.diff(endDate).toMillis() > 0) {
                this.endDate = DateTime.fromJSDate(startDate.toJSDate()).startOf('day').toJSDate();
            }
        }
    }

    onEndDateChange(event: any) {
        let endDate = DateTime.fromJSDate(event);

        if (endDate.isValid) {
            let startDate = DateTime.fromJSDate(this.startDate);

            if (startDate.isValid && startDate.diff(endDate).toMillis() > 0) {
                this.startDate = DateTime.fromJSDate(endDate.toJSDate()).startOf('day').toJSDate();
            }
        }
    }

    showItem(auditLog: AuditLogDto) {
        this.dialog.showWithData<boolean>({
            component: AuditLogDetailComponent,
            componentProps: {
                auditLog: auditLog
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.auditLogServiceProxy
            .getAll(
                DateTime.fromJSDate(this.startDate),
                DateTime.fromJSDate(this.endDate),
                this.userName,
                this.showAdvanceFilters ? this.serviceName : undefined,
                this.showAdvanceFilters ? this.methodName : undefined,
                this.showAdvanceFilters ? (this.requestStatus == this.requestStatuses.exception ? true : this.requestStatus == this.requestStatuses.succeed ? false : undefined) : undefined,
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    resetFilters() {
        this.userName = '';
        this.serviceName = '';
        this.methodName = '';
        this.requestStatus = AuditLogRequestType.All;
        this.startDate = DateTime.now().startOf('day').toJSDate();
        this.endDate = DateTime.now().startOf('day').toJSDate();
        this.getData();
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.auditLogServiceProxy
            .export(
                DateTime.fromJSDate(this.startDate),
                DateTime.fromJSDate(this.endDate),
                this.userName,
                this.showAdvanceFilters ? this.serviceName : undefined,
                this.showAdvanceFilters ? this.methodName : undefined,
                this.showAdvanceFilters ? (this.requestStatus == this.requestStatuses.exception ? true : this.requestStatus == this.requestStatuses.succeed ? false : undefined) : undefined,
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }
}