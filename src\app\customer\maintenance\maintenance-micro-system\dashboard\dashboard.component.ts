import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { MaintenanceMicroSystemDto, MaintenanceMicroSystemServiceProxy } from '@proxies/maintenance-micro-system.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { MaintenanceMicroSystemCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';
import { MaintenanceMicroSystemManagementComponent } from '../management/management.component';

@Component({
    selector: 'app-maintenance-sub-family-dashboard.component',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class MaintenanceMicroSystemDashboardComponent extends ViewComponent {

    private readonly maintenanceMicroSystemServiceProxy: MaintenanceMicroSystemServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    staticStates: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceMicroSystemServiceProxy = _injector.get(MaintenanceMicroSystemServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: MaintenanceMicroSystemCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: MaintenanceMicroSystemDto): void {
        this.dialog.showWithData<boolean>({
            component: MaintenanceMicroSystemCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: MaintenanceMicroSystemDto): void {
        this.message.confirm(`¿Estas seguro de eliminar el registro "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.maintenanceMicroSystemServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el registro satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    managementItem(item: MaintenanceMicroSystemDto): void {
        this.dialog.showWithData<boolean>({
            component: MaintenanceMicroSystemManagementComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.maintenanceMicroSystemServiceProxy
            .getAll({
                maintenanceMacroSystemName: this.dataTable?.filters?.['familyName']?.['value'],
                maintenanceMacroSystemCode: this.dataTable?.filters?.['familyCode']?.['value'],
                name: this.dataTable?.filters?.['name']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator, event),
                skipCount: this.table.getSkipCount(this.paginator, event)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: MaintenanceMicroSystemDto): void {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.MaintenanceMicroSystem.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Gestionar Sistemas 3',
                permissions: [
                    'Pages.Maintenance.MaintenanceNanoSystem.Modify'
                ],
                callback: () => this.managementItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.MaintenanceMicroSystem.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.maintenanceMicroSystemServiceProxy
            .export({
                maintenanceMacroSystemName: this.dataTable?.filters?.['maintenanceMacroSystemName']?.['value'],
                maintenanceMacroSystemCode: this.dataTable?.filters?.['maintenanceMacroSystemCode']?.['value'],
                name: this.dataTable?.filters?.['name']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator),
                skipCount: this.table.getSkipCount(this.paginator)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters(): void {
        this.dataTable?.clear();
    }
}