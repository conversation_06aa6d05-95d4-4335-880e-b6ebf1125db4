import { Component, EventEmitter, Injector, Input, Output } from '@angular/core';
import { AbstractControl, FormGroup } from '@angular/forms';
import { AppFindBussinessPartnerComponent } from '@components/find-bussiness-partner/find-bussiness-partner.component';
import { AppFindPropertyComponent } from '@components/find-property/find-property.component';
import { AppFindUserComponent } from '@components/find-user/find-user.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { InspectionDto, InspectionInspectionTypeDto, InspectionMode, InspectionUserDto } from '@proxies/inspection.proxy';
import { IntegrationBusinessPartnerDto, IntegrationCenterLogisticDto, IntegrationPropertyDto } from '@proxies/integration.proxy';

@Component({
    selector: 'app-inspection-general-information',
    templateUrl: 'general-information.component.html',
    styleUrls: [
        'general-information.component.scss'
    ]
})
export class InspectionGeneralInformationComponent extends ViewComponent {

    private _inspectionDate!: Date;
    private _inspectionTime!: Date;

    @Input() modalForm!: FormGroup;
    @Input() inspection!: InspectionDto;
    @Input() completed!: boolean;
    @Input() centers!: IntegrationCenterLogisticDto[];
    @Input() inspectionTypes!: InspectionInspectionTypeDto[];

    @Input() get inspectionDate(): Date {
        return this._inspectionDate;
    }

    @Input() get inspectionTime(): Date {
        return this._inspectionTime;
    }

    set inspectionDate(value: Date) {
        this._inspectionDate = value;
        this.inspectionDateChange.emit(this._inspectionDate);
    }

    set inspectionTime(value: Date) {
        this._inspectionTime = value;
        this.inspectionTimeChange.emit(this._inspectionTime);
    }

    @Output() inspectionDateChange: EventEmitter<Date> = new EventEmitter<Date>();
    @Output() inspectionTimeChange: EventEmitter<Date> = new EventEmitter<Date>();

    inspectionModes = {
        none: InspectionMode.None,
        programed: InspectionMode.Programed,
        modifiedWithoutDate: InspectionMode.ModifiedWithoutDate,
        modified: InspectionMode.Modified,
        process: InspectionMode.Process,
        executed: InspectionMode.Executed,
        canceled: InspectionMode.Canceled,
        contract: InspectionMode.Contract,
        rejected: InspectionMode.Rejected,
        completed: InspectionMode.Completed
    };

    get inspectionType(): AbstractControl {
        return this.modalForm.controls['inspectionTypeSelect'];
    };

    get inspectionMode(): AbstractControl {
        return this.modalForm.controls['inspectionModeSelect'];
    };

    get inspectionRejectDescription(): AbstractControl {
        return this.modalForm.controls['inspectionRejectDescriptionInput'];
    };

    get inspectionCancelDescription(): AbstractControl {
        return this.modalForm.controls['inspectionCancelDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);
    }

    showFindCustomer(): void {
        this.dialog.showWithData<IntegrationBusinessPartnerDto>({
            component: AppFindBussinessPartnerComponent,
            componentProps: {
                selectionMode: 'single',
                selecteds: this.inspection.customer ? [this.inspection.customer] : []
            }
        }).then((response) => {
            if (response.data.result) {
                this.inspection.customer = response.data.result;
                this.inspection.property = undefined;
            }
        });
    }

    showFindProperty(): void {
        this.dialog.showWithData<IntegrationPropertyDto>({
            component: AppFindPropertyComponent,
            componentProps: {
                customer: this.inspection?.customer?.cardCode
            }
        }).then((response) => {
            if (response.data.result) {
                this.inspection.property = response.data.result;
                this.inspection.center = new IntegrationCenterLogisticDto({
                    code: this.inspection.property.u_mss_clocod,
                    name: this.inspection.property.u_mss_clonom
                });

                if ((this.inspection.customer === null || this.inspection.customer === undefined) || this.inspection.customer.cardCode != this.inspection.property.cardCode) {
                    this.inspection.customer = new IntegrationBusinessPartnerDto();
                    this.inspection.customer.cardCode = this.inspection.property.cardCode;
                    this.inspection.customer.cardName = this.inspection.property.cardName;
                    this.inspection.customer.cardType = this.inspection.property.cardType;
                }
            }
        });
    }

    showFindUser(): void {
        this.dialog.showWithData<InspectionUserDto>({
            component: AppFindUserComponent
        }).then((response) => {
            if (response.data.result) {
                this.inspection.assignedUser = new InspectionUserDto().fromJS(response.data.result);
            }
        });
    }
}