import { Component, Input } from '@angular/core';
import { AlarmEventDto, AlarmStatus } from '@proxies/alarm-proxy';

@Component({
    selector: 'app-alarm-general-information',
    templateUrl: 'general-information.component.html',
    styleUrls: [
        'general-information.component.scss'
    ]
})
export class AlarmGeneralInformationComponent {
    @Input() alarm: AlarmEventDto;

    alarmStatuses = {
        none: AlarmStatus.None,
        pending: AlarmStatus.Pending,
        process: AlarmStatus.Process,
        completed: AlarmStatus.Completed
    };

}