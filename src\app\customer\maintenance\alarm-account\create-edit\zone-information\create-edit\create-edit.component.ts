import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { EntityDto } from '@core/utils/core.request';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { AlarmAccountDto } from '@proxies/alarm-account.proxy';
import { AlarmZoneDto, AlarmZoneServiceProxy } from '@proxies/alarm-zone.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-alarm-zone-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class AlarmZoneCreateEditComponent extends ViewComponent implements OnInit {

    private alarmZoneServiceProxy: AlarmZoneServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;
    @Input() alarmAccount: AlarmAccountDto;
    
    item: AlarmZoneDto = new AlarmZoneDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get alarmZoneName(): AbstractControl {
        return this.modalForm.controls['alarmZoneNameInput'];
    };

    get alarmZonePartition(): AbstractControl {
        return this.modalForm.controls['alarmZonePartitionInput'];
    };

    get alarmZoneCode(): AbstractControl {
        return this.modalForm.controls['alarmZoneCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmZoneServiceProxy = _injector.get(AlarmZoneServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            alarmZoneNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(150)])],
            alarmZonePartitionInput: ['', Validators.compose([Validators.required, Validators.maxLength(2)])],
            alarmZoneCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(4)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.alarmZoneServiceProxy
                .get(this.alarmAccount.id, this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.alarmZoneName.setValue(this.item.name);
                        this.alarmZonePartition.setValue(this.item.partition);
                        this.alarmZoneCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new AlarmZoneDto();
            this.item.alarmAccount = new EntityDto<string>().fromJS(this.alarmAccount);
        }
    }

    async save(): Promise<void> {

        this.item.name = this.alarmZoneName.value;
        this.item.partition = this.alarmZonePartition.value;
        this.item.code = this.alarmZoneCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de la zona de alarma es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código de la zona de alarma es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.alarmZoneServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Zona de alarma actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.alarmZoneServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Zona de alarma creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}