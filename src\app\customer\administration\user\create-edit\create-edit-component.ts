import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { UserDocumentTypeDto, UserDto, UserEmployeeDto, UserMethodDto, UserRoleDto, UserServiceProxy } from '@proxies/user.proxy';
import { isNullEmptyOrWhiteSpace, isValidEmailAddress } from '@core/utils/tools';
import { TreeNode } from 'primeng/api';
import { finalize } from 'rxjs';
import { blobToText } from '@core/utils/core.request';
import { AppFindEmployeeComponent } from '@components/find-employee/find-employee.component';
import { EmployeeDto } from '@proxies/employee.proxy';
import { isValidDocumentType } from '@core/utils/document.util';
import QRCodeStyling from 'qr-code-styling';
import * as htmlToImage from 'html-to-image';

const enum Steps {
    Search,
    Typing
}

@Component({
    selector: 'app-create-edit-user',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class UserCreateEditComponent extends ViewComponent implements OnInit {

    private userServiceProxy: UserServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: UserDto = new UserDto();
    userCode: string;
    roles: string[];
    documentTypes: UserDocumentTypeDto[];
    grantedRoleNames: string[] = [];

    roleData: TreeNode<string>[];
    selectedRoles: any;

    disabled: boolean = false;
    isAdmin: boolean = false;
    loaded: boolean = false;

    step: Steps = Steps.Search;
    steps = {
        search: Steps.Search,
        typing: Steps.Typing
    }

    modalForm!: FormGroup;

    get document(): AbstractControl {
        return this.modalForm.controls['documentInput'];
    };

    get documentType(): AbstractControl {
        return this.modalForm.controls['documentTypeSelect'];
    };

    get name(): AbstractControl {
        return this.modalForm.controls['nameInput'];
    };

    get surname(): AbstractControl {
        return this.modalForm.controls['surnameInput'];
    };

    get secondSurname(): AbstractControl {
        return this.modalForm.controls['secondSurnameInput'];
    };

    get emailAddress(): AbstractControl {
        return this.modalForm.controls['emailAddressInput'];
    };

    get phoneNumber(): AbstractControl {
        return this.modalForm.controls['phoneNumberInput'];
    };

    get isActive(): AbstractControl {
        return this.modalForm.controls['isActiveSelect'];
    };

    get isNewPassword(): AbstractControl {
        return this.modalForm.controls['isNewPasswordCheckbox'];
    };

    get newPassword(): AbstractControl {
        return this.modalForm.controls['newPasswordInput'];
    };

    get job(): AbstractControl {
        return this.modalForm.controls['jobInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.userServiceProxy = _injector.get(UserServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            documentTypeSelect: ['-1', Validators.compose([Validators.required])],
            documentInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
            nameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            surnameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            secondSurnameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            emailAddressInput: ['', Validators.compose([Validators.required, Validators.email, Validators.maxLength(128)])],
            phoneNumberInput: ['', Validators.compose([Validators.minLength(9), Validators.maxLength(9), Validators.pattern(/^-?(0|[1-9]\d*)?$/)])],
            jobInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            isActiveSelect: ['false', [Validators.required]],
            isNewPasswordCheckbox: [true, [Validators.required]],
            newPasswordInput: ['', Validators.compose([Validators.maxLength(255)])]
        });

        this.isNewPassword.statusChanges.subscribe({
            next: (newStatus) => {
                if (newStatus) {
                    this.newPassword.setValidators(Validators.compose([Validators.required, Validators.maxLength(255)]));
                } else {
                    this.newPassword.setValidators(Validators.compose([Validators.maxLength(255)]));
                }
            }
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();

        this.userServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    this.isAdmin = response.user !== undefined && response.user !== null && response.user.userName == 'admin';

                    this.documentTypes = response.documentTypes;
                    this.roles = response.roleNames;
                    this.roleData = response.roleNames.map(p => {
                        return <TreeNode<string>>{
                            label: p,
                            data: p
                        };
                    });

                    if (response.user) {
                        this.item = response.user;
                        this.grantedRoleNames = response.selectedRoleNames;
                        this.selectedRoles = this.roleData.filter(p => this.grantedRoleNames.findIndex(d => d == p.data) !== -1);

                        this.document.setValue(response.user.document);
                        this.documentType.setValue(response.user.documentType ? response.user.documentType.id : -1);
                        this.name.setValue(response.user.name);
                        this.surname.setValue(response.user.surname);
                        this.secondSurname.setValue(response.user.secondSurname);
                        this.emailAddress.setValue(response.user.emailAddress);
                        this.phoneNumber.setValue(response.user.phoneNumber);
                        this.job.setValue(response.user.job);
                        this.isActive.setValue((this.item.isActive ? 'true' : 'false'));
                        this.isNewPassword.setValue(false);
                        this.step = this.steps.typing;

                        const qrCodeStyling = new QRCodeStyling({
                            width: 250,
                            height: 250,
                            type: "svg",
                            data: `U_${response.user.code}`,
                            dotsOptions: {
                                type: 'dots'
                            },
                            cornersSquareOptions: {
                                type: 'extra-rounded'
                            },
                            cornersDotOptions: {
                                type: 'dot'
                            }
                        });

                        qrCodeStyling.getRawData('svg').then((data: any) => {
                            blobToText(data).subscribe({
                                next: (data) => {
                                    this.userCode = data;
                                }
                            });
                        });
                    }

                    this.loaded = true;
                }
            });
    }

    showFindEmployee(): void {
        this.dialog.showWithData<EmployeeDto>({
            component: AppFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true
            }
        }).then((response) => {
            if (response.data.result) {
                this.item.employee = new UserEmployeeDto().fromJS(response.data.result);
                this.step = this.steps.typing;
            }
        });
    }

    downloadUserCode(): void {
        htmlToImage.toJpeg(document.getElementById('qr-row'), { backgroundColor: 'white' }).then((dataUrl) => {
            var link = document.createElement('a');
            link.download = 'qr.jpeg';
            link.href = dataUrl;
            link.click();
        });
    }

    async save(): Promise<void> {

        this.modalForm.markAllAsTouched();

        let data = new UserMethodDto();

        this.item.document = this.document.value;
        this.item.documentType = this.documentTypes.find(p => p.id == this.documentType.value);
        this.item.isActive = (this.isActive.value == 'true');
        this.item.job = this.job.value;

        if (!this.item.documentType) {
            this.message.info('El tipo de documento es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.document)) {
            this.message.info('El Nº de documento es obligatorio', 'Aviso');
            return;
        }
        if (!isValidDocumentType({
            value: this.item.document,
            hasMinLength: this.item.documentType.hasMinLength,
            minLength: this.item.documentType.minLength,
            hasMaxLength: this.item.documentType.hasMaxLength,
            maxLength: this.item.documentType.maxLength,
            hasRegex: this.item.documentType.hasRegex,
            regex: this.item.documentType.regex,
            hasMask: this.item.documentType.hasMask,
            mask: this.item.documentType.mask
        })) {
            this.message.info('El Nº de documento es inválido', 'Aviso');
            return;
        }

        if (this.isAdmin) {
            this.item.name = this.name.value;
            this.item.surname = this.surname.value;
            this.item.secondSurname = this.secondSurname.value;
            this.item.emailAddress = this.emailAddress.value;
            this.item.phoneNumber = this.phoneNumber.value;
            this.item.password = this.isNewPassword.value ? this.newPassword.value : undefined;

            if (isNullEmptyOrWhiteSpace(this.item.name)) {
                this.message.info('El nombre del usuario es obligatorio', 'Aviso');
                return;
            }
            if (isNullEmptyOrWhiteSpace(this.item.surname)) {
                this.message.info('El apellido paterno del usuario es obligatorio', 'Aviso');
                return;
            }
            if (isNullEmptyOrWhiteSpace(this.item.secondSurname)) {
                this.message.info('El apellido materno del usuario es obligatorio', 'Aviso');
                return;
            }
            if (isNullEmptyOrWhiteSpace(this.item.emailAddress)) {
                this.message.info('El correo electrónico del usuario es obligatorio', 'Aviso');
                return;
            }
            if (!isValidEmailAddress(this.item.emailAddress)) {
                this.message.info('El correo electrónico del usuario es inválido', 'Aviso');
                return;
            }
            if (this.isNewPassword.value && isNullEmptyOrWhiteSpace(this.item.password)) {
                this.message.info('La contraseña es obligatoria', 'Aviso');
                return;
            }
        }

        data.user = this.item;
        data.roleNames = this.selectedRoles.map((p: any) => p.data);

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.userServiceProxy
                .update(data)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Usuario actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.userServiceProxy
                .create(data)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Usuario creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}