import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { SpecificProtectionEquipmentDto, SpecificProtectionEquipmentServiceProxy } from '@proxies/specific-protection-equipment.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-specific-protection-equipment-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class SpecificProtectionEquipmentCreateEditComponent extends ViewComponent implements OnInit {

    private specificProtectionEquipmentServiceProxy: SpecificProtectionEquipmentServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: SpecificProtectionEquipmentDto = new SpecificProtectionEquipmentDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get specificProtectionEquipmentName(): AbstractControl {
        return this.modalForm.controls['specificProtectionEquipmentNameInput'];
    };

    get specificProtectionEquipmentEnabled(): AbstractControl {
        return this.modalForm.controls['specificProtectionEquipmentEnabledSelect'];
    };

    get specificProtectionEquipmentCode(): AbstractControl {
        return this.modalForm.controls['specificProtectionEquipmentCodeInput'];
    };
    
    constructor(_injector: Injector) {
        super(_injector);

        this.specificProtectionEquipmentServiceProxy = _injector.get(SpecificProtectionEquipmentServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            specificProtectionEquipmentNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            specificProtectionEquipmentEnabledSelect: ['true', [Validators.required]],
            specificProtectionEquipmentCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.specificProtectionEquipmentServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.specificProtectionEquipmentName.setValue(this.item.name);
                        this.specificProtectionEquipmentEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.specificProtectionEquipmentCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new SpecificProtectionEquipmentDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.specificProtectionEquipmentName.value;
        this.item.enabled = this.specificProtectionEquipmentEnabled.value == 'true';
        this.item.code = this.specificProtectionEquipmentCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de equipo de protección personal específico es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.specificProtectionEquipmentServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Equipo de protección personal específico actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.specificProtectionEquipmentServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Equipo de protección personal específico creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}