import { ChangeDetectorRef, Component, EventEmitter, Injector, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { GeolocationComponent } from '@core/inheritance/app-component.base';
import { MaintenanceCenterCoordinateDto, MaintenanceCenterDto } from '@proxies/maintenance-center.proxy';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-maintenance-center-detail',
    templateUrl: 'detail.component.html',
    styleUrls: [
        'detail.component.scss'
    ]
})
export class MaintenanceCenterDetailComponent extends GeolocationComponent implements OnInit, OnDestroy {

    private _polygons: google.maps.Polygon[];

    @Input() apiKey!: string;
    @Input() center!: MaintenanceCenterDto;

    @Input() get polygons(): google.maps.Polygon[] {
        return this._polygons;
    }

    set polygons(value: any) {
        this._polygons = value;
        this.polygonsChange.emit(value);
    }

    @Output() polygonsChange: EventEmitter<any> = new EventEmitter<any>();

    private manager!: google.maps.drawing.DrawingManager;

    constructor(injector: Injector) {
        super(injector);

        this.mapId = 'map-center';
        this.coords = environment.maps.defaultLocation;
    }

    override ngOnInit(): void {

        this.setToken(this.apiKey);

        super.ngOnInit();
    }

    override ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    override onMapLoaded(): void {
        this.manager = this.google.enableDraw(this.map, (polygon) => {
            for (let polygon of this.polygons)
                polygon.setMap(null);

            this.polygons = [];
            this.polygons.push(polygon);
        });

        let path: any = JSON.parse(this.center.path);

        if (Array.isArray(path)) {
            let coordinates: MaintenanceCenterCoordinateDto[] = path.map(p => new MaintenanceCenterCoordinateDto({ lat: p.lat, lng: p.lng }));

            if (coordinates.length > 0) {

                let polygon: google.maps.Polygon = this.google.createPolygon(this.map, coordinates);
                let bounds = new google.maps.LatLngBounds();

                polygon
                    .getPath()
                    .forEach((latlng) => bounds.extend(latlng));

                this.map.fitBounds(bounds);
                this.map.setCenter(bounds.getCenter());

                this.polygons.push(polygon);

                this.changeDetectorRef.detectChanges();
            }
        }
    }
}