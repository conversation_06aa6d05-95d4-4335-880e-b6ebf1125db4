import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MaintenanceCenterDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceCenterManagementComponent } from './management/management.component';

const routes: Routes = [
    {
        path: '',
        children: [
            { path: 'dashboard', component: MaintenanceCenterDashboardComponent },
            { path: 'management/:center', component: MaintenanceCenterManagementComponent },
            { path: '', pathMatch: 'full', redirectTo: 'dashboard' },
            { path: '**', redirectTo: 'dashboard' }
        ]
    }
];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class MaintenanceCenterRoutingModule { }