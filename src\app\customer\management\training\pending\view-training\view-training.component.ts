import { Component, Injector, OnInit } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { TrainingDto, TrainingServiceProxy, TrainingEvaluationDto, TrainingEvaluationSubmissionDto, TrainingEvaluationAnswerDto, TrainingSatisfactionDto } from '@proxies/training.proxy';
import { data } from 'jquery';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-training-pending-view',
    templateUrl: 'view-training.component.html',
    styleUrls: [
        'view-training.component.scss'
    ]
})
export class TrainingPendingViewComponent extends ViewComponent implements OnInit {

    id: number;
    subscriptionId: number;
    hasEvaluation: boolean = false; // Recibido desde el listado
    isEvaluationCompleted: boolean = false; // Recibido desde el listado
    item: TrainingDto = new TrainingDto();
    loaded: boolean = false;
    activeIndex: number = 0; // Empezar en el primer tab (archivos)
    watchedVideos: Set<string> = new Set(); // Para controlar qué videos se han visto completamente
    code: string  ;
    // Evaluación
    evaluation: TrainingEvaluationDto | null = null;
    evaluationLoading: boolean = false;
    evaluationSubmitted: boolean = false; // Para controlar si se envió en esta sesión

    // Satisfacción
    satisfactionRating: number = 0; // 0-5 estrellas
    satisfactionComments: string = '';
    satisfactionSaving: boolean = false;
    satisfactionSaved: boolean = false;
    
    constructor(
        _injector: Injector,
        private trainingServiceProxy: TrainingServiceProxy,
     ) {
        super(_injector);
    }

    async ngOnInit(): Promise<void> {
        console.log('Component initialized with params:', {
            id: this.id,
            subscriptionId: this.subscriptionId,
            hasEvaluation: this.hasEvaluation,
            isEvaluationCompleted: this.isEvaluationCompleted
        });

        const loading = await this.loader.show();

        this.trainingServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.loaded = true;
      

                    // Cargar evaluación si existe
                    if (this.hasEvaluation) {
                        this.loadEvaluation();
                    }
                },
                error: async () => {
                    await this.dialog.dismiss();
                    this.message.error('Error al cargar la información de la capacitación', 'Error');
                }
            });
    }

    close(): void {
        this.dialog.dismiss();
    }

    getStatusLabel(status: number): string {
        switch (status) {
            case 0: // Pending
                return 'Pendiente';
            case 1: // Changed
                return 'Reprogramado';
            case 2: // Started
                return 'Iniciado';
            case 3: // Completed
                return 'Completado';
            case 4: // Canceled
                return 'Cancelado';
            default:
                return 'Desconocido';
        }
    }

    getStatusColor(status: number): string {
        switch (status) {
            case 0: // Pending
                return 'warning';
            case 1: // Changed
                return 'secondary';
            case 2: // Started
                return 'primary';
            case 3: // Completed
                return 'success';
            case 4: // Canceled
                return 'danger';
            default:
                return 'medium';
        }
    }

    formatDate(date: any): string {
        if (!date) return '';
        return date.toFormat('dd/MM/yyyy HH:mm');
    }

    hasResources(): boolean {
        return (this.item.trainingResources && this.item.trainingResources.length > 0) ||
               (this.item.trainingCompleteResources && this.item.trainingCompleteResources.length > 0);
    }

    getVideoResources(): any[] {
        const allResources = this.getAllResources();
        const videos = allResources.filter(resource => resource.type === 'video');
        console.log('Video resources found:', videos);
        videos.forEach(video => {
            console.log('Video details:', {
                name: video.name,
                path: video.path,
                type: video.type,
                mimeType: video.mimeType,
                size: video.size
            });
        });
        return videos;
    }

    getOtherResources(): any[] {
        const allResources = this.getAllResources();
        return allResources.filter(resource => resource.type !== 'video');
    }

    private getAllResources(): any[] {
        const resources: any[] = [];

        // Agregar recursos de trainingResources
        if (this.item.trainingResources && this.item.trainingResources.length > 0) {
            resources.push(...this.item.trainingResources);
        }

        // Agregar recursos de trainingCompleteResources
        if (this.item.trainingCompleteResources && this.item.trainingCompleteResources.length > 0) {
            resources.push(...this.item.trainingCompleteResources);
        }

        return resources;
    }

    onVideoStarted(videoResource: any): void {
        console.log('Video started:', videoResource.name);
        // Opción 1: Marcar como visto cuando empiece a reproducir
        
    }

    onVideoEnded(videoResource: any): void {
        console.log('Video ended:', videoResource.name);
        // Opción 2: Marcar como visto cuando termine (comentar la línea de onVideoStarted si usas esta)
        // this.markVideoAsWatched(videoResource);
        this.markVideoAsWatched(videoResource);
    }

    private markVideoAsWatched(videoResource: any): void {
        const videoKey = `${videoResource.id}_${videoResource.name}`;

        // Verificar si ya se marcó como visto este video
        if (this.watchedVideos.has(videoKey)) {
            return;
        }

        // Marcar como visto localmente
        this.watchedVideos.add(videoKey);

        // Llamar a la API para actualizar el estado
        this.trainingServiceProxy.updateWatchedVideo(this.subscriptionId)
            .subscribe({
                next: () => {
                     
                    
                },
                error: (error) => {
                     
                    // Remover de la lista local si falló la API
                    this.watchedVideos.delete(videoKey);
                     
                }
            });
    }

    isVideoWatched(videoResource: any): boolean {
        const videoKey = `${videoResource.id}_${videoResource.name}`;
        return this.watchedVideos.has(videoKey);
    }

    // Métodos para evaluación
    loadEvaluation(): void {
        if (this.evaluationLoading) {
            console.log('Evaluation already loading, skipping...');
            return; // Ya se está cargando
        }

        console.log('Starting to load evaluation for training ID:', this.id);
        this.evaluationLoading = true;

        this.trainingServiceProxy.getTrainingEvaluation(this.id)
            .pipe(finalize(() => {
                this.evaluationLoading = false;
                console.log('Evaluation loading finished');
            }))
            .subscribe({
                next: (response) => {
                    this.evaluation = response;
                    
                },
                error: (error) => {
                    
                    
                }
            });
    }

    isEvaluationComplete(): boolean {
        if (!this.evaluation || !this.evaluation.questions) {
            return false;
        }

        return this.evaluation.questions.every(question =>
            question.selectedAnswer && question.selectedAnswer.trim() !== ''
        );
    }

    isEvaluationLocked(): boolean {
        // La evaluación está bloqueada si ya se completó desde el listado o si se envió en esta sesión
        return this.isEvaluationCompleted || this.evaluationSubmitted;
    }

    canSubmitEvaluation(): boolean {
        // Se puede enviar si está completa y no está bloqueada
        return this.isEvaluationComplete() && !this.isEvaluationLocked();
    }

    submitEvaluation(): void {
        if (!this.isEvaluationComplete()) {
            this.message.info('Por favor responda todas las preguntas antes de enviar', 'Evaluación Incompleta');
            return;
        }

        if (this.isEvaluationLocked()) {
            this.message.info('Esta evaluación ya ha sido completada', 'Evaluación Completada');
            return;
        }

        if (!this.evaluation) {
            this.message.error('Error: No se encontró la evaluación', 'Error');
            return;
        }

        // Crear el objeto de envío
        const submission = new TrainingEvaluationSubmissionDto();
        submission.evaluationId = this.evaluation.id;
        submission.trainingSubscriptionId = this.subscriptionId;
        submission.answers = [];

        // Mapear las respuestas
        this.evaluation.questions.forEach(question => {
            if (question.selectedAnswer) {
                const answer = new TrainingEvaluationAnswerDto();
                answer.questionId = question.id;
                answer.selectedAnswer = question.selectedAnswer;
                submission.answers.push(answer);
            }
        });

        console.log('Submitting evaluation:', submission);
        console.log('Submission JSON:', submission.toJSON());

        // Enviar la evaluación
        this.trainingServiceProxy.submitTrainingEvaluation(submission)
            .subscribe({
                next: () => {
                    this.evaluationSubmitted = true; // Marcar como enviado para bloquear
                    this.notify.success('Evaluación enviada exitosamente', 3000);

                     this.loadEvaluation();

                    // Opcional: Cerrar el modal después de enviar
                    // this.dialog.dismiss(true);
                },
                error: (error) => {
                    console.error('Error submitting evaluation:', error);
                    this.message.error('Error al enviar la evaluación', 'Error');
                }
            });
    }

     

    // Métodos para satisfacción
    setSatisfactionRating(rating: number): void {
        this.satisfactionRating = rating;
        console.log('Satisfaction rating set to:', rating);
    }

    saveSatisfaction(): void {
        if (this.satisfactionRating === 0) {
            this.message.info('Por favor selecciona una calificación antes de guardar', 'Calificación Requerida');
            return;
        }

        if (this.satisfactionSaving) {
            return; // Ya se está guardando
        }
 
        this.satisfactionSaving = true;
        const satisfactionData = new TrainingSatisfactionDto();
        satisfactionData.trainingId = this.code;
        satisfactionData.subscriptionId = this.subscriptionId;
        satisfactionData.calification = this.satisfactionRating;
        satisfactionData.description = this.satisfactionComments;
        const data = satisfactionData.toJSON();
         
        this.trainingServiceProxy.saveSatisfaction(satisfactionData).subscribe({
            next: () => {
                this.satisfactionSaving = false;
                this.satisfactionSaved = true;
                this.trainingServiceProxy.updateCalificationState(this.subscriptionId).subscribe();
                this.notify.success('Calificación guardada exitosamente', 3000);
            },
            error: (error) => {
                this.satisfactionSaving = false;
 
            }
        });
 

        // TODO: Implementar llamada real a la API
        // this.trainingServiceProxy.saveSatisfaction({
        //     trainingId: this.id,
        //     subscriptionId: this.subscriptionId,
        //     rating: this.satisfactionRating,
        //     comments: this.satisfactionComments
        // }).subscribe({
        //     next: () => {
        //         this.satisfactionSaving = false;
        //         this.satisfactionSaved = true;
        //         this.notify.success('Calificación guardada exitosamente', 3000);
        //     },
        //     error: (error) => {
        //         this.satisfactionSaving = false;
        //         console.error('Error saving satisfaction:', error);
        //         this.message.error('Error al guardar la calificación', 'Error');
        //     }
        // });
    }
}
