<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Auditoría
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button (click)="export()" class="ion-option" color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col sizeXs="6" sizeSm="6" sizeMd="6" sizeLg="4" sizeXl="4">
                    <app-date-input name="StartDate" label="Desde" placeholder="Ingrese la fecha de búsqueda"
                        [(value)]="startDate" (onDateChange)="onStartDateChange($event)" />
                </ion-col>

                <ion-col sizeXs="6" sizeSm="6" sizeMd="6" sizeLg="4" sizeXl="4">
                    <app-date-input name="EndDate" label="Hasta" placeholder="Ingrese la fecha de búsqueda"
                        [(value)]="endDate" (onDateChange)="onEndDateChange($event)" />
                </ion-col>

                <ion-col sizeXs="6" sizeSm="6" sizeMd="6" sizeLg="4" sizeXl="4">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="UserNameInput">
                                Nombre de usuario
                            </label>
                            <input enter (onEnter)="getData()" type="text" placeholder="Ingrese el nombre de usuario..."
                                class="form-control" id="UserNameInput" name="UserNameInput" [(ngModel)]="userName">
                        </div>
                    </div>
                </ion-col>

                <ion-col size="12">
                    <p-accordion [(activeIndex)]="activeIndex">
                        <p-accordionTab
                            [header]="showAdvanceFilters ? 'Ocultar filtros avanzados' : 'Mostrar filtros avanzados'">
                            <ion-row>

                                <ion-col sizeXs="6" sizeSm="6" sizeMd="6" sizeLg="4" sizeXl="4">
                                    <div class="ion-input mb-2">
                                        <div class="input-control">
                                            <label class="form-label" for="ServiceNameInput">
                                                Servicio
                                            </label>
                                            <input enter (onEnter)="getData()" type="text"
                                                placeholder="Ingrese el nombre del servico..." class="form-control"
                                                id="ServiceNameInput" name="ServiceNameInput" [(ngModel)]="serviceName">
                                        </div>
                                    </div>
                                </ion-col>

                                <ion-col sizeXs="6" sizeSm="6" sizeMd="6" sizeLg="4" sizeXl="4">
                                    <div class="ion-input mb-2">
                                        <div class="input-control">
                                            <label class="form-label" for="MethodNameInput">
                                                Método
                                            </label>
                                            <input enter (onEnter)="getData()" type="text"
                                                placeholder="Ingrese el nombre del método..." class="form-control"
                                                id="MethodNameInput" name="MethodNameInput" [(ngModel)]="methodName">
                                        </div>
                                    </div>
                                </ion-col>

                                <ion-col sizeXs="6" sizeSm="6" sizeMd="6" sizeLg="4" sizeXl="4">
                                    <div class="ion-input mb-2">
                                        <div class="input-control">
                                            <label class="form-label" for="RequestState">
                                                Estado
                                            </label>
                                            <select class="form-control" [(ngModel)]="requestStatus" id="RequestState"
                                                name="RequestState">
                                                <option [value]="requestStatuses.all">
                                                    Todos
                                                </option>
                                                <option [value]="requestStatuses.exception">
                                                    Excepción
                                                </option>
                                                <option [value]="requestStatuses.succeed">
                                                    Completado
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </ion-col>
                            </ion-row>
                        </p-accordionTab>
                    </p-accordion>
                </ion-col>

                <ion-col class="text-end" size="12">
                    <ion-button type="button" (click)="getData()" fill="solid">
                        <ion-label>
                            Buscar
                        </ion-label>
                        <ion-icon name="search" slot="start"></ion-icon>
                    </ion-button>
                </ion-col>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 70px; max-width: 70px; width: 70px;">
                                </th>
                                <th style="min-width: 50px; max-width: 50px; width: 50px;">
                                </th>
                                <th class="text-center" style="min-width: 220px; max-width: 220px; width: 220px"
                                    pSortableColumn="UserName">
                                    Nombre de usuario
                                    <p-sortIcon field="UserName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 250px; max-width: 250px; width: 250px"
                                    pSortableColumn="ServiceName">
                                    Servicio
                                    <p-sortIcon field="ServiceName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px"
                                    pSortableColumn="MethodName">
                                    Método
                                    <p-sortIcon field="MethodName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="ExecutionDuration">
                                    Duración
                                    <p-sortIcon field="ExecutionDuration"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 180px; max-width: 180px; width: 180px"
                                    pSortableColumn="ClientIpAddress">
                                    Dirección IP
                                    <p-sortIcon field="ClientIpAddress"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px"
                                    pSortableColumn="BrowserInfo">
                                    Navegador
                                    <p-sortIcon field="BrowserInfo"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 220px; max-width: 220px; width: 220px"
                                    pSortableColumn="ExecutionTime">
                                    Fecha de ejecución
                                    <p-sortIcon field="ExecutionTime"></p-sortIcon>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 70px; max-width: 70px; width: 70px;">
                                    <ion-button (click)="showItem(record)" class="ion-action" color="primary"
                                        fill="clear" size="small">
                                        <ion-icon name="search-circle" size="large" slot="icon-only"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 50px; max-width: 50px; width: 50px;">
                                    <ion-icon class="mt-1"
                                        [name]="record.exception ? 'close-circle' : 'checkmark-circle'"
                                        [color]="record.exception ? 'danger' : 'success'" size="normal"></ion-icon>
                                </td>
                                <td style="min-width: 220px; max-width: 220px; width: 220px">
                                    {{record.userName}}
                                </td>
                                <td style="min-width: 250px; max-width: 250px; width: 250px">
                                    {{record.serviceName}}
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record.methodName}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.executionDuration}} ms
                                </td>
                                <td class="text-center" style="min-width: 180px; max-width: 180px; width: 180px">
                                    {{record.clientIpAddress}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    <label class="p-table-limit">
                                        {{record.browserInfo}}
                                    </label>
                                </td>
                                <td class="text-center" style="min-width: 220px; max-width: 220px; width: 220px">
                                    {{record.executionTime | luxonFormat: 'yyyy-MM-dd HH:mm:ss'}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>