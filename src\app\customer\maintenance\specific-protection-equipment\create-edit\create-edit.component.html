<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar equipo de protección personal específico' : 'Crear equipo de protección personal específico'"
        [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="SpecificProtectionEquipmentName" class="form-label"
                                [ngClass]="{'ng-invalid' : (specificProtectionEquipmentName.invalid && (specificProtectionEquipmentName.touched || specificProtectionEquipmentName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="SpecificProtectionEquipmentName" name="SpecificProtectionEquipmentName"
                                formControlName="specificProtectionEquipmentNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="SpecificProtectionEquipmentEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="SpecificProtectionEquipmentEnabled" name="SpecificProtectionEquipmentEnabled"
                                formControlName="specificProtectionEquipmentEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="SpecificProtectionEquipmentCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="SpecificProtectionEquipmentCode" name="SpecificProtectionEquipmentCode"
                                formControlName="specificProtectionEquipmentCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>