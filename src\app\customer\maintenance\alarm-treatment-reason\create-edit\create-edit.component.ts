import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { AlarmTreatmentReasonDto, AlarmTreatmentReasonServiceProxy, AlarmTreatmentReasonType } from '@proxies/alarm-treatment-reason.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-alarm-treatment-reason-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class AlarmTreatmentReasonCreateEditComponent extends ViewComponent implements OnInit {

    private alarmTreatmentReasonServiceProxy: AlarmTreatmentReasonServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: AlarmTreatmentReasonDto = new AlarmTreatmentReasonDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get alarmTreatmentReasonName(): AbstractControl {
        return this.modalForm.controls['alarmTreatmentReasonNameInput'];
    };

    get alarmTreatmentReasonEnabled(): AbstractControl {
        return this.modalForm.controls['alarmTreatmentReasonEnabledSelect'];
    };

    get alarmTreatmentReasonType(): AbstractControl {
        return this.modalForm.controls['alarmTreatmentReasonTypeSelect'];
    };

    get alarmTreatmentReasonCode(): AbstractControl {
        return this.modalForm.controls['alarmTreatmentReasonCodeInput'];
    };

    alarmTreatmentReasonTypes = {
        all: AlarmTreatmentReasonType.All,
        true: AlarmTreatmentReasonType.True,
        false: AlarmTreatmentReasonType.False
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmTreatmentReasonServiceProxy = _injector.get(AlarmTreatmentReasonServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            alarmTreatmentReasonNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            alarmTreatmentReasonEnabledSelect: ['true', Validators.compose([Validators.required, Validators.maxLength(255)])],
            alarmTreatmentReasonTypeSelect: [`${this.alarmTreatmentReasonTypes.all}`, Validators.compose([Validators.required, Validators.maxLength(255)])],
            alarmTreatmentReasonCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.alarmTreatmentReasonServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;

                    if (this.item.id) {
                        this.alarmTreatmentReasonName.setValue(this.item.name);
                        this.alarmTreatmentReasonEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.alarmTreatmentReasonType.setValue(this.item.type);
                        this.alarmTreatmentReasonCode.setValue(this.item.code);
                    }
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    onAlarmTypeChange(event: any, index: number) : void {
        this.item.alarmTypes[index].selected = event.detail.checked;
    }

    async save(): Promise<void> {

        this.item.name = this.alarmTreatmentReasonName.value;
        this.item.type = this.alarmTreatmentReasonType.value;
        this.item.enabled = this.alarmTreatmentReasonEnabled.value == 'true';
        this.item.code = this.alarmTreatmentReasonCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre del motivo de alarma es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.alarmTreatmentReasonServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Motivo de alarma actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.alarmTreatmentReasonServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Motivo de alarma creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}