<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Jerarquías
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.Hierarchy' | permission" (click)="createItem()" class="ion-option"
                color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar unidad
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content [scrollX]="true" color="light">
    <ion-card class="hierarchy-content">
        <ion-card-content>
            <ion-row>
                <ion-col size="6">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Organigrama
                    </ion-label>

                    <p-tree styleClass="hierarchy" [value]="nodes" selectionMode="single" [(selection)]="selectedNode"
                        (onNodeSelect)="nodeSelected($event)" (onNodeDrop)="nodeDropped($event)"
                        [contextMenu]="contextMenu" [draggableNodes]="true" [droppableNodes]="true">
                        <ng-template let-node pTemplate="default">
                            <div>
                                <span class="node-label">
                                    {{node.label}}
                                </span>
                                <small class="node-status {{node.data.enabled ? 'enabled' : 'disabled'}}">
                                    {{node.data.enabled ? 'Activo' : 'Inactivo'}}
                                </small>
                                <small class="node-statistics">
                                    {{node.data.members}} miembros
                                </small>
                            </div>
                        </ng-template>
                    </p-tree>

                    <p-contextMenu #contextMenu [model]="contextMenuItems" appendTo="body"
                        [baseZIndex]="99999"></p-contextMenu>

                    <small *ngIf="!nodeCount" class="fz-note text-muted">
                        Ningúna jerarquía registrada
                    </small>

                </ion-col>

                <ion-col *ngIf="selectedNode" size="6">
                    <app-hierarchy-employee-hierarchy (onEmployeeChange)="getData()" [hierarchy]="selectedNode.data">                        
                    </app-hierarchy-employee-hierarchy>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>