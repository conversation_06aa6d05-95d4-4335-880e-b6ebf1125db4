import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { MacroProcessServiceProxy, MacroProcessDto } from '@proxies/auditory/macro-process.proxy'
import { finalize } from 'rxjs';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})

export class MacroProcessCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;

    private macroProcessServiceProxy: MacroProcessServiceProxy;
    private formBuilder: FormBuilder;

    item: MacroProcessDto = new MacroProcessDto();
    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get macroProcessCode(): AbstractControl {
        return this.modalForm.controls['macroProcessCodeInput'];
    };

    get macroProcessName(): AbstractControl {
        return this.modalForm.controls['macroProcessNameInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.macroProcessServiceProxy = _injector.get(MacroProcessServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            macroProcessCodeInput: ['', Validators.compose([Validators.required])],
            macroProcessNameInput: ['', Validators.compose([Validators.required])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.macroProcessServiceProxy
                .get(this.id)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: (response) => {
                        this.item = response;
                        this.macroProcessName.setValue(this.item.name);
                        this.macroProcessCode.setValue(this.item.code);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        }
        else {
            await loading.dismiss();
            this.disabled = false;
        }
    }

    async save(): Promise<void> {
        this.item.code = this.macroProcessCode.value;
        this.item.name = this.macroProcessName.value;

        if (!this.item.code) {
            this.message.info('El código es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.macroProcessServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Macroproceso actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.macroProcessServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Macroproceso creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}