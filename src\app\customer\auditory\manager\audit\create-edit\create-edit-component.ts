import { Component, Injector, Input, OnInit, NgModule, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { UploadResource } from '@core/utils/core.request';
import { finalize } from 'rxjs';
import { IUploadProgressRespose } from '@core/models/app-config';
import { AppAuditoryService } from '@core/services/auditory/auditory.service';
import { AppAuditoryFindEmployeeComponent } from '@components/auditory/find-employee/find-employee.component';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { AppDocumentPreviewComponent } from '@components/file-preview-pdf/file-preview-pdf.component';
import { IntegrationCenterLogisticDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { AuditoryAttachmentDto, AuditoryDateDto, AuditoryDto, AuditoryFindingDto, AuditoryGoalDto, AuditoryPartnerDto, AuditoryServiceProxy } from '@proxies/auditory/manager/audit.proxy';
import { AuditoryPolicyDto } from '@proxies/auditory/manager/audit-policy.proxy';
import { EmployeeDto } from '@proxies/employee.proxy';

@Component({
    selector: 'app-create-edit-audit',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class AuditCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;
    @Input() onlyview: boolean = false;
    private

    private auditServiceProxy: AuditoryServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private formBuilder: FormBuilder;

    size: number = 15_728_640;
    uploadResources: UploadResource[] = [];
    item: AuditoryDto = new AuditoryDto();
    disabled: boolean = false;

    _dates: Date[] = [];
    centers: IntegrationCenterLogisticDto[] = [];
    policies: AuditoryPolicyDto[] = [];
    loaded: boolean = false;

    modalForm!: FormGroup;

    set dates(value: Date[]) {
        this._dates = value;
    }

    get auditoryName(): AbstractControl {
        return this.modalForm.controls['auditoryNameInput'];
    };

    get auditoryCode(): AbstractControl {
        return this.modalForm.controls['auditoryCodeInput'];
    };

    get auditoryDocument(): AbstractControl {
        return this.modalForm.controls['auditoryDocumentInput'];
    };

    get auditoryCaption(): AbstractControl {
        return this.modalForm.controls['auditoryCaptionInput'];
    };

    get auditoryCreatedAt(): AbstractControl {
        return this.modalForm.controls['auditoryCreatedAtInput'];
    };

    get auditoryOwner(): AbstractControl {
        return this.modalForm.controls['auditoryOwnerInput'];
    };

    get auditoryPartners(): AbstractControl {
        return this.modalForm.controls['auditoryPartnersInput'];
    };

    get auditoryScope(): AbstractControl {
        return this.modalForm.controls['auditoryScopeInput'];
    };

    get auditoryCriteria(): AbstractControl {
        return this.modalForm.controls['auditoryCriteriaInput'];
    };

    get auditoryType(): AbstractControl {
        return this.modalForm.controls['auditoryTypeSelect'];
    };

    get auditoryLogisticCenter(): AbstractControl {
        return this.modalForm.controls['auditoryLogisticCenterSelect'];
    };

    get auditoryPolicy(): AbstractControl {
        return this.modalForm.controls['auditoryPolicySelect'];
    };

    get auditoryGoals(): FormArray {
        return this.modalForm.get('auditoryGoalsList') as FormArray;
    }

    get auditoryFindings(): FormArray {
        return this.modalForm.get('auditoryFindingsList') as FormArray;
    }

    createField(): FormGroup  {
        return this.formBuilder.group({
            name: ['', Validators.required]
        });
    }

    constructor(_injector: Injector) {
        super(_injector);

        this.auditServiceProxy = _injector.get(AuditoryServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            auditoryGoalsList: this.formBuilder.array([this.createField()]),
            auditoryFindingsList: this.formBuilder.array([this.createField()]),
            auditoryNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(100)])],
            auditoryCodeInput: [{ value: '', disabled: true }],
            auditoryCreatedAtInput: [{ value: (new Date()).toLocaleDateString(), disabled: true }],
            auditoryOwnerInput: [''],
            auditoryPartnersInput: [''],
            auditoryDocumentInput: [''],
            auditoryCaptionInput: [''],
            auditoryScopeInput: ['', Validators.compose([Validators.required])],
            auditoryCriteriaInput: ['', Validators.compose([Validators.required])],
            auditoryTypeSelect: ['-1', Validators.compose([Validators.required])],
            auditoryLogisticCenterSelect: ['-1', Validators.compose([Validators.required])],
            auditoryPolicySelect: ['-1', Validators.compose([Validators.required])],
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (!this.id) {
            this.disabled = false;
            await loading.dismiss();
            return;
        }

        if (this.onlyview) {
            this.modalForm.disable();
        }
        this.auditServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    console.log(response);
                    this.item = response;
                    this.item.attachments?.forEach(x => x.path =
                        this.auditServiceProxy.getResource(x.filePath, x.fileName, 1)
                    );
                    this.auditoryName.setValue(this.item.name?.toUpperCase() || '');
                    this.auditoryCode.setValue(this.item.code || '');
                    this.auditoryCreatedAt.setValue(this.item.createdAt?.toLocaleString());
                    this.auditoryOwner.setValue(this.item.owner);
                    this.auditoryPartners.setValue(this.item.partners?.map(x => x.email).join());
                    this.auditoryScope.setValue(this.item.scope);
                    this.auditoryCriteria.setValue(this.item.criteria);
                    this.auditoryType.setValue(this.item.typeId || '-1');
                    this.auditoryLogisticCenter.setValue(this.item.logisticCenters || '-1');
                    this.auditoryPolicy.setValue(this.item.policyId || '-1');
                    this.auditoryPartners.setValue(this.getPartners());
                    this.auditoryDocument.setValue(this.item.document);
                    this.auditoryCaption.setValue(this.item.caption);
                    this.auditoryGoals.clear();
                    this.item.goals.forEach(x => this.auditoryGoals.push(
                        this.formBuilder.group({name: [x.name, Validators.required]})
                    )); 
                    this.auditoryFindings.clear();
                    this.item.findings.forEach(x => this.auditoryFindings.push(
                        this.formBuilder.group({name: [x.name, Validators.required]})
                    ));                    
                    this._dates = this.item.dates?.map(x => x.date);
                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {
        this.item.name = this.auditoryName.value.toUpperCase();
        this.item.scope = this.auditoryScope.value;
        this.item.caption = this.auditoryCaption.value;
        this.item.criteria = this.auditoryCriteria.value;
        this.item.typeId = this.auditoryType.value;
        this.item.logisticCenters = this.auditoryLogisticCenter.value;
        this.item.policyId = this.auditoryPolicy.value;
        this.item.owner = this.auditoryOwner.value;
        this.item.partners = this.auditoryPartners.value?.split().map(x => new AuditoryPartnerDto().fromJS({ Email: x }));
        this.item.dates = this._dates.map(x => new AuditoryDateDto().fromJS({ Date: x }));
        this.item.document = this.auditoryDocument.value;
        this.item.goals = this.auditoryGoals.value.map(x => new AuditoryGoalDto().fromJS({ Name: x.name }))
        this.item.findings = this.auditoryFindings.value.map(x => new AuditoryFindingDto().fromJS({ Name: x.name }))
        this.item.attachments = this.item.attachments ?? [];

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        const action = this.id ? 'update' : 'create';
        const message = this.id ? 'Documento actualizado exitosamente' : 'Documento creado exitosamente';
        const callback = () => {
            this.uploadResources.forEach(x => {
                const info = new AuditoryAttachmentDto().fromJS({
                    FileName: x.file.name,
                    FilePath: x.token,
                    Extension: x.file.name.split('.').pop(),
                    Type: x.type == 'document' ? 'file' : x.type,
                    Icon: x.icon
                });
                this.item.attachments.push(info)
            })
            this.auditServiceProxy[action](this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                }))
                .subscribe(() => {
                    this.notify.success(message, 5000);
                    this.dialog.dismiss(true);
                });
        };

        this.uploadResources.length ? this.uploads(callback) : callback();
    }

    private async loadInputs(): Promise<void> {
        await Promise.all([
            this.getCenters(),
            this.getInputs()
        ]);
    }

    private getCenters(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.integrationServiceProxy
                .getAllLogiticCenters()
                .subscribe({
                    next: (response) => {
                        this.centers = response.items;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private getInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.auditServiceProxy
                .getFilters()
                .subscribe({
                    next: (response) => {
                        this.policies = response.policies;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    addGoal(): void {
        this.auditoryGoals.push(this.createField());
    }

    removeGoal(index: number): void {
        this.auditoryGoals.removeAt(index);
    }

    addFinding(): void {
        this.auditoryFindings.push(this.createField());
    }

    removeFinding(index: number): void {
        this.auditoryFindings.removeAt(index);
    }

    onUploadItem(event: UploadResource): void {
        this.uploadResources ??= [];
        this.uploadResources.push(event);
        console.log(event);
    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.uploadResources ??= [];
                this.uploadResources.splice(index, 1);
            }
        });
    }

    onRemoveResource(index: number): void {
        this.item.attachments.splice(index, 1);
    }

    showFindEmployeeAuditory(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto[]>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                selecteds: this.item.partners?.map(x => x.email) ?? []
            }
        }).then((response) => {
            if (Array.isArray(response.data.result)) {
                response.data.result.forEach(e => {
                    this.item.partners = this.item.partners ?? [];
                    const email = e.employee?.email;
                    if (email && !this.item.partners.some(p => p.email === email)) {
                        this.item.partners.push(new AuditoryPartnerDto().fromJS({ Email: email }));
                    }
                });
            }
            this.auditoryPartners.setValue(this.getPartners());
        });
    }

    showFindEmployee(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto[]>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                hideSelectAll: true
            }
        }).then((response) => {
            if (Array.isArray(response.data.result)) {
                response.data.result.forEach(e => {
                    this.auditoryOwner.setValue(e.employee?.email);
                });
            }
        });
    }

    private uploads(callback: () => void) {
        this.dialog.show({
            component: AppFileUploadProgressComponent,
            componentProps: {
                treatment: "none",
                source: 1,
                files: this.uploadResources.map(p => p.file),
                processed: (data: IUploadProgressRespose) => {
                    if (data.completed) {
                        let index: number = 0;

                        for (let token of data.tokens) {
                            this.uploadResources[index].token = token;
                            index++;
                        }

                        callback();
                    } else {
                        this.disabled = false;
                    }
                }
            }
        });
    }

    getPartners(): string {
        return this.item?.partners?.map(x => x.email).join()
    }
}