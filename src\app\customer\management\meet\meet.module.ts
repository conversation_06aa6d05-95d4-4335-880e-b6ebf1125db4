import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MeetRoutingModule } from './meet.routing.module';
import { MeetDashboardComponent } from './dashboard/dashboard.component';
import { ComponentModule } from '@components/component.module';
import { MeetCreateEditComponent } from './create-edit/create-edit.component';
import { MeetGeneralInformationComponent } from './create-edit/general-information/general-information.component';
import { MeetAssistanceInformationComponent } from './create-edit/assistance-information/assistance-information.component';
import { MeetCreateEditAssistanceComponent } from './create-edit/assistance-information/create-edit-assistance/create-edit-assistance.component';
import { MeetOperationInformationComponent } from './create-edit/operation-information/operation-information.component';
import { MeetCompleteComponent } from './complete/complete.component';
import { MeetCompleteInformationComponent } from './create-edit/complete-information/complete-information.component';
import { MeetCancelInformationComponent } from './create-edit/cancel-information/cancel-information.component';
import { MeetCancelComponent } from './cancel/cancel.component';

@NgModule({
  imports: [
    MeetRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule,
    ComponentModule
  ],
  declarations: [
    MeetDashboardComponent,
    MeetCompleteComponent,
    MeetCancelComponent,
    MeetCreateEditComponent,
    MeetGeneralInformationComponent,
    MeetAssistanceInformationComponent,
    MeetCreateEditAssistanceComponent,
    MeetOperationInformationComponent,
    MeetCompleteInformationComponent,
    MeetCancelInformationComponent
  ]
})
export class MeetModule { }