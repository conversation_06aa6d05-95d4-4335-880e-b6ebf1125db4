import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { LogisticCenterDto, LogisticCenterService } from '@core/services/servicesSCA/logistic-center.service';
import { IntegrationCenterLogisticDto } from '@proxies/integration.proxy';
import { finalize, Subscription } from 'rxjs';
import { LostItemCreateComponent } from '../create/create.component';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { ChargeService } from '@core/services/servicesSCA/charges.service'; import { RegisterChargeView } from '@core/models/modelsSCA/register-charge';

@Component({
  selector: 'app-lost-item-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class LostItemDashboardComponent extends ViewComponent {

  private fileDownloadService: AppFileDownloadService;
  private logisticCenterService: LogisticCenterService;
  private chargeService: ChargeService;
  private subscription: Subscription;

  @ViewChild('dataTable', { static: true }) dataTable: Table;
  @ViewChild('paginator', { static: true }) paginator: Paginator;

  centers: LogisticCenterDto[] = [];
  centerOptions: { value: string, label: string }[] = [];

  constructor(_injector: Injector) {
    super(_injector);
    this.logisticCenterService = _injector.get(LogisticCenterService)
    this.fileDownloadService = _injector.get(AppFileDownloadService);
    this.chargeService = _injector.get(ChargeService);
  }

  ngOnInit() {
    this.loadDataCenterLogistic()
  }
  loadDataCenterLogistic() {
    this.logisticCenterService.getAllLogisticCenter(undefined, undefined, undefined, undefined).subscribe({
      next: (response) => {
        this.centers = response.items;

        this.centerOptions = this.centers.map(center => ({
          label: center.name,
          value: center.id.toString()
        }));
      }
    });
  }
  async getData(event?: TableLazyLoadEvent) {
    if (this.table.shouldResetPaging(event)) {
      this.paginator.changePage(0);

      if (this.table.records && this.table.records.length > 0) {
        return;
      }
    }

    const loading = await this.loader.show();
    this.chargeService.getAllCharges(
      this.dataTable?.filters?.['centerId']?.['value'],
      this.dataTable?.filters?.['nameEmisor']?.['value'],
      this.dataTable?.filters?.['nameReceptor']?.['value'],
      this.table.getSorting(this.dataTable),
      this.table.getMaxResultCount(this.paginator, event),
      this.table.getSkipCount(this.paginator, event))
      .pipe(finalize(async () => await loading.dismiss()))
      .subscribe({
        next: (result) => {
          this.table.totalRecordsCount = result.totalCount;
          this.table.records = result.items;
        }
      });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }


  showActions(event: any, data: RegisterChargeView) {
    this.popover.show(event, [
      {
        label: 'Visualizar',
        callback: () => this.showItem(data)
      }
    ]);
  }

  createItem(): void {
    this.dialog.showWithData<boolean>({
      component: LostItemCreateComponent
    }).then((response) => {
      if (response.data.result)
        this.getData();
    });
  }

  showItem(data: RegisterChargeView): void {
    this.dialog.showWithData<boolean>({
      component: LostItemCreateComponent,
      componentProps: {
        id: data.id,
        editable: false,
        registerCharge: data
      }
    }).then((response) => {
      if (response.data.result)
        this.getData();
    });
  }

  resetFilters() {
    this.dataTable?.clear();
    this.getData();
  }

}
