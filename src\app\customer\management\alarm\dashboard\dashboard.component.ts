import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { AppFindBussinessPartnerComponent } from '@components/find-bussiness-partner/find-bussiness-partner.component';
import { AppFindPropertyComponent } from '@components/find-property/find-property.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IFilterOption } from '@core/models/filters';
import { AlarmTypeServiceProxy } from '@proxies/alarm-type.proxy';
import { AlarmUserServiceProxy } from '@proxies/alarm-user.proxy';
import { AlarmZoneServiceProxy } from '@proxies/alarm-zone.proxy';
import { AlarmDto, AlarmServiceProxy, AlarmStatus } from '@proxies/alarm-proxy';
import { IntegrationBusinessPartnerDto, IntegrationPropertyDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { DateTime } from 'luxon';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Subscription, finalize } from 'rxjs';
import { AlarmShowComponent } from '../show/show.component';
import { AppFileDownloadService } from '@core/services/file-download.service';

@Component({
    selector: 'app-alarm-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class AlarmDashboardComponent extends ViewComponent implements OnInit {

    private readonly alarmServiceProxy: AlarmServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;
    private readonly alarmTypeServiceProxy: AlarmTypeServiceProxy;
    private readonly integrationServiceProxy: IntegrationServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    center: string = '-1';
    centers: IFilterOption<string>[];
    alarmTypes: IFilterOption<number>[];
    alarmUsers: IFilterOption<number>[];
    alarmZones: IFilterOption<number>[];
    alarmStatusesArray: IFilterOption<AlarmStatus>[] = [
        { label: 'En curso', value: AlarmStatus.Pending },
        { label: 'Atendido', value: AlarmStatus.Completed }
    ];

    startDate: Date = DateTime.now().startOf('day').toJSDate();
    endDate: Date = DateTime.now().startOf('day').toJSDate();

    customer: IntegrationBusinessPartnerDto;
    get customerLabel(): string {
        return this.customer ? `${this.customer.licTradNum} - ${this.customer.cardName}` : 'Presiona para buscar un cliente';
    }

    property: IntegrationPropertyDto;
    get propertyLabel(): string {
        return this.property ? `${this.property.name}` : 'Presiona para buscar un bodega';
    }

    alarmType: number = -1;
    alarmStatus = AlarmStatus.None;
    alarmStatuses = {
        none: AlarmStatus.None,
        pending: AlarmStatus.Pending,
        process: AlarmStatus.Process,
        completed: AlarmStatus.Completed
    };

    private subscription: Subscription;

    constructor(injector: Injector) {
        super(injector);

        this.alarmServiceProxy = injector.get(AlarmServiceProxy);
        this.alarmTypeServiceProxy = injector.get(AlarmTypeServiceProxy);
        this.fileDownloadService = injector.get(AppFileDownloadService);
        this.integrationServiceProxy = injector.get(IntegrationServiceProxy);
    }

    async ngOnInit(): Promise<void> {

        this.alarmTypeServiceProxy
            .getAll(undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, "Name ASC", 1000, 0)
            .subscribe({
                next: (response) => {

                    this.alarmTypes = response.items.map(p => {
                        return {
                            label: p.name,
                            value: p.id
                        };
                    });

                }
            });

        this.integrationServiceProxy
            .getAllLogiticCenters()
            .subscribe({
                next: (response) => {
                    this.centers = response.items.map(p => {
                        return {
                            label: p.name,
                            value: p.code
                        }
                    });
                }
            });
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    showItem(data: AlarmDto) {
        this.dialog.show({
            component: AlarmShowComponent,
            componentProps: {
                id: data.alarmId
            }
        });
    }

    showFindUser(): void {
        this.dialog.showWithData<IntegrationBusinessPartnerDto>({
            component: AppFindBussinessPartnerComponent,
            componentProps: {
                selectionMode: 'single',
                selecteds: this.customer ? [this.customer] : []
            }
        }).then((response) => {
            if (response.data.result) {
                this.customer = response.data.result;

                if (this.property && this.property.cardCode != this.customer.cardCode) {
                    this.property = undefined;
                    this.center = '-1';
                }
            }
        });
    }

    clearCustomer(): void {
        this.customer = undefined;
    }

    showFindProperty(): void {
        this.dialog.showWithData<IntegrationPropertyDto>({
            component: AppFindPropertyComponent,
            componentProps: {
                customer: this.customer?.cardCode
            }
        }).then((response) => {
            if (response.data.result) {
                this.property = response.data.result;
                this.center = response.data.result.u_mss_clocod;

                if (this.customer) {
                    this.customer = new IntegrationBusinessPartnerDto();
                    this.customer.cardCode = this.property.cardCode;
                    this.customer.cardName = this.property.cardName;
                    this.customer.cardType = this.property.cardType;
                }
            }
        });
    }

    clearProperty(): void {
        this.property = undefined;
    }

    onStartDateChange(event: any) {
        let startDate = DateTime.fromJSDate(event);

        if (startDate.isValid) {
            let endDate = DateTime.fromJSDate(this.endDate);

            if (endDate.isValid && startDate.diff(endDate).toMillis() > 0) {
                this.endDate = DateTime.fromJSDate(startDate.toJSDate()).startOf('day').toJSDate();
            }
        }
    }

    onEndDateChange(event: any) {
        let endDate = DateTime.fromJSDate(event);

        if (endDate.isValid) {
            let startDate = DateTime.fromJSDate(this.startDate);

            if (startDate.isValid && startDate.diff(endDate).toMillis() > 0) {
                this.startDate = DateTime.fromJSDate(endDate.toJSDate()).startOf('day').toJSDate();
            }
        }
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.alarmServiceProxy
            .getAll(
                this.property?.code,
                this.center == '-1' ? undefined : this.center,
                this.customer?.cardCode,
                this.alarmType == -1 ? undefined : this.alarmType,
                this.alarmStatus == AlarmStatus.None ? undefined : this.alarmStatus,
                DateTime.fromJSDate(this.startDate),
                DateTime.fromJSDate(this.endDate),
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.alarmServiceProxy
            .export(
                this.property?.code,
                this.center == '-1' ? undefined : this.center,
                this.customer?.cardCode,
                this.alarmType == -1 ? undefined : this.alarmType,
                this.alarmStatus == AlarmStatus.None ? undefined : this.alarmStatus,
                DateTime.fromJSDate(this.startDate),
                DateTime.fromJSDate(this.endDate),
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    showActions(event: any, data: AlarmDto) {
        this.popover.show(event, [
            {
                label: 'Ver detalles',
                permissions: [],
                callback: () => this.showItem(data)
            }
        ]);
    }

    resetFilters() {
        this.startDate = DateTime.now().startOf('day').toJSDate();
        this.endDate = DateTime.now().startOf('day').toJSDate();
        this.alarmType = -1;
        this.alarmStatus = AlarmStatus.None;
        this.customer = undefined;
        this.property = undefined;

        this.dataTable?.clear();
        this.getData();
    }
}