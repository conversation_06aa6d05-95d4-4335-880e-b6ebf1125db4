import { Component, Injector, OnInit } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { SettingDto, SettingServiceProxy } from '@proxies/setting.proxy';
import { isValidEmailAddress } from '@core/utils/tools';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-configuration-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class ConfigurationDashboardComponent extends ViewComponent implements OnInit {

    private settingServiceProxy: SettingServiceProxy;

    settingsData: SettingDto;
    busy: boolean = false;

    testEmailAddress: string;

    constructor(_injector: Injector) {
        super(_injector);

        this.settingServiceProxy = _injector.get(SettingServiceProxy);
    }

    ngOnInit(): void {
        this.refresh();
    }

    async reset(): Promise<void> {
        this.settingServiceProxy
            .get()
            .subscribe({
                next: (settings) => {
                    this.settingsData = settings;
                }
            });
    }

    async sendTestEmailAddress(): Promise<void> {
        if (!isValidEmailAddress(this.testEmailAddress)) {
            this.message.info('El correo electrónico ingresado es inválido', 'Aviso');
            return;
        }

        if (this.busy)
            return;

        this.busy = true;
        const loading = await this.loader.show();

        this.settingServiceProxy
            .sendTestEmailAddress(this.testEmailAddress)
            .pipe(finalize(async () => {
                await loading.dismiss();
                this.busy = false;
            })).subscribe({
                next: () => {
                    this.notify.success('Se ha envíado el correo electrónico de pruebas exitosamente', 5000);
                    this.refresh();
                }
            });
    }

    async save(): Promise<void> {
        if (this.busy)
            return;

        this.busy = true;
        const loading = await this.loader.show();

        this.settingServiceProxy
            .update(this.settingsData)
            .pipe(finalize(() => setTimeout(async () => {
                await loading.dismiss();
                this.busy = false;
            }, 1500))).subscribe({
                next: () => {
                    this.notify.success('Se ha actualizado la configuración exitosamente', 5000);
                    this.refresh();
                }
            });
    }
}