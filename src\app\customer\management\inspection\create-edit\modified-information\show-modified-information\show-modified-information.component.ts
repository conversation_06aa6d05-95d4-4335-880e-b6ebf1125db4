import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { InspectionDto, InspectionModifiedType, InspectionModifyDto } from '@proxies/inspection.proxy';

@Component({
    selector: 'app-inspection-show-modified-information',
    templateUrl: 'show-modified-information.component.html',
    styleUrls: [
        'show-modified-information.component.scss'
    ]
})
export class InspectionShowModifiedInformationComponent extends ViewComponent implements OnInit {

    private formBuilder: FormBuilder;

    @Input() inspection: InspectionDto;
    @Input() inspectionModify: InspectionModifyDto;
    @Input() index: number;

    modalForm!: FormGroup;

    inspectionModes = {
        none: InspectionModifiedType.None,
        internal: InspectionModifiedType.Internal,
        external: InspectionModifiedType.External,
        propertyUnavailable: InspectionModifiedType.PropertyUnavailable
    };

    inspectionDate!: Date;
    inspectionTime!: Date;

    get inspectionModifiedType(): AbstractControl {
        return this.modalForm.controls['inspectionModifiedTypeSelect'];
    };

    get inspectionModifiedObservation(): AbstractControl {
        return this.modalForm.controls['inspectionModifiedObservationInput'];
    };

    get inspectionModifiedCreationTime(): AbstractControl {
        return this.modalForm.controls['inspectionModifiedCreationTimeInput'];
    };

    get inspectionModifiedCreationUser(): AbstractControl {
        return this.modalForm.controls['inspectionModifiedCreationUserInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            inspectionModifiedTypeSelect: [this.inspectionModes.none],
            inspectionModifiedObservationInput: ['', Validators.compose([Validators.maxLength(25000)])],
            inspectionModifiedCreationTimeInput: [''],
            inspectionModifiedCreationUserInput: [''],
        });
    }

    ngOnInit(): void {
        this.inspectionModifiedType.disable();
        this.inspectionModifiedObservation.disable();
        this.inspectionModifiedCreationTime.disable();
        this.inspectionModifiedCreationUser.disable();
        
        this.inspectionDate = this.inspectionModify.inspectionTime?.toJSDate();
        this.inspectionTime = this.inspectionModify.inspectionTime?.toJSDate();
        this.inspectionModifiedType.setValue(this.inspectionModify.type);
        this.inspectionModifiedObservation.setValue(this.inspectionModify.observation);
        this.inspectionModifiedCreationTime.setValue(this.inspectionModify.creationTime.toFormat('yyyy-MM-dd HH:mm:ss'));
        this.inspectionModifiedCreationUser.setValue(
            (this.inspectionModify.creationUser?.name || '') + ' ' + 
            (this.inspectionModify.creationUser?.surname || '') + ' ' + 
            (this.inspectionModify.creationUser?.secondSurname || ''));
    }
}