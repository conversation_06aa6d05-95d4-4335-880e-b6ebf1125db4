<ion-header [translucent]="true">
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-menu-button></ion-menu-button>
        </ion-buttons>
        <ion-title>Pendientes de Capacitación</ion-title>
        <ion-buttons slot="end">
            <ion-button (click)="refresh()" fill="clear">
                <ion-icon name="refresh" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-button (click)="export()" fill="clear">
                <ion-icon name="download" slot="icon-only"></ion-icon>
            </ion-button>
        </ion-buttons>
    </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bell me-2"></i>
                            Mis Pendientes de Capacitación
                        </h5>
                    </div>
                    <div class="card-body">
                        <p-table 
                            #dataTable 
                            [value]="data" 
                            [loading]="loading"
                            [totalRecords]="totalRecords"
                            [rows]="10"
                            [paginator]="true"
                            [responsive]="true"
                            styleClass="p-datatable-striped"
                            [globalFilterFields]="['title', 'trainingType', 'trainingMode', 'location', 'center', 'leader']">
                            
                            <ng-template pTemplate="caption">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>
                                        <i class="fas fa-graduation-cap me-2"></i>
                                        Total de registros: {{totalRecords}}
                                    </span>
                                    <span class="p-input-icon-left">
                                        <i class="pi pi-search"></i>
                                        <input 
                                            pInputText 
                                            type="text" 
                                            (input)="dataTable.filterGlobal($any($event.target).value, 'contains')" 
                                            placeholder="Buscar..." 
                                            class="form-control" />
                                    </span>
                                </div>
                            </ng-template>

                            <ng-template pTemplate="header">
                                <tr>
                                    <th style="min-width: 130px; max-width: 130px; width: 130px;">
                                        Acciones
                                    </th>
                                    <th pSortableColumn="trainingMode">
                                        Título
                                        <p-sortIcon field="trainingMode"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="status">
                                        Estado
                                        <p-sortIcon field="status"></p-sortIcon>
                                    </th>
                                    
                                    <th pSortableColumn="trainingStartTime">
                                        Fecha Inicio
                                        <p-sortIcon field="trainingStartTime"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="trainingEndTime">
                                        Fecha Fin
                                        <p-sortIcon field="trainingEndTime"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="location">
                                        Ubicación
                                        <p-sortIcon field="location"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="center">
                                        Centro
                                        <p-sortIcon field="center"></p-sortIcon>
                                    </th>
                                    <th pSortableColumn="leader">
                                        Líder
                                        <p-sortIcon field="leader"></p-sortIcon>
                                    </th>
                                    
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-item>
                                <tr>
                                    <td style="min-width: 130px; max-width: 130px; width: 130px;">
                                        <ion-button (click)="showActions($event, item)" class="ion-action" color="primary"
                                            fill="solid" size="small">
                                            <ion-label class="fz-small">
                                                Acciones
                                            </ion-label>
                                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                                        </ion-button>
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{item.title}}</div>
                                        <small class="text-muted">{{item.code}}</small>
                                    </td>
                                   <td>
                                        <ion-badge [color]="getStatusColor(item.status)">
                                            {{getStatusLabel(item.status)}}
                                        </ion-badge>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{formatDate(item.trainingStartTime)}}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{formatDate(item.trainingEndTime)}}
                                        </span>
                                    </td>
                                    <td>{{item.location}}</td>
                                    <td>{{item.centerName}}</td>
                                    <td>{{item.leader}}</td>
                                    
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <ion-icon name="school-outline" size="large" color="medium"></ion-icon>
                                            <p class="mt-2 mb-0 text-muted">No tienes capacitaciones pendientes</p>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ion-content>
