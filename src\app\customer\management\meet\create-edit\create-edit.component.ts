import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IUploadProgressRespose } from '@core/models/app-config';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { IntegrationCenterLogisticDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { MeetDto, MeetServiceProxy, MeetStatus, MeetTurn, MeetType } from '@proxies/meet.proxy';
import { DateTime } from 'luxon';
import { finalize } from 'rxjs';

const enum MeetIndexes {
    GeneralInformation,
    AssistanceInformation,
    OperationInformation,
    CompleteInformation,
    CancelInformation
}

@Component({
    selector: 'app-meet-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MeetCreateEditComponent extends ViewComponent implements OnInit {

    private meetServiceProxy: MeetServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;

    item!: MeetDto;
    centers!: IntegrationCenterLogisticDto[];

    loaded!: boolean;
    disabled!: boolean;
    modalForm!: FormGroup;
    activeIndex: MeetIndexes = MeetIndexes.GeneralInformation;

    meetDate!: Date;
    meetStartTime!: Date;
    meetEndTime!: Date;
    meetCompleted: boolean = false;

    get meetTitle(): AbstractControl {
        return this.modalForm.controls['meetTitleInput'];
    };

    get meetDescription(): AbstractControl {
        return this.modalForm.controls['meetDescriptionInput'];
    };

    get meetCenter(): AbstractControl {
        return this.modalForm.controls['meetCenterSelect'];
    };

    get meetCode(): AbstractControl {
        return this.modalForm.controls['meetCodeInput'];
    };

    get meetStatus(): AbstractControl {
        return this.modalForm.controls['meetStatusSelect'];
    };

    get meetTurn(): AbstractControl {
        return this.modalForm.controls['meetTurnSelect'];
    };

    get meetType(): AbstractControl {
        return this.modalForm.controls['meetTypeSelect'];
    };

    get meetCompleteDescription(): AbstractControl {
        return this.modalForm.controls['meetCompleteDescriptionInput'];
    };

    get meetCancelDescription(): AbstractControl {
        return this.modalForm.controls['meetCancelDescriptionInput'];
    };
    
    meetStatuses = {
        programmed: MeetStatus.Programmed,
        executed: MeetStatus.Executed,
        canceled: MeetStatus.Canceled,
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.meetServiceProxy = _injector.get(MeetServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            meetTitleInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            meetDescriptionInput: ['', Validators.compose([Validators.required, Validators.maxLength(25000)])],
            meetCompleteDescriptionInput: ['', Validators.compose([Validators.required, Validators.maxLength(25000)])],
            meetCancelDescriptionInput: ['', Validators.compose([Validators.required, Validators.maxLength(25000)])],            
            meetCenterSelect: ['-1'],
            meetCodeInput: [''],
            meetStatusSelect: [{ value: `${MeetStatus.Programmed}`, disabled: true }],
            meetTypeSelect: [`${MeetType.None}`],
            meetTurnSelect: [`${MeetTurn.None}`]
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        if (this.id) {
            this.meetServiceProxy
                .get(this.id)
                .subscribe({
                    next: (response) => {
                        this.item = response;
                        
                        this.meetCompleted = response.status == MeetStatus.Executed || response.status == MeetStatus.Canceled;
                        this.meetTitle.setValue(this.item.title);
                        this.meetDescription.setValue(this.item.description);
                        this.meetCenter.setValue(this.item.center);
                        this.meetCode.setValue(this.item.code);
                        this.meetType.setValue(this.item.type);
                        this.meetTurn.setValue(this.item.turn);
                        this.meetStatus.setValue(this.item.status);
                        this.meetCompleteDescription.setValue(this.item.completeObservation);
                        this.meetCancelDescription.setValue(this.item.cancelObservation);

                        this.meetDate = this.item.startTime?.toJSDate();
                        this.meetStartTime = this.item.startTime?.toJSDate();
                        this.meetEndTime = this.item.endTime?.toJSDate();

                        if (this.meetCompleted) {
                            this.meetTitle.disable();
                            this.meetDescription.disable();
                            this.meetCenter.disable();
                            this.meetCode.disable();
                            this.meetType.disable();
                            this.meetTurn.disable();
                            this.meetStatus.disable();
                        }

                        this.loadFilters({
                            next: async () => {
                                this.loaded = true;
                                await loading.dismiss();
                            },
                            error: async () => {
                                await this.dialog.dismiss();
                                await loading.dismiss();
                            }
                        });
                    }
                });
        } else {
            this.item = new MeetDto();
            this.item.uploadResources = [];
            this.item.meetResources = [];

            this.loadFilters({
                next: async () => {
                    this.loaded = true;
                    await loading.dismiss();
                },
                error: async () => {
                    await this.dialog.dismiss();
                    await loading.dismiss();
                }
            });
        }
    }

    save(): void {
        this.item.title = this.meetTitle.value;
        this.item.description = this.meetDescription.value;
        this.item.center = this.meetCenter.value;
        this.item.type = this.meetType.value;
        this.item.turn = this.meetTurn.value;
        this.item.status = this.meetStatus.value;

        if (this.item.center == "-1") {
            this.message.info('El centro logístico de la capacitación es obligatorio', 'Aviso');
            return;
        }
        if (this.item.type == MeetType.None) {
            this.message.info('El tipo de charla es obligatorio', 'Aviso');
            return;
        }
        if (this.item.turn == MeetTurn.None) {
            this.message.info('El turno de charla es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.title)) {
            this.message.info('El título de la charla es obligatorio', 'Aviso');
            return;
        }

        const meetDate: DateTime = DateTime.fromJSDate(this.meetDate);

        if (!meetDate.isValid) {
            this.message.info('La fecha de capacitación es inválida', 'Aviso');
            return;
        }

        const meetStartTime: DateTime = DateTime.fromJSDate(this.meetStartTime);

        if (!meetStartTime.isValid) {
            this.message.info('La hora de inicio de la capacitación es inválida', 'Aviso');
            return;
        }

        const meetEndTime: DateTime = DateTime.fromJSDate(this.meetEndTime);

        if (!meetEndTime.isValid) {
            this.message.info('La hora de finalización de la capacitación es inválida', 'Aviso');
            return;
        }

        if (this.item.status == MeetStatus.None) {
            this.message.info('El estado de charla es obligatorio', 'Aviso');
            return;
        }

        this.item.startTime = DateTime.fromObject({
            year: meetDate.year,
            month: meetDate.month,
            day: meetDate.day,
            hour: meetStartTime.hour,
            minute: meetStartTime.minute,
            second: 0,
            millisecond: 0
        });

        this.item.endTime = DateTime.fromObject({
            year: meetDate.year,
            month: meetDate.month,
            day: meetDate.day,
            hour: meetEndTime.hour,
            minute: meetEndTime.minute,
            second: 0,
            millisecond: 0
        });

        if (this.disabled)
            return;

        this.disabled = true;

        this.uploads(async () => {
            const loading = await this.loader.show();

            if (this.id) {
                this.meetServiceProxy
                    .update(this.item)
                    .pipe(finalize(async () => {
                        this.disabled = false;
                        await loading.dismiss()
                    })).subscribe({
                        next: () => {
                            this.notify.success('Registro actualizado satisfactoriamente', 5000);
                            this.dialog.dismiss({
                                command: 'update',
                                id: this.id
                            });
                        }
                    });
            } else {
                this.meetServiceProxy
                    .create(this.item)
                    .pipe(finalize(async () => {
                        this.disabled = false;
                        await loading.dismiss()
                    })).subscribe({
                        next: (response) => {
                            this.notify.success('Registro creado satisfactoriamente', 5000);
                            this.dialog.dismiss({
                                command: 'create',
                                id: response.id
                            });
                        }
                    });
            }
        });
    }

    private uploads(callback: () => void) {
        if (this.item.uploadResources && this.item.uploadResources.length > 0) {
            this.dialog.show({
                component: AppFileUploadProgressComponent,
                componentProps: {
                    files: this.item.uploadResources.map(p => p.file),
                    processed: (data: IUploadProgressRespose) => {
                        if (data.completed) {
                            let index: number = 0;

                            for (let token of data.tokens) {
                                this.item.uploadResources[index].token = token;
                                index++;
                            }

                            callback();
                        } else {
                            this.disabled = false;
                        }
                    }
                }
            });
        } else {
            callback();
        }
    }

    private loadFilters(observer: { next: () => void, error: () => void }): void {
        this.loadCenters(observer.next, observer.error);
    }

    private loadCenters(success: () => void, error: () => void): void {
        this.integrationServiceProxy.getAllLogiticCenters().subscribe({
            next: (response) => {
                this.centers = response.items;
                success();
            },
            error: () => error()
        });
    }
}