import { Component, Injector, Input, OnInit } from "@angular/core";
import { AbstractControl, FormGroup } from "@angular/forms";
import { ViewComponent } from "@core/inheritance/app-component.base";
import { AppConsts } from "@core/inheritance/app-core-consts";
import { MeetDto } from "@proxies/meet.proxy";

@Component({
    selector: 'app-meet-complete-information',
    templateUrl: 'complete-information.component.html',
    styleUrls: [
        'complete-information.component.scss'
    ]
})
export class MeetCompleteInformationComponent extends ViewComponent implements OnInit {

    @Input() meet!: MeetDto;
    @Input() modalForm!: FormGroup;
    @Input() completed!: boolean;
    
    meetCompleteDate!: Date;
    meetCompleteTime!: Date;

    get trainingSessionDescription(): AbstractControl {
        return this.modalForm.controls['meetCompleteDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);
    }

    ngOnInit(): void {
        this.meetCompleteDate = this.meet.completeTime?.toJSDate();
        this.meetCompleteTime = this.meet.completeTime?.toJSDate();

        this.preferences.get(AppConsts.authorization.encrptedAuthTokenName).subscribe({
            next: (token) => {
                for (let resource of this.meet.meetCompleteResources)
                    resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
            }
        });
    }
}