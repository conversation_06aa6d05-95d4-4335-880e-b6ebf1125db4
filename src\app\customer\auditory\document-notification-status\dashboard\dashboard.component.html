<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Monitoreo de Autorización
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 100px">
                                    Tipo
                                </th>
                                <th style="min-width: 100px">
                                    Código
                                </th>
                                <th style="min-width: 100px">
                                    Nombre
                                </th>
                                <th style="min-width: 100px">
                                    Versión
                                </th>
                                <th style="min-width: 100px">
                                    Estado
                                </th>
                                <th style="min-width: 100px">
                                    Nombre Completo
                                </th>
                                <th style="min-width: 100px">
                                    Correo
                                </th>
                                <th style="min-width: 100px">
                                    Gerencia
                                </th>
                                <th style="min-width: 100px">
                                    Área
                                </th>
                                <th style="min-width: 100px">
                                    Fecha de Envío
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="DocumentTypeId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="typeArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Code"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Name"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Version"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="StatusId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="statusArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="EmployeeName"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Email"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="DocumentManagementId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="managementArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="DocumentAreaId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="areaArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="sendStartTime" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <app-date-input name="sendStartTime" [value]="value" [showLabel]="false"
                                                placeholder="Desde" (onDateChange)="filter($event)" />
                                        </ng-template>
                                    </p-columnFilter>
                                    <div class="d-block my-1"></div>
                                    <p-columnFilter field="sendEndTime" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <app-date-input name="sendEndTime" [value]="value" [showLabel]="false"
                                                placeholder="Hasta" (onDateChange)="filter($event)" />
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Tipo
                                    </span> {{record?.document?.documentType?.name}}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Code
                                    </span> {{record?.document?.code}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Name
                                    </span> {{record?.document?.name}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Version
                                    </span> {{record?.document?.version}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Status
                                    </span> {{ getStatus(record?.status) }}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Nombre
                                    </span> {{ getName(record) }}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Email
                                    </span> {{record?.email}}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Gerencia
                                    </span> {{ getManagement(record?.document?.documentManagementCode) }}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Area
                                    </span> {{ getArea(record?.document?.documentAreaCode) }}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Fecha de Envio
                                    </span> {{record?.dateSend | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>