<div class="h-100 w-100">
    <app-modal [title]="id ? 'Editar zona de alarma' : 'Crear zona de alarma'" [disabled]="disabled">
        <app-modal-body>
            <form class="row" [formGroup]="modalForm">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmZoneName" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmZoneName.invalid && (alarmZoneName.touched || alarmZoneName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmZoneName" name="AlarmZoneName"
                                formControlName="alarmZoneNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmZonePartition" class="form-label">
                                Partición
                            </label>

                            <input type="text" class="form-control" id="AlarmZonePartition" name="AlarmZonePartition"
                                formControlName="alarmZonePartitionInput">
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmZoneCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmZoneCode.invalid && (alarmZoneCode.touched || alarmZoneCode.dirty))}">
                                Código (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmZoneCode" name="AlarmZoneCode"
                                formControlName="alarmZoneCodeInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El código es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

            </form>

        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</div>