<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Mantenimiento
        </ion-title>
    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>
                <p-tabView [scrollable]="true">
                    <p-tabPanel header="Caches">
                        <ion-row>
                            <ion-col class="d-flex align-items-center" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6"
                                sizeXl="6">
                                <ion-label class="d-block fz-small">
                                    Puedes limpiar la cache en esta página.
                                </ion-label>
                            </ion-col>
                            <ion-col class="text-end" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
                                <ion-button (click)="clearAllCache()">
                                    <ion-icon size="normal" name="trash-bin" slot="start"></ion-icon>
                                    <ion-label class="fz-small">
                                        Borrar todo
                                    </ion-label>
                                </ion-button>
                            </ion-col>
                            <ion-col size="12">
                                <p-table sortMode="multiple" [value]="caches" [rows]="1000" [paginator]="false"
                                    [lazy]="false" [scrollable]="true" ScrollWidth="100%" scrollDirection="horizontal">
                                    <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                                        <tr>
                                            <td style="min-width: 200px;">
                                                {{record.name}}
                                            </td>
                                            <td class="action-column" style="min-width: 80px; max-width: 80px; width: 80px;">
                                                <ion-button (click)="clearCache(record)" class="my-2" color="primary" fill="solid" size="small">
                                                    <ion-icon size="small" name="trash-bin" slot="start"></ion-icon>
                                                    <ion-label class="fz-small">
                                                        Borrar
                                                    </ion-label>
                                                </ion-button>
                                            </td>
                                            <td style="min-width: 10px; max-width: 10px; width: 10px;"></td>
                                        </tr>
                                    </ng-template>
                                </p-table>
                                <div class="primeng-no-data" *ngIf="caches && caches.length == 0">
                                    No hay data
                                </div>
                            </ion-col>
                        </ion-row>
                    </p-tabPanel>
                    <p-tabPanel header="Registro del sitio web">
                        <ion-row>
                            <ion-col class="d-flex align-items-center" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6"
                                sizeXl="6">
                                <ion-label class="d-block fz-small">
                                    Puedes ver los últimos registros en esta página o descargar todos los registros en un archivo zip.
                                </ion-label>
                            </ion-col>
                            <ion-col class="text-end" sizeXs="12" sizeSm="12" sizeMd="6" sizeLg="6" sizeXl="6">
                                <ion-button (click)="refreshLogs()">
                                    <ion-icon size="normal" name="refresh" slot="start"></ion-icon>
                                    <ion-label class="fz-small">
                                        Refrescar
                                    </ion-label>
                                </ion-button>
                            </ion-col>
                            <ion-col size="12">
                                <div class="web-log-view full-height">
                                    <span class="log-line" *ngFor="let log of webLogs">
                                        <ion-badge [color]="getLogClass(log)">{{getLogType(log)}}</ion-badge>
                                        {{ getRawLogContent(log) }}
                                    </span>
                                </div>
                            </ion-col>
                        </ion-row>
                    </p-tabPanel>
                </p-tabView>
            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>