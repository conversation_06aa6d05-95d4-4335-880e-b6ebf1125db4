import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { MaintenanceFamilyDto, MaintenanceFamilyServiceProxy } from '@proxies/maintenance-family.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { MaintenanceFamilyCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-maintenance-family-dashboard.component',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class MaintenanceFamilyDashboardComponent extends ViewComponent {

    private readonly maintenanceFamilyServiceProxy: MaintenanceFamilyServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    staticStates: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceFamilyServiceProxy = _injector.get(MaintenanceFamilyServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: MaintenanceFamilyCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: MaintenanceFamilyDto): void {
        this.dialog.showWithData<boolean>({
            component: MaintenanceFamilyCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: MaintenanceFamilyDto): void {
        this.message.confirm(`¿Estas seguro de eliminar el registro "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.maintenanceFamilyServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el registro satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.maintenanceFamilyServiceProxy
            .getAll({
                name: this.dataTable?.filters?.['name']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator, event),
                skipCount: this.table.getSkipCount(this.paginator, event)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: MaintenanceFamilyDto): void {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.MaintenanceFamily.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.MaintenanceFamily.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.maintenanceFamilyServiceProxy
            .export({
                name: this.dataTable?.filters?.['name']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator),
                skipCount: this.table.getSkipCount(this.paginator)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters(): void {
        this.dataTable?.clear();
    }
}