import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { SpecificProtectionEquipmentRoutingModule } from './specific-protection-equipment.routing.module';
import { SpecificProtectionEquipmentDashboardComponent } from './dashboard/dashboard.component';
import { SpecificProtectionEquipmentCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    SpecificProtectionEquipmentRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    SpecificProtectionEquipmentDashboardComponent,
    SpecificProtectionEquipmentCreateEditComponent
  ]
})
export class SpecificProtectionEquipmentModule { }
