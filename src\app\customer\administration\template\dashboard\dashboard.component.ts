import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { TemplateCreateEditComponent } from '../create-edit/create-edit-component';
import { TemplateDto, TemplateServiceProxy } from '@proxies/template.proxy';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-template-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class TemplateDashboardComponent extends ViewComponent {

    private templateServiceProxy: TemplateServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    constructor(_injector: Injector) {
        super(_injector);

        this.templateServiceProxy = _injector.get(TemplateServiceProxy);
    }

    editItem(template: TemplateDto) {
        this.dialog.showWithData<boolean>({
            component: TemplateCreateEditComponent,
            componentProps: {
                id: template.id
            }
        }).then((response) => {
            if (response.data.result) 
                this.getData();            
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.templateServiceProxy
            .getAll(
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, data: TemplateDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Administration.User.Modify'
                ],
                callback: () => this.editItem(data)
            }
        ]);
    }
}