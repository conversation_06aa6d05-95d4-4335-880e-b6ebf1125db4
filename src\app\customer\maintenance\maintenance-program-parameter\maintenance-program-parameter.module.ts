import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceProgramParameterRoutingModule } from './maintenance-program-parameter.routing.module';
import { MaintenanceProgramParameterDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceProgramParameterCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    MaintenanceProgramParameterRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceProgramParameterDashboardComponent,
    MaintenanceProgramParameterCreateEditComponent
  ]
})
export class MaintenanceProgramParameterModule { }
