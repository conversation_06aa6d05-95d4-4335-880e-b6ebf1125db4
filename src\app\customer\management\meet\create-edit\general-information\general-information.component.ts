import { Component, EventEmitter, Injector, Input, Output } from '@angular/core';
import { AbstractControl, FormGroup } from '@angular/forms';
import { AppFindUserComponent } from '@components/find-user/find-user.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IFilterOption } from '@core/models/filters';
import { UploadResource } from '@core/utils/core.request';
import { IntegrationCenterLogisticDto } from '@proxies/integration.proxy';
import { MeetDto, MeetStatus, MeetTurn, MeetType, MeetUserDto } from '@proxies/meet.proxy';

@Component({
    selector: 'app-meet-general-information',
    templateUrl: 'general-information.component.html',
    styleUrls: [
        'general-information.component.scss'
    ]
})
export class MeetGeneralInformationComponent extends ViewComponent {

    private _meetDate!: Date;
    private _meetStartTime!: Date;
    private _meetEndTime!: Date;

    @Input() meet!: MeetDto;
    @Input() completed!: boolean;
    @Input() modalForm!: FormGroup;

    @Input() centers!: IntegrationCenterLogisticDto[];

    @Input() get meetDate(): Date {
        return this._meetDate;
    }

    @Input() get meetStartTime(): Date {
        return this._meetStartTime;
    }

    @Input() get meetEndTime(): Date {
        return this._meetEndTime;
    }

    @Output() meetDateChange: EventEmitter<Date> = new EventEmitter<Date>();
    @Output() meetStartTimeChange: EventEmitter<Date> = new EventEmitter<Date>();
    @Output() meetEndTimeChange: EventEmitter<Date> = new EventEmitter<Date>();

    set meetDate(value: Date) {
        this._meetDate = value;
        this.meetDateChange.emit(this._meetDate);
    }

    set meetStartTime(value: Date) {
        this._meetStartTime = value;
        this.meetStartTimeChange.emit(this._meetStartTime);
    }

    set meetEndTime(value: Date) {
        this._meetEndTime = value;
        this.meetEndTimeChange.emit(this._meetEndTime);
    }

    get meetTitle(): AbstractControl {
        return this.modalForm.controls['meetTitleInput'];
    };

    get meetDescription(): AbstractControl {
        return this.modalForm.controls['meetDescriptionInput'];
    };

    get meetCenter(): AbstractControl {
        return this.modalForm.controls['meetCenterSelect'];
    };

    get meetCode(): AbstractControl {
        return this.modalForm.controls['meetCodeInput'];
    };

    get meetStatus(): AbstractControl {
        return this.modalForm.controls['meetStatusSelect'];
    };

    get meetTurn(): AbstractControl {
        return this.modalForm.controls['meetTurnSelect'];
    };

    get meetType(): AbstractControl {
        return this.modalForm.controls['meetTypeSelect'];
    };

    meetTypeArray: IFilterOption<MeetType>[] = [
        { label: 'Seleccione', value: MeetType.None },
        { label: 'De rutina', value: MeetType.Recurrent },
        { label: 'A demanda', value: MeetType.OnDemand }
    ];

    meetTurnArray: IFilterOption<MeetTurn>[] = [
        { label: 'Seleccione', value: MeetTurn.None },
        { label: 'Diurno', value: MeetTurn.Diurnal },
        { label: 'Nocturno', value: MeetTurn.Nocturnal }
    ];

    meetStatusArray: IFilterOption<MeetStatus>[] = [
        { label: 'Seleccione', value: MeetStatus.None },
        { label: 'Programado', value: MeetStatus.Programmed },
        { label: 'Ejecutado', value: MeetStatus.Executed },
        { label: 'Cancelado', value: MeetStatus.Canceled }
    ];

    size: number = 15_728_640;

    constructor(_injector: Injector) {
        super(_injector);
    }

    showFindUser(): void {
        this.dialog.showWithData<MeetUserDto>({
            component: AppFindUserComponent
        }).then((response) => {
            if (response.data.result) {
                this.meet.leader = new MeetUserDto().fromJS(response.data.result);
            }
        });
    }
    
    onUploadItem(event: UploadResource): void {
        this.meet.uploadResources ??= [];
        this.meet.uploadResources.push(event);
    }

    onRemoveResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.meet.meetResources[index].remove = true;
            }
        });
    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.meet.uploadResources ??= [];
                this.meet.uploadResources.splice(index, 1);
            }
        });
    }
}