import { Component, Injector, Input, OnInit } from "@angular/core";
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ViewComponent } from "@core/inheritance/app-component.base";
import { TrainingDto, TrainingStatus } from "@proxies/training.proxy";
import { TrainingEvaluationServiceProxy, TrainingEvaluationDto, TrainingEvaluationCreateDto, TrainingEvaluationQuestionCreateDto, TrainingEvaluationUpdateDto, TrainingEvaluationQuestionUpdateDto } from "@proxies/training-evaluation.proxy";
import { finalize } from "rxjs";

@Component({
    selector: 'app-training-evaluation-information',
    templateUrl: 'evaluation-information.component.html',
    styleUrls: [
        'evaluation-information.component.scss'
    ]
})
export class TrainingEvaluationInformationComponent extends ViewComponent implements OnInit {

    @Input() training!: TrainingDto;
    @Input() modalForm!: FormGroup;
    @Input() completed!: boolean;

    evaluationForm!: FormGroup;
    private formBuilder: FormBuilder;
    private trainingEvaluationServiceProxy: TrainingEvaluationServiceProxy;

    evaluationData: TrainingEvaluationDto | null = null;
    isLoading: boolean = false;

    constructor(_injector: Injector) {
        super(_injector);
        this.formBuilder = _injector.get(FormBuilder);
        this.trainingEvaluationServiceProxy = _injector.get(TrainingEvaluationServiceProxy);
    }

    ngOnInit(): void {
        this.initializeEvaluationForm();
        if (this.training?.id) {
            this.loadEvaluationData();
        }
    }

    private loadEvaluationData(): void {
        this.isLoading = true;
        this.trainingEvaluationServiceProxy.get(this.training.id)
            .pipe(finalize(() => this.isLoading = false))
            .subscribe({
                next: (evaluation) => {
                    this.evaluationData = evaluation;
                    this.populateFormWithData(evaluation);
                },
                error: () => {
                    // Si no existe evaluación, mantener el formulario con valores por defecto
                    // No mostrar ningún mensaje, es normal que no exista evaluación al principio
                    this.evaluationData = null;
                }
            });
    }

    private populateFormWithData(evaluation: TrainingEvaluationDto): void {
        // Actualizar los campos principales
        this.evaluationForm.patchValue({
            evaluationTitle: evaluation.title,
            evaluationDescription: evaluation.description
        });

        // Limpiar preguntas existentes
        const questionsArray = this.evaluationForm.get('questions') as FormArray;
        while (questionsArray.length !== 0) {
            questionsArray.removeAt(0);
        }

        // Agregar preguntas desde la API
        evaluation.questions.forEach((question, index) => {
            const questionGroup = this.formBuilder.group({
                questionText: [question.questionText, Validators.required],
                options: this.formBuilder.array([
                    this.formBuilder.group({
                        optionText: [question.optionA, Validators.required],
                        isCorrect: [question.correctAnswer === 'A']
                    }),
                    this.formBuilder.group({
                        optionText: [question.optionB, Validators.required],
                        isCorrect: [question.correctAnswer === 'B']
                    }),
                    this.formBuilder.group({
                        optionText: [question.optionC, Validators.required],
                        isCorrect: [question.correctAnswer === 'C']
                    }),
                    this.formBuilder.group({
                        optionText: [question.optionD, Validators.required],
                        isCorrect: [question.correctAnswer === 'D']
                    })
                ]),
                points: [25, [Validators.required, Validators.min(1), Validators.max(100)]]
            });

            questionsArray.push(questionGroup);
        });
    }

    private initializeEvaluationForm(): void {
        this.evaluationForm = this.formBuilder.group({
            evaluationTitle: ['Evaluación de Capacitación Obligatoria', Validators.required],
            evaluationDescription: ['Evaluación para medir el conocimiento adquirido durante la capacitación', Validators.required],
            questions: this.formBuilder.array([])
        });

        // Agregar las 4 preguntas por defecto
        this.addDefaultQuestions();
    }

    private addDefaultQuestions(): void {
        const questionsArray = this.evaluationForm.get('questions') as FormArray;

        for (let i = 1; i <= 4; i++) {
            const questionGroup = this.formBuilder.group({
                questionText: [`Pregunta ${i}`, Validators.required],
                options: this.formBuilder.array([
                    this.formBuilder.group({
                        optionText: ['Opción A', Validators.required],
                        isCorrect: [false] // Inicializar explícitamente como false
                    }),
                    this.formBuilder.group({
                        optionText: ['Opción B', Validators.required],
                        isCorrect: [false]
                    }),
                    this.formBuilder.group({
                        optionText: ['Opción C', Validators.required],
                        isCorrect: [false]
                    }),
                    this.formBuilder.group({
                        optionText: ['Opción D', Validators.required],
                        isCorrect: [false]
                    })
                ]),
                points: [25, [Validators.required, Validators.min(1), Validators.max(100)]]
            });

            questionsArray.push(questionGroup);
        }
    }

    get questions(): FormArray {
        return this.evaluationForm.get('questions') as FormArray;
    }

    getQuestionOptions(questionIndex: number): FormArray {
        return this.questions.at(questionIndex).get('options') as FormArray;
    }

    onCorrectAnswerChange(questionIndex: number, optionIndex: number): void {
        const options = this.getQuestionOptions(questionIndex);

        // Para respuesta única, desmarcar todas las opciones primero
        for (let i = 0; i < options.length; i++) {
            options.at(i).get('isCorrect')?.setValue(false);
        }

        // Luego marcar solo la opción seleccionada como true
        options.at(optionIndex).get('isCorrect')?.setValue(true);

        // Los valores se han actualizado correctamente
    }

    addQuestion(): void {
        const questionsArray = this.questions;
        const questionNumber = questionsArray.length + 1;

        const newQuestion = this.formBuilder.group({
            questionText: [`Pregunta ${questionNumber}`, Validators.required],
            options: this.formBuilder.array([
                this.formBuilder.group({
                    optionText: ['Opción A', Validators.required],
                    isCorrect: [false] // Inicializar explícitamente como false
                }),
                this.formBuilder.group({
                    optionText: ['Opción B', Validators.required],
                    isCorrect: [false] // Inicializar explícitamente como false
                }),
                this.formBuilder.group({
                    optionText: ['Opción C', Validators.required],
                    isCorrect: [false] // Inicializar explícitamente como false
                }),
                this.formBuilder.group({
                    optionText: ['Opción D', Validators.required],
                    isCorrect: [false] // Inicializar explícitamente como false
                })
            ]),
            points: [25, [Validators.required, Validators.min(1), Validators.max(100)]]
        });

        questionsArray.push(newQuestion);
    }

    removeQuestion(index: number): void {
        if (this.questions.length > 1) {
            this.message.confirm('¿Está seguro de eliminar esta pregunta?', 'Confirmar eliminación', (confirmed) => {
                if (confirmed) {
                    this.questions.removeAt(index);
                }
            });
        } else {
            this.message.info('Debe mantener al menos una pregunta en la evaluación', 'Aviso');
        }
    }

    getTotalPoints(): number {
        let total = 0;
        for (let i = 0; i < this.questions.length; i++) {
            const points = this.questions.at(i).get('points')?.value || 0;
            total += points;
        }
        return total;
    }

    validateEvaluation(): boolean {
        if (!this.evaluationForm.valid) {
            this.message.info('Por favor complete todos los campos requeridos', 'Validación');
            return false;
        }
        // Validar que cada pregunta tenga exactamente una respuesta correcta
        for (let i = 0; i < this.questions.length; i++) {
                    console.log(this.questions);
            const options = this.getQuestionOptions(i);
            const correctAnswers = options.controls.filter(option =>
                option.get('isCorrect')?.value === true
            );

            if (correctAnswers.length === 0) {
                this.message.info(`La pregunta ${i + 1} debe tener una respuesta correcta`, 'Validación');
                return false;
            }

            if (correctAnswers.length > 1) {
                this.message.info(`La pregunta ${i + 1} solo puede tener una respuesta correcta`, 'Validación');
                return false;
            }
        }

        const totalPoints = this.getTotalPoints();
        if (totalPoints !== 100) {
            this.message.info(`El total de puntos debe ser 100. Actualmente es ${totalPoints}`, 'Validación');
            return false;
        }

        return true;
    }

    saveEvaluation(): void {
        if (this.validateEvaluation()) {
            this.isLoading = true;

            if (this.evaluationData && this.evaluationData.id) {
                // Actualizar evaluación existente
                const updateData = this.buildEvaluationUpdateDto();

                this.trainingEvaluationServiceProxy.update(updateData)
                    .pipe(finalize(() => this.isLoading = false))
                    .subscribe({
                        next: () => {
                            this.notify.success('Evaluación actualizada exitosamente', 3000);
                            this.loadEvaluationData();
                        },
                        error: (error) => {
                            console.error('Error updating evaluation:', error);
                            this.message.error('Error al actualizar la evaluación', 'Error');
                        }
                    });
            } else {
                // Crear nueva evaluación
                const createData = this.buildEvaluationCreateDto();

                this.trainingEvaluationServiceProxy.create(createData)
                    .pipe(finalize(() => this.isLoading = false))
                    .subscribe({
                        next: () => {
                            this.notify.success('Evaluación creada exitosamente', 3000);
                            this.loadEvaluationData();
                        },
                        error: (error) => {
                            console.error('Error creating evaluation:', error);
                            this.message.error('Error al crear la evaluación', 'Error');
                        }
                    });
            }
        }
    }

    private buildEvaluationCreateDto(): TrainingEvaluationCreateDto {
        const formValue = this.evaluationForm.value;

        const questions: TrainingEvaluationQuestionCreateDto[] = formValue.questions.map((question: any, index: number) => {
            // Encontrar cuál opción está marcada como correcta
            let correctAnswer = 'A'; // Por defecto
            question.options.forEach((option: any, optionIndex: number) => {
                if (option.isCorrect) {
                    correctAnswer = String.fromCharCode(65 + optionIndex); // A, B, C, D
                }
            });

            return new TrainingEvaluationQuestionCreateDto({
                questionText: question.questionText,
                optionA: question.options[0].optionText,
                optionB: question.options[1].optionText,
                optionC: question.options[2].optionText,
                optionD: question.options[3].optionText,
                correctAnswer: correctAnswer,
                order: index + 1
            });
        });

        return new TrainingEvaluationCreateDto({
            title: formValue.evaluationTitle,
            description: formValue.evaluationDescription,
            isActive: true,
            trainingId: this.training.id,
            questions: questions
        });
    }

    private buildEvaluationUpdateDto(): TrainingEvaluationUpdateDto {
        const formValue = this.evaluationForm.value;

        const questions: TrainingEvaluationQuestionUpdateDto[] = formValue.questions.map((question: any, index: number) => {
            // Encontrar cuál opción está marcada como correcta
            let correctAnswer = 'A'; // Por defecto
            question.options.forEach((option: any, optionIndex: number) => {
                if (option.isCorrect) {
                    correctAnswer = String.fromCharCode(65 + optionIndex); // A, B, C, D
                }
            });

            // Obtener el ID de la pregunta existente si existe
            const existingQuestion = this.evaluationData?.questions[index];

            return new TrainingEvaluationQuestionUpdateDto({
                id: existingQuestion?.id || 0, // Si es nueva pregunta, el backend asignará el ID
                questionText: question.questionText,
                optionA: question.options[0].optionText,
                optionB: question.options[1].optionText,
                optionC: question.options[2].optionText,
                optionD: question.options[3].optionText,
                correctAnswer: correctAnswer,
                order: index + 1,
                trainingEvaluationId: this.evaluationData!.id
            });
        });

        return new TrainingEvaluationUpdateDto({
            id: this.evaluationData!.id,
            title: formValue.evaluationTitle,
            description: formValue.evaluationDescription,
            isActive: true,
            trainingId: this.training.id,
            questions: questions
        });
    }

    get canEdit(): boolean {
        return this.training?.status !== TrainingStatus.Completed && !this.completed;
    }
}
