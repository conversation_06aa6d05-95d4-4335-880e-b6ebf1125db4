<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal
        [title]="id ? 'Editar parámetro de programa de actividades' : 'Crear parámetro de programa de actividades'"
        [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceProgramParameterName" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceProgramParameterName.invalid && (maintenanceProgramParameterName.touched || maintenanceProgramParameterName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceProgramParameterName"
                                name="MaintenanceProgramParameterName"
                                formControlName="maintenanceProgramParameterNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="MaintenanceProgramParameterEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="MaintenanceProgramParameterEnabled"
                                name="MaintenanceProgramParameterEnabled"
                                formControlName="maintenanceProgramParameterEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="MaintenanceProgramParameterType">
                                Tipo (*)
                            </label>
                            <select (change)="onTypeChange($event)" class="form-control"
                                id="MaintenanceProgramParameterType" name="MaintenanceProgramParameterType"
                                formControlName="maintenanceProgramParameterTypeSelect">
                                <option [value]="maintenanceProgramParameters.none">
                                    Seleccione
                                </option>
                                <option [value]="maintenanceProgramParameters.text">
                                    Textual
                                </option>
                                <option [value]="maintenanceProgramParameters.numeric">
                                    Numérico
                                </option>
                                <option [value]="maintenanceProgramParameters.range">
                                    Rango
                                </option>
                                <option [value]="maintenanceProgramParameters.file">
                                    Fichero
                                </option>
                                <option [value]="maintenanceProgramParameters.image">
                                    Imagen
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <ng-container
                    *ngIf="item.type == maintenanceProgramParameters.numeric || item.type == maintenanceProgramParameters.range">

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label class="form-label" for="MaintenanceProgramParameterHasPrecision">
                                    ¿Es decimal? (*)
                                </label>
                                <select (change)="onHasPrecisionChange($event)" class="form-control"
                                    id="MaintenanceProgramParameterHasPrecision"
                                    name="MaintenanceProgramParameterHasPrecision"
                                    formControlName="maintenanceProgramParameterHasPrecisionSelect">
                                    <option value="true">
                                        Si
                                    </option>
                                    <option value="false">
                                        No
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="item.hasPrecision" class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label class="form-label" for="MaintenanceProgramParameterPrecision">
                                    Cantidad de decimales (*)
                                </label>
                                <select (change)="onPresicionChange($event)" class="form-control"
                                    id="MaintenanceProgramParameterPrecision"
                                    name="MaintenanceProgramParameterPrecision"
                                    formControlName="maintenanceProgramParameterPrecisionSelect">
                                    <option *ngFor="let i of avaliablePrecisions" [value]="i">
                                        {{i}}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <ng-container *ngIf="!item.hasPrecision">

                        <div class="col-12 col-sm-12 col-md-12 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                            <div class="ion-input mb-2">
                                <div class="input-control">

                                    <label class="form-label" for="MaintenanceProgramParameterMin">
                                        Valor mínimo
                                    </label>

                                    <input inputNumber type="text" class="form-control"
                                        id="MaintenanceProgramParameterMin" name="MaintenanceProgramParameterMin"
                                        formControlName="maintenanceProgramParameterMinInput" maxlength="9">
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12 col-md-12 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                            <div class="ion-input mb-2">
                                <div class="input-control">

                                    <label class="form-label" for="MaintenanceProgramParameterMax">
                                        Valor máximo
                                    </label>

                                    <input inputNumber type="text" class="form-control"
                                        id="MaintenanceProgramParameterMax" name="MaintenanceProgramParameterMax"
                                        formControlName="maintenanceProgramParameterMaxInput" maxlength="9">
                                </div>
                            </div>
                        </div>

                    </ng-container>

                    <ng-container *ngIf="item.hasPrecision">

                        <div class="col-12 col-sm-12 col-md-12 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                            <div class="ion-input mb-2">
                                <div class="input-control">

                                    <label class="form-label" for="MaintenanceProgramParameterMin">
                                        Valor mínimo
                                    </label>

                                    <input inputDecimal [precision]="item.precision" type="text" class="form-control"
                                        id="MaintenanceProgramParameterMin" name="MaintenanceProgramParameterMin"
                                        formControlName="maintenanceProgramParameterMinInput" maxlength="9">
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12 col-md-12 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                            <div class="ion-input mb-2">
                                <div class="input-control">

                                    <label class="form-label" for="MaintenanceProgramParameterMax">
                                        Valor máximo
                                    </label>

                                    <input inputDecimal [precision]="item.precision" type="text" class="form-control"
                                        id="MaintenanceProgramParameterMax" name="MaintenanceProgramParameterMax"
                                        formControlName="maintenanceProgramParameterMaxInput" maxlength="9">
                                </div>
                            </div>
                        </div>

                    </ng-container>

                </ng-container>


                <ng-container
                    *ngIf="item.type == maintenanceProgramParameters.image || item.type == maintenanceProgramParameters.file">

                    <div class="col-12 col-sm-12 col-md-12 col-lg-6 col-xl-6 col-xxl-6 mb-4"
                        [ngClass]="{'ng-invalid' : (maintenanceProgramParameterQuantity.invalid && (maintenanceProgramParameterQuantity.touched || maintenanceProgramParameterQuantity.dirty))}">
                        <div class="ion-input mb-2">
                            <div class="input-control">

                                <label class="form-label" for="MaintenanceProgramParameterQuantity">
                                    Cantidad (*)
                                </label>

                                <input inputNumber type="text" class="form-control"
                                    id="MaintenanceProgramParameterQuantity" name="MaintenanceProgramParameterQuantity"
                                    formControlName="maintenanceProgramParameterQuantityInput" maxlength="9">
                            </div>
                        </div>
                    </div>

                </ng-container>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="MaintenanceProgramParameterRequired">
                                Obligatorio? (*)
                            </label>
                            <select class="form-control" id="MaintenanceProgramParameterRequired"
                                name="MaintenanceProgramParameterRequired"
                                formControlName="maintenanceProgramParameterRequiredSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceProgramParameterCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="MaintenanceProgramParameterCode"
                                name="MaintenanceProgramParameterCode"
                                formControlName="maintenanceProgramParameterCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>