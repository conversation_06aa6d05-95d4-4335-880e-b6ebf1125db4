import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { PersonAreaRoutingModule } from './person-area.routing.module';
import { PersonAreaDashboardComponent } from './dashboard/dashboard.component';
import { PersonAreaCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    PersonAreaRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    PersonAreaDashboardComponent,
    PersonAreaCreateEditComponent
  ]
})
export class PersonAreaModule { }
