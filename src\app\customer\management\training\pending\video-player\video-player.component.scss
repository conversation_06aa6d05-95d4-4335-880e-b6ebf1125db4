.video-container {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease-in-out;

    &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
}

.video-header {
    border-bottom: 1px solid #f1f3f4;
    padding-bottom: 0.5rem;
}

.video-player {
    video {
        border-radius: 4px;
        background: #000;
        
        &:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
        }
    }
}

.fw-bold {
    font-weight: 600 !important;
}

.text-muted {
    color: #6c757d !important;
}

@media (max-width: 768px) {
    .video-container {
        padding: 0.75rem;
    }
    
    .video-player video {
        height: 200px;
    }
}
