import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { EntityDto } from '@core/utils/core.request';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { AlarmAccountDto } from '@proxies/alarm-account.proxy';
import { AlarmUserDto, AlarmUserServiceProxy } from '@proxies/alarm-user.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-alarm-user-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class AlarmUserCreateEditComponent extends ViewComponent implements OnInit {

    private alarmUserServiceProxy: AlarmUserServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;
    @Input() alarmAccount: AlarmAccountDto;
    
    item: AlarmUserDto = new AlarmUserDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get alarmUserName(): AbstractControl {
        return this.modalForm.controls['alarmUserNameInput'];
    };

    get alarmUserPartition(): AbstractControl {
        return this.modalForm.controls['alarmUserPartitionInput'];
    };

    get alarmUserCode(): AbstractControl {
        return this.modalForm.controls['alarmUserCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmUserServiceProxy = _injector.get(AlarmUserServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            alarmUserNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(150)])],
            alarmUserPartitionInput: ['', Validators.compose([Validators.required, Validators.maxLength(2)])],
            alarmUserCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(4)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.alarmUserServiceProxy
                .get(this.alarmAccount.id, this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.alarmUserName.setValue(this.item.name);
                        this.alarmUserPartition.setValue(this.item.partition);
                        this.alarmUserCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new AlarmUserDto();
            this.item.alarmAccount = new EntityDto<string>().fromJS(this.alarmAccount);
        }
    }

    async save(): Promise<void> {

        this.item.name = this.alarmUserName.value;
        this.item.partition = this.alarmUserPartition.value;
        this.item.code = this.alarmUserCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre del usuario de alarma es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código del usuario de alarma es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.alarmUserServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Usuario de alarma actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.alarmUserServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Usuario de alarma creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}