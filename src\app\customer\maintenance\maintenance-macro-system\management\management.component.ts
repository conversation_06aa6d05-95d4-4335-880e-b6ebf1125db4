import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { MaintenanceMacroSystemDto, MaintenanceMacroSystemServiceProxy } from '@proxies/maintenance-macro-system.proxy';
import { MaintenanceMicroSystemDto, MaintenanceMicroSystemMaintenanceMacroSystemDto, MaintenanceMicroSystemServiceProxy } from '@proxies/maintenance-micro-system.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { MaintenanceMicroSystemCreateEditComponent } from '../../maintenance-micro-system/create-edit/create-edit.component';

@Component({
    selector: 'app-maintenance-macro-system-management',
    templateUrl: 'management.component.html',
    styleUrls: [
        'management.component.scss'
    ]
})
export class MaintenanceMacroSystemManagementComponent extends ViewComponent implements OnInit {

    private readonly maintenanceMacroSystemServiceProxy: MaintenanceMacroSystemServiceProxy;
    private readonly maintenanceMicroSystemServiceProxy: MaintenanceMicroSystemServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    @Input() id!: number;

    item!: MaintenanceMacroSystemDto;
    loaded: boolean = false;

    constructor(injector: Injector) {
        super(injector);

        this.maintenanceMacroSystemServiceProxy = injector.get(MaintenanceMacroSystemServiceProxy);
        this.maintenanceMicroSystemServiceProxy = injector.get(MaintenanceMicroSystemServiceProxy);
        this.fileDownloadService = injector.get(AppFileDownloadService);

        this.table.defaultRecordsCountPerPage = this.table.predefinedRecordsCountPerPage[0];
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.maintenanceMacroSystemServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.maintenanceMicroSystemServiceProxy
            .getAll({
                maintenanceMacroSystemId: this.id,
                name: this.dataTable?.filters?.['name']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator, event),
                skipCount: this.table.getSkipCount(this.paginator, event)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: MaintenanceMicroSystemDto): void {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.MaintenanceMicroSystem.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.MaintenanceMicroSystem.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: MaintenanceMicroSystemCreateEditComponent,
            componentProps: {
                maintenanceMacroSystem: new MaintenanceMicroSystemMaintenanceMacroSystemDto().fromJS(this.item)
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: MaintenanceMicroSystemDto): void {
        this.dialog.showWithData<boolean>({
            component: MaintenanceMicroSystemCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: MaintenanceMicroSystemDto): void {
        this.message.confirm(`¿Estas seguro de eliminar el registro "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.maintenanceMicroSystemServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el registro satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    resetFilters(): void {
        this.dataTable?.clear();
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.maintenanceMicroSystemServiceProxy
            .export({
                maintenanceMacroSystemId: this.id,
                name: this.dataTable?.filters?.['name']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator),
                skipCount: this.table.getSkipCount(this.paginator)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }
}