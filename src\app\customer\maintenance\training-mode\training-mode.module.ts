import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { TrainingModeRoutingModule } from './training-mode.routing.module';
import { TrainingModeDashboardComponent } from './dashboard/dashboard.component';
import { TrainingModeCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    TrainingModeRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    TrainingModeDashboardComponent,
    TrainingModeCreateEditComponent
  ]
})
export class TrainingModeModule { }
