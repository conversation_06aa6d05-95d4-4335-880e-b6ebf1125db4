<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar proceso' : 'Crear proceso'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="ProcessName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (processName.invalid && (processName.touched || processName.dirty))}">
                                        Nombre (*)
                                    </label>
                                    <input type="text" class="form-control" id="ProcessName" name="ProcessName"
                                        formControlName="processNameInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="ProcessCode" class="form-label"
                                        [ngClass]="{'ng-invalid' : (processCode.invalid && (processCode.touched || processCode.dirty))}">
                                        Código (*)
                                    </label>
                                    <input type="text" class="form-control" id="ProcessCode" name="ProcessCode"
                                        formControlName="processCodeInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El código es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="ion-input mb-4">
                                    <div class="input-control">
                                        <label for="ProcessMacroProcess" class="form-label"
                                            [ngClass]="{'ng-invalid' : (processMacroProcess.invalid && (processMacroProcess.touched || processMacroProcess.dirty))}">
                                            MacroProceso
                                        </label>
                                        <select class="form-control" style="z-index: 1;" id="ProcessMacroProcess"
                                            name="ProcessMacroProcess"
                                            formControlName="processMacroProcessSelect">
                                            <option value="-1">
                                                Seleccione
                                            </option>
                                            <option *ngFor="let macro of macroProcess"
                                                [value]="macro.id">
                                                {{macro.name}}
                                            </option>
                                        </select>

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Seleccione un macroproceso
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>