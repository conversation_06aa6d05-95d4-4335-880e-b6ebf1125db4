import { Component, Injector, Input, OnD<PERSON>roy, OnInit, Output, ViewChild, EventEmitter } from '@angular/core';
import { AppFindEmployeeComponent } from '@components/find-employee/find-employee.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { EmployeeHierarchyDto, EmployeeHierarchyServiceProxy } from '@proxies/employee-hierarchy.proxy';
import { EmployeeDto } from '@proxies/employee.proxy';
import { HierarchyDto } from '@proxies/hierarchy.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Subscription, finalize } from 'rxjs';
import { EmployeeCreateEditComponent } from '../../employee/create-edit/create-edit.component';
import { EntityDto } from '@core/utils/core.request';

@Component({
    selector: 'app-hierarchy-employee-hierarchy',
    templateUrl: 'employee-hierarchy.component.html',
    styleUrls: [
        'employee-hierarchy.component.scss'
    ]
})
export class HierarchyEmployeeHierarchyComponent extends ViewComponent implements OnInit, OnD<PERSON>roy {

    private readonly employeeHierarchyServiceProxy: EmployeeHierarchyServiceProxy;
    private _hierarchy: HierarchyDto;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    @Input() get hierarchy(): HierarchyDto {
        return this._hierarchy;
    }

    set hierarchy(value: HierarchyDto) {
        this._hierarchy = value;
        this.getData();
    }

    @Output() onEmployeeChange: EventEmitter<void> = new EventEmitter<void>();

    private subscription: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.employeeHierarchyServiceProxy = _injector.get(EmployeeHierarchyServiceProxy);
    }

    ngOnInit(): void {

    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    async createItem(): Promise<void> {

        const employeeIds: EntityDto<number>[] = await this.getAllEmployees(this.hierarchy.id);

        this.dialog.showWithData<EmployeeDto>({
            component: AppFindEmployeeComponent,
            componentProps: {
                selecteds: employeeIds.map(p => p.id)
            }
        }).then(async (response) => {
            if (response.data.result) {
                const loading = await this.loader.show();

                this.employeeHierarchyServiceProxy
                    .create(this.hierarchy.id, response.data.result.employee.empid)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Empleado agregado satisfactoriamente');
                            this.getData();
                            this.onEmployeeChange.emit();
                        }
                    });
            }
        });
    }

    editItem(item: EmployeeHierarchyDto) {
        this.dialog.showWithData<boolean>({
            component: EmployeeCreateEditComponent,
            componentProps: {
                id: item.employee.empid
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        })
    }

    deleteItem(item: EmployeeHierarchyDto) {
        this.message.confirm(`¿Estas seguro de eliminar el empleado "${((item.employee.firstName || '').trim() + ' ' + (item.employee.middleName || '').trim()).trim()} ${(item.employee.lastName || '').trim()}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.employeeHierarchyServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el registro satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.employeeHierarchyServiceProxy
            .getAll(
                this.hierarchy.id,
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    async showActions(event: any, item: EmployeeHierarchyDto): Promise<void> {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: ['Pages.Management.Employee.Modify'],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Management.Hierarchy.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    private async getAllEmployees(hierarchyId: number): Promise<EntityDto<number>[]> {
        return new Promise(async (resolve, reject) => {

            const loading = await this.loader.show();

            this.employeeHierarchyServiceProxy
                .getAllEmployees(hierarchyId)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        resolve(response.items);
                    },
                    error: () => reject()
                });
        });
    }
}