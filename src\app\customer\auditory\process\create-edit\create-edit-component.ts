import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { MacroProcessDto, MacroProcessServiceProxy } from '@proxies/auditory/macro-process.proxy';
import { ProcessServiceProxy, ProcessDto } from '@proxies/auditory/process.proxy'
import { finalize } from 'rxjs';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})

export class ProcessCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;

    private processServiceProxy: ProcessServiceProxy;
    private macroProcessServiceProxy: MacroProcessServiceProxy;
    private formBuilder: FormBuilder;

    item: ProcessDto = new ProcessDto();
    macroProcess: MacroProcessDto[] = [];
    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get processCode(): AbstractControl {
        return this.modalForm.controls['processCodeInput'];
    };

    get processName(): AbstractControl {
        return this.modalForm.controls['processNameInput'];
    };

    get processMacroProcess(): AbstractControl {
        return this.modalForm.controls['processMacroProcessSelect'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.processServiceProxy = _injector.get(ProcessServiceProxy);
        this.macroProcessServiceProxy = _injector.get(MacroProcessServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            processCodeInput: ['', Validators.compose([Validators.required])],
            processNameInput: ['', Validators.compose([Validators.required])],
            processMacroProcessSelect: ['-1', Validators.compose([Validators.required])],
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (this.id) {
            this.processServiceProxy
                .get(this.id)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: (response) => {
                        this.item = response;
                        this.processName.setValue(this.item.name);
                        this.processCode.setValue(this.item.code);
                        this.processMacroProcess.setValue(this.item.macroprocessid);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        }
        else {
            await loading.dismiss();
            this.disabled = false;
        }
    }

    async save(): Promise<void> {
        this.item.code = this.processCode.value;
        this.item.name = this.processName.value;
        this.item.macroprocessid = this.processMacroProcess.value;

        if (!this.item.code) {
            this.message.info('El código es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.macroprocessid) {
            this.message.info('El macro proceso es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.processServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Proceso actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.processServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Proceso creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private loadInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.macroProcessServiceProxy
                .getAllFilter()
                .subscribe({
                    next: (response) => {
                        this.macroProcess = response.items
                        resolve();

                    }, error: () => reject()
                });
        });
    }
}