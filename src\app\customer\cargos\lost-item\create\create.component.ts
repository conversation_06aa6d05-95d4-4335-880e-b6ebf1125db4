import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { RegisterCharge, RegisterChargeResource, RegisterChargeView } from '@core/models/modelsSCA/register-charge';
import { ChargeService } from '@core/services/servicesSCA/charges.service';
import { PersonDocumentTypeDto, PersonDto, PersonPersonAreaDto } from '@proxies/person.proxy';
import { CargoDocumentInformationComponent } from './document-information/document-information.component';
import { CargoGeneralInformationComponent } from './general-information/general-information.component';
import { UploadResource } from '@core/utils/core.request';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { IUploadProgressRespose } from '@core/models/app-config';
import { AppFileUploadProgressScaComponent } from '@components/file-upload-progress-sca/file-upload-progress-sca.component';
import { image } from 'd3';

const enum PersonIndexes {
    GeneralInformation,
    DocumentInformation,
    CodeInformation
}

const enum Step {
    Search,
    Typing
}

@Component({
    selector: 'app-create-lost-item',
    templateUrl: './create.component.html',
    styleUrls: ['./create.component.scss'],
})
export class LostItemCreateComponent extends ViewComponent implements OnInit {
    @ViewChild("infoCharge") controlInfoCharge: CargoGeneralInformationComponent

    @ViewChild("infoDocument") controlInfoDocument: CargoDocumentInformationComponent
    @Input() registerCharge: RegisterChargeView
    @Input() id: number;

    private readonly formBuilder: FormBuilder;
    private registerChargeService: ChargeService;

    item: PersonDto = new PersonDto();
    documentTypes: PersonDocumentTypeDto[];
    personAreas: PersonPersonAreaDto[];
    disabled: boolean = false;
    loaded: boolean = false;
    modalForm!: FormGroup;
    activeIndex: PersonIndexes = PersonIndexes.GeneralInformation;
    step: Step = Step.Search;
    steps = {
        search: Step.Search,
        typing: Step.Typing
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.formBuilder = _injector.get(FormBuilder);
        this.registerChargeService = _injector.get(ChargeService);
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        try {

            this.disabled = false;
            if (this.registerCharge) {
                console.log("Cargando data....");
                this.disabled = true;
            } 
            this.loaded = true; 
        } catch {
            await this.dialog.dismiss();
        }

        await loading.dismiss();
    }

    mapToRegisterChargeResource(request: UploadResource): RegisterChargeResource {
        return {
            fileName: request.file.name,
            size: request.size.toString(),
            extension: request.file.name.split(".").pop(),
            className: "",
            name: `${request.token}.${request.file.name.split(".").pop()}`,
            token: request.token,
        }
    }

    async save(): Promise<void> {
        let request: RegisterCharge = await this.controlInfoCharge.getRegisterCharge();
        let resources: UploadResource[] = await this.controlInfoCharge.getFirmas();
        let imageResource = this.controlInfoDocument.imageUploadResource;
        let documentResource = this.controlInfoDocument.documentUploadResource;
        if (imageResource)
            resources.push(imageResource);
        if (documentResource)
            resources.push(documentResource)
        this.uploads(resources, async () => {
            request.signatureReceptorResource = this.mapToRegisterChargeResource(resources[1]);
            request.SignatureEmisorResource = this.mapToRegisterChargeResource(resources[0]);
            if (documentResource) request.DocumentResource = this.mapToRegisterChargeResource(documentResource);
            if (imageResource) request.ImageResource = this.mapToRegisterChargeResource(imageResource);

            this.registerChargeService.save(request).subscribe(next => {
                this.notify.success('Registro Cargo actualizada exitosamente', 5000);
                this.dialog.dismiss(true);

            })
        });
    }

    private uploads(uploadResources: UploadResource[], callback: () => void) {

        if (uploadResources.length == 0) {
            callback();
        } else {
            this.dialog.show({
                component: AppFileUploadProgressScaComponent,//sca
                componentProps: {
                    files: uploadResources.map(p => p.file),
                    source: 1,
                    processed: (data: IUploadProgressRespose) => {
                        if (data.completed) {
                            let index: number = 0;

                            for (let token of data.tokens) {
                                uploadResources[index].token = token;
                                index++;
                            }
                            callback();
                        }
                    }
                }
            });
        }
    }
}
