import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PolicyRoutingModule } from './policy.routing.module';
import { PolicyDashboardComponent } from './dashboard/dashboard.component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';
import { PolicyCreateEditComponent } from './create-edit/create-edit-component';
import { RequerimentCreateEditComponent } from './create-edit/requeriment/create-edit/create-edit-component';
import { RequerimentDashboardComponent } from './create-edit/requeriment/dashboard/dashboard.component';

@NgModule({
  imports: [
    PolicyRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    PolicyCreateEditComponent,
    PolicyDashboardComponent,
    RequerimentCreateEditComponent,
    RequerimentDashboardComponent
  ]
})
export class PolicyModule { }