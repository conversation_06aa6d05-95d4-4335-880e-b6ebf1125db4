<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Documentos Pendientes
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 100px; max-width: 100px; width: 100px">
                                    Acciones
                                </th>
                                <th style="min-width: 20px">
                                    ID
                                </th>
                                <th style="min-width: 100px">
                                    Tipo Documento
                                </th>
                                <th style="min-width: 100px">
                                    Estado
                                </th>
                                <th style="min-width: 100px">
                                    Código
                                </th>
                                <th style="min-width: 100px">
                                    Nombre
                                </th>
                                <th style="min-width: 100px">
                                    Proceso
                                </th>
                                <th style="min-width: 100px">
                                    Área
                                </th>
                                <th style="min-width: 60px">
                                    Versión
                                </th>
                                <th style="min-width: 100px">
                                    Fecha de Vigencia
                                </th>
                                <th style="min-width: 100px">
                                    Fecha de Actualización
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 80px; max-width: 80px; width: 80px">
                                </th>
                                <th style="min-width: 20px; width: 20px; max-width: 20px;">
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="DocumentTypeId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="documentTypesArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="DocumentStatusId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="documentStatusArray"
                                                [showHeader]="false" [showClear]="true" optionLabel="label"
                                                appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Code"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Name"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="DocumentProcessId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="documentProcessArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="DocumentAreaId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="documentAreaArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 60px; width: 60px; max-width: 60px;">
                                    <p-columnFilter type="text" field="Version"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 80px; max-width: 80px; width: 80px">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        ID
                                    </span> {{record?.id}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        TypeName
                                    </span> {{record?.documentType?.name}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Status
                                    </span> {{record?.documentStatus?.name}}
                                </td>
                                <td style="min-width: 200px">
                                    <span class="p-column-title">
                                        Code
                                    </span> {{record?.code}}
                                </td>
                                <td style="min-width: 200px">
                                    <span class="p-column-title">
                                        Name
                                    </span> {{record?.name?.toUpperCase()}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Process
                                    </span> {{ getProcess(record?.documentProcess?.processid) }}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Area
                                    </span> {{ getArea(record?.documentAreaCode) }}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Version
                                    </span> {{record?.version}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        CreatedAt
                                    </span> {{record?.createdAt | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        LastModified
                                    </span> {{record?.lastModified | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>