<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Monitoreo de alarmas
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="export()" class="ion-option me-2" color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

            <ion-button (click)="getData()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <div class="row">

                <div class="col-12 mb-4">

                    <label class="d-block fz-normal fw-bold text-dark">
                        Verificación de conexión
                    </label>

                    <label class="d-block fz-small text-dark">
                        Estado: <span class="fw-bold" [ngClass]="{ 
                                'text-warning' : status == 'starting' || status == 'reconnecting', 
                                'text-danger' : status == 'disconnected', 
                                'text-success' : status == 'connected' 
                                }">
                            {{
                            status == 'starting' ? 'Iniciando' :
                            status == 'reconnecting' ? 'Reconectando' :
                            status == 'disconnected' ? 'Desconectado' :
                            status == 'connected' ? 'Conectado' :
                            'Desconocido'
                            }}
                        </span>
                    </label>

                    <label class="d-block fz-small text-dark">
                        Detalles: <span class="text-danger">
                            {{description}}
                        </span>
                    </label>

                </div>

                <div class="col-12 mb-4">

                    <div class="monitoring-statistic__container">
                        <div *ngFor="let statistic of statistics" class="monitoring-statistic__item"
                            [style.--background]="statistic.background" [style.--color]="statistic.color">
                            <img class="monitoring-statistic__icon" src="/assets/icons/{{statistic.icon}}"
                                aria-label="Statistic Label">
                            <span class="monitoring-statistic__label">
                                {{statistic.name}}
                            </span>
                            <div class="monitoring-statistic__badge">
                                {{statistic.count}}
                                <div class="monitoring-statistic__badge-box"></div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Management.Monitoring.Modify',
                                        'Pages.Management.Monitoring.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th class="text-center" style="min-width: 180px; max-width: 180px; width: 180px"
                                    pSortableColumn="CenterName">
                                    Centro logístico
                                    <p-sortIcon field="CenterName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="PropertyName">
                                    Almacén
                                    <p-sortIcon field="PropertyName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="CustomerDocument">
                                    RUC
                                    <p-sortIcon field="CustomerDocument"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 250px; max-width: 250px; width: 250px"
                                    pSortableColumn="CustomerName">
                                    Razón Social
                                    <p-sortIcon field="CustomerName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 180px; max-width: 180px; width: 180px"
                                    pSortableColumn="AlarmName">
                                    Alarma
                                    <p-sortIcon field="AlarmName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="AlarmTypeName">
                                    Evento
                                    <p-sortIcon field="AlarmTypeName"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px"
                                    pSortableColumn="Status">
                                    Estado de atención
                                    <p-sortIcon field="Status"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="AlarmZone">
                                    Zona
                                    <p-sortIcon field="AlarmZone"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="IsReal">
                                    ¿Es real?
                                    <p-sortIcon field="IsReal"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="Canceled">
                                    Estado real
                                    <p-sortIcon field="Canceled"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="AlarmTime">
                                    Fecha y hora
                                    <p-sortIcon field="AlarmTime"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="AlarmId">
                                    Código
                                    <p-sortIcon field="AlarmId"></p-sortIcon>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px"
                                    [hidden]="
                                !(
                                    [
                                        'Pages.Management.Monitoring.Modify',
                                        'Pages.Management.Monitoring.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 180px; max-width: 180px; width: 180px">
                                    {{record.centerName}}
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.propertyName}}
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.customerDocument}}
                                </td>
                                <td style="min-width: 250px; max-width: 250px; width: 250px">
                                    {{record.customerName}}
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.alarmName}}
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.alarmTypeName}}
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    <ion-badge color="danger" *ngIf="record.status == monitoringAlarmStatuses.pending">
                                        Pendiente de atención
                                    </ion-badge>
                                    <ion-badge color="warning" *ngIf="record.status == monitoringAlarmStatuses.process">
                                        En curso
                                    </ion-badge>
                                    <ion-badge color="success"
                                        *ngIf="record.status == monitoringAlarmStatuses.completed">
                                        Atendido
                                    </ion-badge>
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.alarmZoneName ? record.alarmZoneName : record.alarmZone}}
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ng-container
                                        *ngIf="record.status == monitoringAlarmStatuses.process || record.status == monitoringAlarmStatuses.completed">
                                        <ion-badge color="danger" *ngIf="record.isReal">
                                            Si
                                        </ion-badge>
                                        <ion-badge color="warning" *ngIf="!record.isReal">
                                            No
                                        </ion-badge>
                                    </ng-container>
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ion-badge color="success" *ngIf="record.canceled">
                                        Finalizada
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record.canceled">
                                        En curso
                                    </ion-badge>
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.alarmTime | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.alarmId}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </div>
            </div>
        </ion-card-content>
    </ion-card>
</ion-content>