<form [formGroup]="loginForm" class="d-flex flex-column">

    <ion-label class="d-block text-center h4 mb-3">
        Iniciar sesión
    </ion-label>

    <div *ngIf="'App.Authentication.Microsoft.IsEnabled' | setting">
    
        <ion-button (click)="loginWithMicrosoft()" color="light" size="normal" expand="block">
            <ion-icon src="/assets/icons/microsoft.svg" slot="start">
            </ion-icon>
            <ion-label>
                Inicia sesión con Microsoft
            </ion-label>
        </ion-button>
        
    </div>


        <ion-label *ngIf="!showCredentialsForm" (click)="showCredentialsForm = !showCredentialsForm" 
            class="mb-3 mt-4 text-center d-block text-muted credentials-label">
            Otro metodo de inicio de sesión
        </ion-label>

    
    <div *ngIf="showCredentialsForm">
        <div class="form-group mb-4">
            <label class="form-label" for="EmailAddress">
                Correo electrónico
            </label>
            <div class="form-control-group">
                <input type="email" id="EmailAddress" class="form-control" formControlName="emailAdressInput">
                <div class="form-control-validation">
                    {{ emailAddress.value ? 'El correo electrónico no es válido.' : 'El correo electrónico es obligatorio.'
                    }}
                </div>
            </div>
        </div>

        <div class="form-group mb-3">
            <label class="form-label" for="Password">
                Contraseña
            </label>
            <div class="form-control-group">
                <input [type]="inputPassword.type" id="Password" class="form-control" formControlName="passwordInput"
                    autocomplete="off">
                <ion-button *ngIf="password.value" (click)="inputPassword.toggle()" class="form-action-right" fill="clear"
                    size="small">
                    <ion-icon [name]="inputPassword.icon"></ion-icon>
                </ion-button>
                <div class="form-control-validation">
                    La contraseña es obligatoria.
                </div>
            </div>
        </div>

        <ion-button routerLink="/account/forgot-password" routerDirection="root" class="ms-auto mb-3" size="small"
            fill="clear">
            <ion-label>
                ¿Olvidaste la contraseña?
            </ion-label>
        </ion-button>

        <ion-button (click)="login()" size="normal" expand="block">
            <ion-label>
                Iniciar sesión
            </ion-label>
        </ion-button>
    </div>

</form>