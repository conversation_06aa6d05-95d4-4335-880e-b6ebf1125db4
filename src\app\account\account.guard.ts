import { Injectable, Injector, inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateChild, CanActivateChildFn, RouterStateSnapshot } from '@angular/router';
import { AppNavigationService } from '@core/services/router.service';
import { AppSessionService } from '@core/services/session.service';

@Injectable()
export class AccountGuard  {
    private _sessionService: AppSessionService;
    private _navigationService: AppNavigationService;

    constructor(_injector: Injector) {
        this._sessionService = _injector.get(AppSessionService);
        this._navigationService = _injector.get(AppNavigationService);
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
        return this.canVerification(route, state);
    }

    canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        return this.canVerification(route, state);
    }

    private canVerification(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        if (this._sessionService.user) {
            this._navigationService.root('/customer/welcome/dashboard', 'forward');
            return false;
        }

        return true;
    }
}