import { Component, Injector, Input, OnInit, OnDestroy, NgModule } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { DocumentServiceProxy, DocumentStatusDto, Status } from '@proxies/auditory/document.proxy';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { finalize, Subscription } from 'rxjs';
import { DateTime } from 'luxon';



@Component({
    selector: 'app-status-update-document',
    templateUrl: 'status-update-component.html',
    styleUrls: [
        'status-update-component.scss'
    ]
})

export class DocumentStatusUpdateComponent extends ViewComponent implements OnInit, OnDestroy {

    @Input() id: number;
    @Input() currentstatus: number;
    @Input() nextstatus: boolean = false;
    @Input() processed: (result: boolean) => void;

    private documentServiceProxy: DocumentServiceProxy;
    private item: DocumentStatusDto;
    private formBuilder: FormBuilder;
    private subscription: Subscription;

    disabled: boolean = false;
    loaded: boolean = false;
    isOnPreview: boolean = false;

    modalForm!: FormGroup;

    get documentStatusReason(): AbstractControl {
        return this.modalForm.controls['documentStatusReasonInput'];
    };
    
    get documentStatusDate(): AbstractControl {
        return this.modalForm.controls['documentStatusDateInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.documentServiceProxy = _injector.get(DocumentServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            documentStatusDateInput: [{value :DateTime.fromJSDate(new Date()).toFormat('dd/MM/yyyy HH:mm'), disabled: true}, Validators.compose([Validators.required])],
            documentStatusReasonInput: ['']
        });
    }

    ngOnInit() {
        
    }
    
    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    async save(): Promise<void> {
        this.item = new DocumentStatusDto();
        this.item.documentid = this.id;
        this.item.statusid = !this.nextstatus ? Status.Rejected : (this.currentstatus == Status.Rejected ? Status.WorkingOn : this.currentstatus) + 1;
        this.item.comment = this.documentStatusReason.value

        if (isNullEmptyOrWhiteSpace(this.item.comment) && !this.nextstatus) {
            this.message.info('El motivo es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.documentServiceProxy
                .updateStatus(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: async (response) => {
                        await this.dialog.dismiss(true);
                        if(response.IsSuccess){
                            this.notify.success('Documento actualizado exitosamente', 5000);
                            this.processed(true);
                        }
                        else{
                            this.notify.error("Ocurrio un error. Contacte al administrador: " + response.Message, 5000);
                        }
                    }
                });
    }
}