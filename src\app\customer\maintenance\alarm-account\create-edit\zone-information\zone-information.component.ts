import { Component, Injector, Input, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AlarmAccountDto } from '@proxies/alarm-account.proxy';
import { AlarmZoneDto, AlarmZoneServiceProxy } from '@proxies/alarm-zone.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { AlarmZoneCreateEditComponent } from './create-edit/create-edit.component';

@Component({
    selector: 'app-alarm-zone-dashboard',
    templateUrl: 'zone-information.component.html',
    styleUrls: [
        'zone-information.component.scss'
    ]
})
export class AlarmZoneInformationComponent extends ViewComponent {

    private alarmZoneServiceProxy: AlarmZoneServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    @Input() alarmAccount: AlarmAccountDto;

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmZoneServiceProxy = _injector.get(AlarmZoneServiceProxy);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: AlarmZoneCreateEditComponent,
            componentProps: {
                alarmAccount: this.alarmAccount
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: AlarmZoneDto) {
        this.dialog.showWithData<boolean>({
            component: AlarmZoneCreateEditComponent,
            componentProps: {
                alarmAccount: this.alarmAccount,
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: AlarmZoneDto) {
        this.message.confirm(`¿Estas seguro de eliminar la zona de alarma "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.alarmZoneServiceProxy
                    .delete(this.alarmAccount.id, item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado la zona de alarma satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.alarmZoneServiceProxy
            .getAll(
                this.alarmAccount.id,
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['partition']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: AlarmZoneDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.AlarmZone.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.AlarmZone.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}