import { Component, Injector, Input, OnInit, NgModule, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { UploadResource } from '@core/utils/core.request';
import { finalize } from 'rxjs';
import { IUploadProgressRespose } from '@core/models/app-config';
import { AppAuditoryService } from '@core/services/auditory/auditory.service';
import { AppAuditoryFindEmployeeComponent } from '@components/auditory/find-employee/find-employee.component';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { AppDocumentPreviewComponent } from '@components/file-preview-pdf/file-preview-pdf.component';
import { IntegrationCenterLogisticDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { AuditoryDto, AuditoryPartnerDto, AuditoryServiceProxy } from '@proxies/auditory/manager/audit.proxy';
import { AuditoryPolicyDto } from '@proxies/auditory/manager/audit-policy.proxy';
import { EmployeeDto } from '@proxies/employee.proxy';
import { MacroProcessDto, ProcessDto, SubProcessDto } from '@proxies/auditory/document.proxy';
import { AuditoryResultAttachmentDto, AuditoryResultDto, AuditoryResultPartnerDto, AuditoryResultRequerimentDto, AuditoryResultServiceProxy, AuditoryResultStatus, AuditoryResultTypeDto, AuditoryResultVerifierDto } from '@proxies/auditory/manager/audit-result.proxy';
import { AuditoryRequerimentDto } from '@proxies/auditory/manager/audit-requeriment.proxy';

@Component({
    selector: 'app-create-edit-result-audit',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class AuditoryResultCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;
    @Input() audit!: AuditoryDto;
    @Input() onlyview: boolean = false;

    private auditResultServiceProxy: AuditoryResultServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private formBuilder: FormBuilder;

    _date: Date;
    size: number = 15_728_640;
    uploadResources: UploadResource[] = [];
    item: AuditoryResultDto = new AuditoryResultDto();
    disabled: boolean = false;

    centers: IntegrationCenterLogisticDto[] = [];
    types: AuditoryResultTypeDto[] = [];
    policies: AuditoryPolicyDto[] = [];
    process: ProcessDto[] = [];
    subprocess: SubProcessDto[] = [];
    macroprocesses: MacroProcessDto[] = [];
    requeriments: AuditoryRequerimentDto[] = [];
    loaded: boolean = false;

    modalForm!: FormGroup;

    set dates(value: Date) {
        this._date = value;
    }

    get auditoryResultName(): AbstractControl {
        return this.modalForm.controls['auditoryResultNameInput'];
    };

    get auditoryCode(): AbstractControl {
        return this.modalForm.controls['auditoryCodeInput'];
    };

    get auditoryDocument(): AbstractControl {
        return this.modalForm.controls['auditoryDocumentInput'];
    };

    get auditoryCaption(): AbstractControl {
        return this.modalForm.controls['auditoryCaptionInput'];
    };

    get auditoryResultCreatedAt(): AbstractControl {
        return this.modalForm.controls['auditoryResultCreatedAtInput'];
    };

    get auditoryResultWatcher(): AbstractControl {
        return this.modalForm.controls['auditoryResultWatcherInput'];
    };

    get auditoryResultPartners(): AbstractControl {
        return this.modalForm.controls['auditoryResultPartnersInput'];
    };

    get auditoryResultStatus(): AbstractControl {
        return this.modalForm.controls['auditoryResultStatusInput'];
    };

    get auditoryResultTrainingVerifier(): AbstractControl {
        return this.modalForm.controls['auditoryResultTrainingVerifierInput'];
    };

    get auditoryResultAction(): AbstractControl {
        return this.modalForm.controls['auditoryResultActionSelect'];
    };

    get auditoryResultVerifier(): AbstractControl {
        return this.modalForm.controls['auditoryResultVerifierInput'];
    };

    get auditoryResultType(): AbstractControl {
        return this.modalForm.controls['auditoryResultTypeSelect'];
    };

    get auditoryProcess(): AbstractControl {
        return this.modalForm.controls['auditoryProcessSelect'];
    };

    get auditorySubProcess(): AbstractControl {
        return this.modalForm.controls['auditorySubProcessSelect'];
    };

    get auditoryMacroProcess(): AbstractControl {
        return this.modalForm.controls['auditoryMacroProcessSelect'];
    };

    get auditoryResultLogisticCenter(): AbstractControl {
        return this.modalForm.controls['auditoryResultLogisticCenterSelect'];
    };

    get auditoryResultPolicy(): AbstractControl {
        return this.modalForm.controls['auditoryResultPolicySelect'];
    };

    get auditoryRequerimentPolicy(): AbstractControl {
        return this.modalForm.controls['auditoryRequerimentPolicySelect'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.auditResultServiceProxy = _injector.get(AuditoryResultServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            auditoryResultNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(100)])],
            auditoryResultCreatedAtInput: [{ value: (new Date()).toLocaleDateString(), disabled: true }],
            auditoryResultVerifierInput: [''],
            auditoryResultWatcherInput: [''],
            auditoryResultPartnersInput: [''],
            auditoryResultTrainingVerifierInput: [''],
            auditoryResultActionSelect: ['-1', Validators.compose([Validators.required])],
            auditoryProcessSelect: ['-1', Validators.compose([Validators.required])],
            auditorySubProcessSelect: ['-1', Validators.compose([Validators.required])],
            auditoryMacroProcessSelect: ['-1', Validators.compose([Validators.required])],
            auditoryResultStatusInput: [{ value: 'Pendiente', disabled: true }, Validators.compose([Validators.required])],
            auditoryResultTypeSelect: ['-1', Validators.compose([Validators.required])],
            auditoryResultLogisticCenterSelect: ['-1', Validators.compose([Validators.required])],
            auditoryResultPolicySelect: ['-1', Validators.compose([Validators.required])],
            auditoryRequerimentPolicySelect: ['-1', Validators.compose([Validators.required])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (!this.id) {
            this.auditoryResultPolicy.setValue(this.audit?.policyId);
            this.auditoryResultPolicy.disable();

            this.disabled = false;
            await loading.dismiss();
            return;
        }

        if (this.onlyview) {
            this.modalForm.disable();
        }
        this.auditResultServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    this.item = response;
                    this.item.attachments?.forEach(x => x.path =
                        this.auditResultServiceProxy.getResource(x.filePath, x.fileName, 1)
                    );
                    const documentsubprocess = this.subprocess.find(x => x.id == this.item.subProcessId);
                    const documentprocess = this.process.find(x => x.id == documentsubprocess?.processid?.toString());
                    this.auditoryMacroProcess.setValue(documentprocess?.macroprocessid || '-1');
                    this.auditoryProcess.setValue(documentprocess?.id || '-1');
                    this.auditorySubProcess.setValue(this.item.subProcessId || '-1');
                    this._date = this.item.realDate;
                    this.auditoryResultName.setValue(this.item.description?.toUpperCase() || '');
                    this.auditoryResultCreatedAt.setValue(this.item.createdAt?.toLocaleString());
                    this.auditoryResultPartners.setValue(this.getPartners());
                    this.auditoryResultType.setValue(this.item.typeId || '-1');
                    this.auditoryResultLogisticCenter.setValue(this.item.logisticCenters || '-1');
                    this.auditoryResultVerifier.setValue(this.getVerifiers());
                    this.auditoryResultTrainingVerifier.setValue(this.item.trainingVerifier);
                    this.auditoryResultWatcher.setValue(this.item.watcher);
                    this.auditoryResultPolicy.setValue(this.item.policyId);
                    this.auditoryResultAction.setValue(this.item.action ? "1" : "0")
                    this.auditoryRequerimentPolicy.setValue(this.item.requeriments?.map(x => x.auditoryRequerimentId))

                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {
        this.item.auditoryId = this.audit.id;
        this.item.description = this.auditoryResultName.value.toUpperCase();
        this.item.realDate = this._date;
        this.item.subProcessId = this.auditorySubProcess.value;
        this.item.logisticCenters = this.auditoryResultLogisticCenter.value;
        this.item.typeId = this.auditoryResultType.value;
        this.item.action = this.auditoryResultAction.value == "0" ? false : true;
        this.item.policyId = this.auditoryResultPolicy.value;
        this.item.partners = this.auditoryResultPartners.value?.split().map(x => new AuditoryResultPartnerDto().fromJS({ Email: x }));
        this.item.verifiers = this.auditoryResultVerifier.value?.split().map(x => new AuditoryResultVerifierDto().fromJS({ Email: x }));
        this.item.trainingVerifier = this.auditoryResultTrainingVerifier.value;
        this.item.watcher = this.auditoryResultWatcher.value;
        this.item.statusId = AuditoryResultStatus.Pending;
        this.item.attachments = this.item.attachments ?? [];
        this.item.requeriments = Array.isArray(this.auditoryRequerimentPolicy.value) ? 
            this.auditoryRequerimentPolicy.value.map(x => new AuditoryResultRequerimentDto().fromJS({ AuditoryRequerimentId: x })): undefined;

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        const action = this.id ? 'update' : 'create';
        const message = this.id ? 'Documento actualizado exitosamente' : 'Documento creado exitosamente';
        const callback = () => {
            this.uploadResources.forEach(x => {
                const info = new AuditoryResultAttachmentDto().fromJS({
                    FileName: x.file.name,
                    FilePath: x.token,
                    Extension: x.file.name.split('.').pop(),
                    Type: x.type == 'document' ? 'file' : x.type,
                    Icon: x.icon
                });
                this.item.attachments.push(info)
            })
            this.auditResultServiceProxy[action](this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                }))
                .subscribe(() => {
                    this.notify.success(message, 5000);
                    this.dialog.dismiss(true);
                });
        };

        this.uploadResources.length ? this.uploads(callback) : callback();
    }

    private async loadInputs(): Promise<void> {
        await Promise.all([
            this.getCenters(),
            this.getInputs()
        ]);
    }

    private getCenters(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.integrationServiceProxy
                .getAllLogiticCenters()
                .subscribe({
                    next: (response) => {
                        this.centers = response.items;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private getInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.auditResultServiceProxy
                .getFilters()
                .subscribe({
                    next: (response) => {
                        this.policies = response.policies;
                        this.subprocess = response.subprocess;
                        this.macroprocesses = response.macroprocess;
                        this.process = response.process;
                        this.types = response.types;
                        this.requeriments = response.requeriments;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    onUploadItem(event: UploadResource): void {
        this.uploadResources ??= [];
        this.uploadResources.push(event);
    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.uploadResources ??= [];
                this.uploadResources.splice(index, 1);
            }
        });
    }

    onRemoveResource(index: number): void {
        this.item.attachments.splice(index, 1);
    }

    showFindEmployeePartner(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto[]>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                selecteds: this.item.partners?.map(x => x.email) ?? []
            }
        }).then((response) => {
            if (Array.isArray(response.data.result)) {
                response.data.result.forEach(e => {
                    this.item.partners = this.item.partners ?? [];
                    const email = e.employee?.email;
                    if (email && !this.item.partners.some(p => p.email === email)) {
                        this.item.partners.push(new AuditoryResultPartnerDto().fromJS({ Email: email }));
                    }
                });
            }
            this.auditoryResultPartners.setValue(this.getPartners());
        });
    }

    showFindEmployeeVerifier(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto[]>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                selecteds: this.item.verifiers?.map(x => x.email) ?? []
            }
        }).then((response) => {
            if (Array.isArray(response.data.result)) {
                response.data.result.forEach(e => {
                    this.item.verifiers = this.item.verifiers ?? [];
                    const email = e.employee?.email;
                    if (email && !this.item.verifiers.some(p => p.email === email)) {
                        this.item.verifiers.push(new AuditoryResultVerifierDto().fromJS({ Email: email }));
                    }
                });
            }
            this.auditoryResultVerifier.setValue(this.getVerifiers());
        });
    }

    showFindEmployeeTrainingVerifier(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto[]>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                hideSelectAll: true
            }
        }).then((response) => {
            if (Array.isArray(response.data.result)) {
                response.data.result.forEach(e => {
                    this.auditoryResultTrainingVerifier.setValue(e.employee?.email);
                });
            }
        });
    }

    showFindEmployeeWatcher(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto[]>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                hideSelectAll: true
            }
        }).then((response) => {
            if (Array.isArray(response.data.result)) {
                response.data.result.forEach(e => {
                    this.auditoryResultWatcher.setValue(e.employee?.email);
                });
            }
        });
    }

    private uploads(callback: () => void) {
        this.dialog.show({
            component: AppFileUploadProgressComponent,
            componentProps: {
                treatment: "none",
                source: 1,
                files: this.uploadResources.map(p => p.file),
                processed: (data: IUploadProgressRespose) => {
                    if (data.completed) {
                        let index: number = 0;

                        for (let token of data.tokens) {
                            this.uploadResources[index].token = token;
                            index++;
                        }

                        callback();
                    } else {
                        this.disabled = false;
                    }
                }
            }
        });
    }

    get processSelect() {
        const selectedValue = this.auditoryMacroProcess?.value;
        return this.process.filter(x => x.macroprocessid == selectedValue);
    }

    get subProcessSelect() {
        const selectedValue = this.auditoryProcess?.value;
        return this.subprocess.filter(x => x.processid == selectedValue);
    }

    get requerimentsSelect() {
        const selectedValue = this.auditoryResultPolicy?.value;
        return this.requeriments.filter(x => x.policyId == selectedValue);
    }

    getPartners(): string {
        return this.item?.partners?.map(x => x.email).join()
    }

    getVerifiers(): string {
        return this.item?.verifiers?.map(x => x.email).join()
    }
}