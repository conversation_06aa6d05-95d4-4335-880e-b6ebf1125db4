<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Modelos de Autorización
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button *ngIf="('Pages.Auditory.Models.Modify' | permission)" (click)="createItem()" class="ion-option"
                color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar Modelo
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 80px; max-width: 80px; width: 80px" [hidden]="
                                !(
                                    [
                                        'Pages.Auditory.Models.Modify',
                                        'Pages.Auditory.Models.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th style="min-width: 20px">
                                    ID
                                </th>
                                <th style="min-width: 100px">
                                    Nombre
                                </th>
                                <th style="min-width: 100px">
                                    Proceso
                                </th>
                                <th style="min-width: 100px">
                                    Elaborador
                                </th>
                                <th style="min-width: 100px">
                                    Revisor
                                </th>
                                <th style="min-width: 100px">
                                    Aprobador
                                </th>
                                <th style="min-width: 100px">
                                    Responsable
                                </th>
                                <th style="min-width: 100px">
                                    Control SIG
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 80px; max-width: 80px; width: 80px" [hidden]="
                                !(
                                    [
                                        'Pages.Auditory.Models.Modify',
                                        'Pages.Auditory.Models.Delete'
                                    ] | permissionAny
                                )
                                ">
                                </th>
                                <th style="min-width: 20px; width: 20px; max-width: 20px;">
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="AreaPositionName"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="AreaPositionProcessId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="processArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 80px; max-width: 80px; width: 80px"
                                    [hidden]="
                                !(
                                    [
                                        'Pages.Auditory.Models.Modify',
                                        'Pages.Auditory.Models.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        ID
                                    </span> {{record?.id}}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Name
                                    </span> {{record?.name}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Area
                                    </span> {{ record?.process?.name }}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Creador
                                    </span> {{record?.tiercreate}}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Revisor
                                    </span> {{record?.tierreview}}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Aprobador
                                    </span> {{record?.tierapprove}}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Responsable
                                    </span> {{record?.owner}}
                                </td>
                                <td style="min-width: 100px">
                                    <span class="p-column-title">
                                        Control SIG
                                    </span> {{record?.tiercontrol}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>