// Cards de estadísticas
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        ion-icon {
            font-size: 24px;
            color: white;
        }

        &.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.attendance {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        &.video {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        }

        &.evaluation {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
        }
    }

    .stats-content {
        h3 {
            font-size: 28px;
            font-weight: bold;
            margin: 0;
            color: #2c3e50;
        }

        p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }
    }
}

// Card de promedio de calificaciones
.average-score-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #f8d7da;

    .trophy-icon {
        font-size: 32px;
        color: #856404;
        margin-right: 15px;
    }

    h4 {
        color: #856404;
        font-weight: bold;
        margin: 0;
    }

    .average-score {
        font-size: 24px;
        font-weight: bold;
        padding: 8px 16px;
        border-radius: 20px;
        
        &.score-excellent {
            background: #d4edda;
            color: #155724;
        }
        
        &.score-good {
            background: #fff3cd;
            color: #856404;
        }
        
        &.score-poor {
            background: #f8d7da;
            color: #721c24;
        }
    }
}

// Badges
.badge {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;

    &.badge-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    &.badge-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    &.badge-secondary {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }

    &.badge-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
}

// Score badges
.score-badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: bold;

    &.score-excellent {
        background: #d4edda;
        color: #155724;
    }

    &.score-good {
        background: #fff3cd;
        color: #856404;
    }

    &.score-poor {
        background: #f8d7da;
        color: #721c24;
    }

    &.score-none {
        background: #e2e3e5;
        color: #6c757d;
    }
}

// Tabla
.table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    thead {
        background: #f8f9fa;
        
        th {
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 15px 12px;
        }
    }

    tbody {
        tr {
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #f8f9fa;
            }

            td {
                padding: 12px;
                vertical-align: middle;
                border-top: 1px solid #dee2e6;
            }
        }
    }
}

// Estado vacío
.empty-state {
    padding: 40px 20px;

    .empty-icon {
        font-size: 64px;
        color: #6c757d;
        margin-bottom: 20px;
    }

    h4 {
        color: #495057;
        margin-bottom: 10px;
    }

    p {
        color: #6c757d;
        margin: 0;
    }
}

// Responsive
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 15px;
        
        .stats-content h3 {
            font-size: 24px;
        }
    }

    .table-responsive {
        font-size: 14px;
    }

    .average-score-card {
        text-align: center;
        
        .d-flex {
            flex-direction: column;
            
            .trophy-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
            
            .ms-auto {
                margin-left: 0 !important;
                margin-top: 10px;
            }
        }
    }
}
