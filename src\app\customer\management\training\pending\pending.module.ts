import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { ComponentModule } from '@shared/components/component.module';

import { TrainingPendingRoutingModule } from './pending.routing.module';
import { TrainingPendingDashboardComponent } from './dashboard/dashboard.component';
import { TrainingPendingViewComponent } from './view-training/view-training.component';
import { TrainingVideoPlayerComponent } from './video-player/video-player.component';
import { VideoTrackerDirective } from './directives/video-tracker.directive';

@NgModule({
  imports: [
    TrainingPendingRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule,
    ComponentModule
  ],
  declarations: [
    TrainingPendingDashboardComponent,
    TrainingPendingViewComponent,
    TrainingVideoPlayerComponent,
    VideoTrackerDirective
  ]
})
export class TrainingPendingModule { }
