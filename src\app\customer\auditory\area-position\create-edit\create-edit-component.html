<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar Modelo' : 'Crear Modelo'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AreaPositionName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (areaPositionName.invalid && (areaPositionName.touched || areaPositionName.dirty))}">
                                        Nombre (*)
                                    </label>
                                    <input type="text" class="form-control" id="AreaPositionName" name="AreaPositionName"
                                        formControlName="areaPositionNameInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre del modelo es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="PositionProcess" class="form-label"
                                        [ngClass]="{'ng-invalid' : (areaPositionProcess.invalid && (areaPositionProcess.touched || areaPositionProcess.dirty))}">
                                        Proceso(*)
                                    </label>
                                    <select class="form-control" id="PositionProcess" name="PositionProcess"
                                        formControlName="areaPositionProcessSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let proc of process" [value]="proc.id">
                                            {{proc.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El proceso es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AreaPositionTierCreate">
                                        Creador
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindPosition('create')" type="text" class="form-control rounded"
                                            id="AreaPositionTierCreate" name="AreaPositionTierCreate" formControlName="areaPositionTierCreateInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AreaPositionTierReview">
                                        Revisor
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindPosition('review')" type="text" class="form-control rounded"
                                            id="AreaPositionTierReview" name="AreaPositionTierReview" formControlName="areaPositionTierReviewInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AreaPositionTierApprove">
                                        Aprobador
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindPosition('approve')" type="text" class="form-control rounded"
                                            id="AreaPositionTierApprove" name="AreaPositionTierApprove" formControlName="areaPositionTierApproveInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AreaPositionTierControl">
                                        Control SIG
                                    </label>
                                    <div class="input-group action">
                                        <input type="text" class="form-control rounded"
                                            id="AreaPositionTierControl" name="AreaPositionTierControl" formControlName="areaPositionTierControlInput"
                                            readonly />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AreaPositionOwner">
                                        Responsable
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployee()" type="text" class="form-control rounded"
                                            id="AreaPositionOwner" name="AreaPositionOwner" formControlName="areaPositionOwnerInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>