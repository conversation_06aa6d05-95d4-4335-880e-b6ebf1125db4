import { Component, EventEmitter, Injector, Input, Output, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { OperationTaskProgramDetailDto, OperationTaskProgramDto, OperationTaskProgramPersonDto, OperationTaskProgramServiceProxy, OperationTaskProgramValidatePersonDto } from '@proxies/operation-task-program.proxy';
import { OperationTaskProgramCreateEditPersonComponent } from './create-edit-person/create-edit-person.component';
import { DocumentTypeServiceProxy } from '@proxies/document-type.proxy';
import { IFilterOption } from '@core/models/filters';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { DateTime } from 'luxon';
import { OperationTaskProgramService } from '../../operation-task.service';
import { finalize, Subscription } from 'rxjs';
import { PersonAreaServiceProxy } from '@proxies/person-area.proxy';
import { AppFindPersonComponent } from '@components/find-person/find-person.component';
import { PersonDto } from '@proxies/person.proxy';
import { EntityDto } from '@core/utils/core.request';

@Component({
    selector: 'app-operation-task-program-person-information',
    templateUrl: 'person-information.component.html',
    styleUrls: [
        'person-information.component.scss'
    ]
})
export class OperationTaskProgramPersonInformationComponent extends ViewComponent {

    private _operationTime!: Date;

    private readonly documentTypeServiceProxy: DocumentTypeServiceProxy;
    private readonly personAreaServiceProxy: PersonAreaServiceProxy;
    private readonly operationTaskProgramService: OperationTaskProgramService;
    private readonly operationTaskProgramServiceProxy: OperationTaskProgramServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable!: Table;
    @ViewChild('paginator', { static: true }) paginator!: Paginator;

    @Input() editable!: boolean;
    @Input() item!: OperationTaskProgramDto;

    @Input() get operationTime(): Date {
        return this._operationTime;
    }

    set operationTime(value: Date) {
        this._operationTime = value;
        this.operationTimeChange.emit(value);
    }

    @Output() operationTimeChange: EventEmitter<Date> = new EventEmitter<Date>();

    documentTypeArray: IFilterOption<number>[];
    personAreaArray: IFilterOption<number>[];

    private subscription: Subscription;
    private skipCount: number;
    private maxResultCount: number;

    constructor(injector: Injector) {
        super(injector);

        this.documentTypeServiceProxy = injector.get(DocumentTypeServiceProxy);
        this.personAreaServiceProxy = injector.get(PersonAreaServiceProxy);
        this.operationTaskProgramService = injector.get(OperationTaskProgramService);
        this.operationTaskProgramServiceProxy = injector.get(OperationTaskProgramServiceProxy);

        this.skipCount = 0;
        this.table.defaultRecordsCountPerPage = 5;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;
    }

    ngOnInit(): void {

        this.subscription = this.operationTaskProgramService.onPagination.subscribe({
            next: () => this.formatPagination(this.skipCount, this.maxResultCount)
        });

        this.documentTypeServiceProxy.getAll({ maxResultCount: 1000, skipCount: 0 }).subscribe({
            next: (response) => {
                this.documentTypeArray = response.items.map(p => {
                    return {
                        label: p.name,
                        value: p.id
                    };
                });
            }
        });

        this.personAreaServiceProxy.getAll({ maxResultCount: 1000, skipCount: 0 }).subscribe({
            next: (response) => {
                this.personAreaArray = response.items.map(p => {
                    return {
                        label: p.name,
                        value: p.id
                    };
                });
            }
        });

        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    createItem(): void {
        this.dialog.showWithData<PersonDto[]>({
            component: AppFindPersonComponent,
            componentProps: {
                selectionMode: 'multiple',
                selecteds: this.item.operationTaskProgramDetails
                    .filter(p => p.person !== undefined && p.person !== null)
                    .map(p => new PersonDto().fromJS(p.person))
            }
        }).then(async (response) => {
            if (response.data.result) {
                let persons: PersonDto[] = response.data.result;

                let currentTime: DateTime = DateTime.now();

                let details: OperationTaskProgramDetailDto[] = [];

                for (let person of persons) {

                    if (this.item.operationTaskProgramDetails.findIndex(p => !p.remove && p.person?.id == person.id) === -1) {

                        details.push(new OperationTaskProgramDetailDto({
                            person: new OperationTaskProgramPersonDto().fromJS(person),
                            startTime: DateTime.fromObject({
                                year: currentTime.year,
                                month: currentTime.month,
                                day: currentTime.day,
                                hour: 8,
                                minute: 0,
                                second: 0,
                                millisecond: 0
                            }),
                            endTime: DateTime.fromObject({
                                year: currentTime.year,
                                month: currentTime.month,
                                day: currentTime.day,
                                hour: 18,
                                minute: 0,
                                second: 0,
                                millisecond: 0
                            })
                        }));
                    }
                }

                details = await this.verificatePersons(details);

                for (let detail of details) {
                    this.item.operationTaskProgramDetails.push(detail);
                }

                this.operationTaskProgramService.onPagination.next();
            }
        });
    }

    editItem(item: OperationTaskProgramDetailDto, index: number): void {

        let itemClone: OperationTaskProgramDetailDto = new OperationTaskProgramDetailDto().fromJS(item);

        itemClone.startTime = item.startTime?.isValid ? DateTime.fromJSDate(item.startTime.toJSDate()) : null;
        itemClone.endTime = item.endTime?.isValid ? DateTime.fromJSDate(item.endTime.toJSDate()) : null;

        this.dialog.showWithData<OperationTaskProgramDetailDto>({
            component: OperationTaskProgramCreateEditPersonComponent,
            componentProps: {
                operationTaskProgram: this.item,
                detail: itemClone,
                index: index,
                editable: true
            }
        }).then((response) => {
            if (response.data.result) {
                this.item.operationTaskProgramDetails[index] = response.data.result;
                this.operationTaskProgramService.onPagination.next();
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    resetFilters(): void {
        this.dataTable?.clear();
        this.getData();
    }

    deleteItem(item: OperationTaskProgramDetailDto, index: number) {
        this.message.confirm(`¿Estas seguro de eliminar la persona "${(item.person.name || '').trim()}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {

                if (item.id) {
                    item.remove = true;
                } else {
                    this.item.operationTaskProgramDetails.splice(index, 1);
                }

                this.formatPagination(this.skipCount, this.maxResultCount);
            }
        });
    }

    showActions(event: any, item: OperationTaskProgramDetailDto, index: number) {
        this.popover.show(event, [
            {
                label: 'Editar',
                callback: () => this.editItem(item, index)
            },
            {
                label: 'Eliminar',
                callback: () => this.deleteItem(item, index)
            }
        ]);
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        const documentType: IFilterOption<number>[] = this.dataTable?.filters?.['documentType']?.['value'];
        const isNullDocumentType: boolean = documentType === null || documentType === undefined || documentType.length == 0;
        const personArea: IFilterOption<number>[] = this.dataTable?.filters?.['personArea']?.['value'];
        const isNullPersonArea: boolean = personArea === null || personArea === undefined || personArea.length == 0;
        const document: string = this.dataTable?.filters?.['document']?.['value'];
        const isNullDocument: boolean = isNullEmptyOrWhiteSpace(document);
        const name: string = this.dataTable?.filters?.['name']?.['value'];
        const isNullName: boolean = isNullEmptyOrWhiteSpace(name);
        const emailAddress: string = this.dataTable?.filters?.['emailAddress']?.['value'];
        const isNullEmailAddress: boolean = isNullEmptyOrWhiteSpace(emailAddress);
        const company: string = this.dataTable?.filters?.['company']?.['value'];
        const isNullCompany: boolean = isNullEmptyOrWhiteSpace(company);
        const area: string = this.dataTable?.filters?.['area']?.['value'];
        const isNullArea: boolean = isNullEmptyOrWhiteSpace(area);
        const job: string = this.dataTable?.filters?.['job']?.['value'];
        const isNullJob: boolean = isNullEmptyOrWhiteSpace(job);

        let values = this.item.operationTaskProgramDetails.filter((p) => (
            (isNullDocumentType || documentType.findIndex(d => d.value == p?.person?.documentType?.id) !== -1) &&
            (isNullPersonArea || personArea.findIndex(d => d.value == p?.person?.personArea?.id) !== -1) &&
            (isNullDocument || (p?.person?.document || '').trim().toUpperCase().indexOf((document || '').toUpperCase().trim()) != -1) &&
            (isNullName || (p?.person?.name || '').toUpperCase().trim().indexOf((name || '').toUpperCase().trim()) != -1) &&
            (isNullEmailAddress || (p?.person?.emailAddress || '').toUpperCase().trim().indexOf((emailAddress || '').toUpperCase().trim()) != -1) &&
            (isNullCompany || (p?.person?.company || '').toUpperCase().trim().indexOf((company || '').toUpperCase().trim()) != -1) &&
            (isNullArea || (p?.person?.area || '').toUpperCase().trim().indexOf((area || '').toUpperCase().trim()) != -1) &&
            (isNullJob || (p?.person?.job || '').toUpperCase().trim().indexOf((job || '').toUpperCase().trim()) != -1)
        ));

        this.table.records = [];

        for (let item of values) {
            item.isHidden = true;

            if (!item.remove) {

                if (index >= skipCount && result < maxResultCount) {
                    this.table.records.push(item);
                    result++;
                }

                index++;
            }
        }

        this.table.totalRecordsCount = index;
    }

    private verificatePersons(input: OperationTaskProgramDetailDto[]): Promise<OperationTaskProgramDetailDto[]> {
        return new Promise<OperationTaskProgramDetailDto[]>(async (resolve, reject) => {

            let data: OperationTaskProgramValidatePersonDto = new OperationTaskProgramValidatePersonDto();

            data.operationTaskProgram = new EntityDto<number>(this.item.id || 0);
            data.operationTime = DateTime.fromJSDate(this.operationTime);
            data.persons = [];

            for (let item of input) 
                data.persons.push(new EntityDto<number>(item.person.id));            

            if (data.operationTime.isValid) {

                const loading = await this.loader.show();

                this.operationTaskProgramServiceProxy
                    .validatePersons(data)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: (response) => {
                            if (response.totalCount === 0) {
                                resolve(input);
                            } else {
                                let message: string = `Las siguientes personas ya se encuentran asignadas en un tareo para la fecha "${data.operationTime.toFormat('dd/MM/yyyy')}": \n\n`;

                                let newData: OperationTaskProgramDetailDto[] = [];

                                for (let detail of input) {
                                    if (response.items.findIndex(p => p.id == detail.person.id) === -1) {
                                        newData.push(detail);
                                    } else {
                                        message += `  -  ${detail.person.name || ''}\n`;
                                    }
                                }

                                this.message.info(message);
                                resolve(newData);
                            }
                        }
                    });
            } else {
                resolve(input);
            }
        });
    }
}