<div class="row">
    <div class="col-12">
        <p-table #dataTable sortMode="multiple" [value]="table?.records" [rows]="table.defaultRecordsCountPerPage"
            [paginator]="false" [lazy]="true" [scrollable]="true" ScrollWidth="100%" scrollDirection="horizontal"
            [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width: 200px; max-width: 200px; width: 200px">
                        Tipo de registro
                    </th>
                    <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                        Llamada de servicio
                    </th>
                    <th style="min-width: 200px; max-width: 200px; width: 200px">
                        Fecha de envío
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr>
                    <td style="min-width: 200px; max-width: 200px; width: 200px">
                        {{record.type}}
                    </td>
                    <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                        <ion-badge *ngIf="record.hasCall" color="success">
                            Si
                        </ion-badge>
                        <ion-badge *ngIf="!record.hasCall" color="danger">
                            No
                        </ion-badge>
                    </td>
                    <td style="min-width: 200px; max-width: 200px; width: 200px">
                        {{record.executionTime | luxonFormat: 'dd/MM/yyyy HH:mm:ss'}}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
    </div>
</div>