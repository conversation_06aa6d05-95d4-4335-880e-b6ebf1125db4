import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { PolicyCreateEditComponent } from '../create-edit/create-edit-component';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { IFilterOption } from '@core/models/filters';
import { IntegrationServiceProxy } from '@proxies/integration.proxy';
import { AuditoryPolicyDto, AuditoryPolicyServiceProxy } from '@proxies/auditory/manager/audit-policy.proxy';

@Component({
    selector: 'app-policy-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class PolicyDashboardComponent extends ViewComponent implements OnInit {

    private policyServiceProxy: AuditoryPolicyServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    auditoryTypeArray: IFilterOption<number>[];
    logisticCenterArray: IFilterOption<number>[];
    policyArray: IFilterOption<string>[];
    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.policyServiceProxy = _injector.get(AuditoryPolicyServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
    }

    ngOnInit() {
        
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: PolicyCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: AuditoryPolicyDto): void {
        this.dialog.showWithData<boolean>({
            component: PolicyCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    previewItem(role: AuditoryPolicyDto) {
        this.dialog.showWithData<boolean>({
            component: PolicyCreateEditComponent,
            componentProps: {
                id: role.id,
                onlyview: true
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(document: AuditoryPolicyDto) {
        this.message.confirm(`¿Estas seguro de eliminar la norma "${document.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.policyServiceProxy
                    .delete(document.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado la auditoría satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.policyServiceProxy
            .getAll(
                this.dataTable?.filters?.['Name']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    console.log(result);
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: AuditoryPolicyDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                hide: false,
                callback: () => this.editItem(role)
            },
            {
                label: 'Visualizar',
                hide: false,
                callback: () => this.previewItem(role)
            },
            {
                label: 'Eliminar',
                hide: false,
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    // private getFiltersAsync(): Promise<void> {
    //     return new Promise((resolve, reject) => {
    //         this.policyServiceProxy.getFilters().subscribe({
    //             next: (response) => {
    //                 this.policyArray = this.parseFilter(response.policies);
    //                 this.auditoryTypeArray = this.parseFilter(response.types);
    //                 resolve()
    //             },
    //             error: (err) => reject(err)
    //         });
    //     });
    // }

    private async loadFilters(): Promise<void> {
        await Promise.all([
            //this.getFiltersAsync()
        ]);
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }
    
    private parseFilterCode(data: any[]): any {
        return data.map(p => {
            return {
                label: p.code,
                value: p.id ?? p.code
            };
        });
    }
}
