<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar Requisito' : '<PERSON>rea<PERSON>'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="RequerimentName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (requerimentName.invalid && (requerimentName.touched || requerimentName.dirty))}">
                                        Nombre (*)
                                    </label>
                                    <input type="text" class="form-control" id="RequerimentName" name="RequerimentName"
                                        formControlName="requerimentNameInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>