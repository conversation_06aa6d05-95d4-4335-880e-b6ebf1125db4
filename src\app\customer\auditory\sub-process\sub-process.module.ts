import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SubProcessRoutingModule } from './sub-process.routing.module';
import { SubProcessDashboardComponent } from './dashboard/dashboard.component';
import { SubProcessCreateEditComponent } from './create-edit/create-edit-component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';

@NgModule({
  imports: [
    SubProcessRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    SubProcessDashboardComponent,
    SubProcessCreateEditComponent
  ]
})
export class SubProcessModule { }