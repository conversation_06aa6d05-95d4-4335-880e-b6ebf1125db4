import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AppFindMaintenanceMacroSystemComponent } from '@components/find-macro-system/find-macro-system.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { MaintenanceMacroSystemDto } from '@proxies/maintenance-macro-system.proxy';
import { MaintenanceMicroSystemDto, MaintenanceMicroSystemMaintenanceMacroSystemDto, MaintenanceMicroSystemServiceProxy } from '@proxies/maintenance-micro-system.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-micro-system-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MaintenanceMicroSystemCreateEditComponent extends ViewComponent implements OnInit {

    private maintenanceMicroSystemServiceProxy: MaintenanceMicroSystemServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;
    @Input() maintenanceMacroSystem: MaintenanceMicroSystemMaintenanceMacroSystemDto;

    item: MaintenanceMicroSystemDto = new MaintenanceMicroSystemDto();

    loaded: boolean = false;
    disabled: boolean = false;

    modalForm!: FormGroup;

    get maintenanceMicroSystemName(): AbstractControl {
        return this.modalForm.controls['maintenanceMicroSystemNameInput'];
    };

    get maintenanceMicroSystemCode(): AbstractControl {
        return this.modalForm.controls['maintenanceMicroSystemCodeInput'];
    };

    get maintenanceMicroSystemSerie(): AbstractControl {
        return this.modalForm.controls['maintenanceMicroSystemSerieInput'];
    };

    get maintenanceMacroSystemLabel(): string {
        return this.item?.maintenanceMacroSystem ? `${this.item.maintenanceMacroSystem.name} (${this.item.maintenanceMacroSystem.code})` : 'Presione para buscar un Sistema 1';
    }

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceMicroSystemServiceProxy = _injector.get(MaintenanceMicroSystemServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            maintenanceMicroSystemNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            maintenanceMicroSystemCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
            maintenanceMicroSystemSerieInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])]   
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        try {
            this.item = await this.getMaintenanceMicroSystem(this.id);

            if (this.item.id) {
                this.maintenanceMicroSystemName.setValue(this.item.name);
                this.maintenanceMicroSystemCode.setValue(this.item.code);
                this.maintenanceMicroSystemSerie.setValue(`${this.item?.maintenanceMacroSystem?.code}-${this.item.code}`);
            } else if (this.maintenanceMacroSystem) {
                this.item.maintenanceMacroSystem = this.maintenanceMacroSystem;
            }

            this.loaded = true;
        } catch {
            await this.dialog.dismiss();
        }

        await loading.dismiss()
    }

    showFindMaintenanceMacroSystem(): void {
        this.dialog.showWithData<MaintenanceMacroSystemDto>({
            component: AppFindMaintenanceMacroSystemComponent
        }).then((response) => {
            if (response.data.result) {
                this.item.maintenanceMacroSystem = new MaintenanceMicroSystemMaintenanceMacroSystemDto().fromJS(response.data.result);
            }
        });
    }

    clearMaintenanceMacroSystem(): void {
        this.item.maintenanceMacroSystem = null;
    }

    async save(): Promise<void> {

        this.item.name = this.maintenanceMicroSystemName.value;
        this.item.code = this.maintenanceMicroSystemCode.value;

        if (this.item.maintenanceMacroSystem === null || this.item.maintenanceMacroSystem === undefined) {
            this.message.info('El Sistema 1 es obligatorio es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.maintenanceMicroSystemServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.maintenanceMicroSystemServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private getMaintenanceMicroSystem(id: number): Promise<MaintenanceMicroSystemDto> {
        return new Promise<MaintenanceMicroSystemDto>((resolve, reject) => {
            if (id) {
                this.maintenanceMicroSystemServiceProxy
                    .get(this.id)
                    .subscribe({
                        next: (response) => resolve(response),
                        error: () => reject()
                    });
            } else {
                resolve(new MaintenanceMicroSystemDto().fromJS({}));
            }
        });
    }
}