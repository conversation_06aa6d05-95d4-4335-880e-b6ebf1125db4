import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { PersonAreaDto, PersonAreaServiceProxy } from '@proxies/person-area.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { PersonAreaCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-person-area-dashboard.component',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class PersonAreaDashboardComponent extends ViewComponent {

    private personAreaServiceProxy: PersonAreaServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    staticStates: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.personAreaServiceProxy = _injector.get(PersonAreaServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: PersonAreaCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: PersonAreaDto) {
        this.dialog.showWithData<boolean>({
            component: PersonAreaCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: PersonAreaDto) {
        this.message.confirm(`¿Estas seguro de eliminar el tipo de trabajo "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.personAreaServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el tipo de trabajo satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.personAreaServiceProxy
            .getAll({
                name: this.dataTable?.filters?.['name']?.['value'],
                hasMultipleOperationTaskPerDay: this.dataTable?.filters?.['name']?.['hasMultipleOperationTaskPerDay'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator, event),
                skipCount: this.table.getSkipCount(this.paginator, event)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: PersonAreaDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.PersonArea.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.PersonArea.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.personAreaServiceProxy
            .export({
                name: this.dataTable?.filters?.['name']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator),
                skipCount: this.table.getSkipCount(this.paginator)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}