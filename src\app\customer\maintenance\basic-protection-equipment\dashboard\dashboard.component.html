<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Equipos de protección personal básicos
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button (click)="export()" class="ion-option me-2" color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>
            
            <ion-button *ngIf="'Pages.Maintenance.BasicProtectionEquipment.Modify' | permission" (click)="createItem()" class="ion-option" color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar equipo de protección personal básico
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Maintenance.BasicProtectionEquipment.Modify',
                                        'Pages.Maintenance.BasicProtectionEquipment.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th style="min-width: 200px" pSortableColumn="Name">
                                    Nombre
                                    <p-sortIcon field="Name"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px" pSortableColumn="Enabled">
                                    ¿Habilitado?
                                    <p-sortIcon field="Enabled"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px" pSortableColumn="Code">
                                    Código
                                    <p-sortIcon field="Code"></p-sortIcon>
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Maintenance.BasicProtectionEquipment.Modify',
                                        'Pages.Maintenance.BasicProtectionEquipment.Delete'
                                    ] | permissionAny
                                )
                                ">
                                </th>
                                <th style="min-width: 200px">
                                    <p-columnFilter type="text" field="name"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter field="enabled" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="staticStates"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter type="text" field="code"></p-columnFilter>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Maintenance.BasicProtectionEquipment.Modify',
                                        'Pages.Maintenance.BasicProtectionEquipment.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 200px">
                                    {{record.name}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ion-badge color="tertiary" *ngIf="record.enabled">
                                        Si
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record.enabled">
                                        No
                                    </ion-badge>
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.code}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>