<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar motivo de tratamiento de alarma' : 'Crear motivo de tratamiento de  alarma'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12 mb-2">
                    <div class="ion-input">
                        <div class="input-control">

                            <label class="form-label fz-normal d-block">
                                Información general
                            </label>

                            <hr>

                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmTreatmentReasonName" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmTreatmentReasonName.invalid && (alarmTreatmentReasonName.touched || alarmTreatmentReasonName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmTreatmentReasonName" name="AlarmTreatmentReasonName"
                                formControlName="alarmTreatmentReasonNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="AlarmTreatmentReasonEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="AlarmTreatmentReasonEnabled" name="AlarmTreatmentReasonEnabled"
                                formControlName="alarmTreatmentReasonEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="AlarmTreatmentReasonType">
                                ¿Mostrar cuando? (*)
                            </label>
                            <select class="form-control" id="AlarmTreatmentReasonType" name="AlarmTreatmentReasonType"
                                formControlName="alarmTreatmentReasonTypeSelect">
                                <option [value]="alarmTreatmentReasonTypes.all">
                                    Siempre
                                </option>
                                <option [value]="alarmTreatmentReasonTypes.true">
                                    Cuando sea real
                                </option>
                                <option [value]="alarmTreatmentReasonTypes.false">
                                    Cuando no sea real
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmTreatmentReasonCode" class="form-label">
                                Código 
                            </label>

                            <input type="text" class="form-control" id="AlarmTreatmentReasonCode" name="AlarmTreatmentReasonCode"
                                formControlName="alarmTreatmentReasonCodeInput" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-12 mb-2">
                    <div class="ion-input">
                        <div class="input-control">

                            <label class="form-label fz-normal d-block">
                                Mostrar en los tipos de alarma
                            </label>

                            <hr>

                        </div>
                    </div>
                </div>

                <div *ngFor="let alarmType of item.alarmTypes; index as i;" class="col-12">
                    <ion-item class="ion-no-padding" button>
                        <ion-checkbox (ionChange)="onAlarmTypeChange($event, i)" [checked]="alarmType.selected" mode="ios">
                            {{alarmType.name}}
                        </ion-checkbox>
                    </ion-item>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>