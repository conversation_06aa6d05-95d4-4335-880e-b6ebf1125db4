<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar tipo de documento' : 'Crear tipo de documento'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentTypeName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentTypeName.invalid && (documentTypeName.touched || documentTypeName.dirty))}">
                                        Nombre (*)
                                    </label>
                                    <input type="text" class="form-control" id="DocumentTypeName" name="DocumentTypeName"
                                        formControlName="documentTypeNameInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre del tipo es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentTypeCode" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentTypeCode.invalid && (documentTypeCode.touched || documentTypeCode.dirty))}">
                                        Código (*)
                                    </label>
                                    <input type="text" class="form-control" id="DocumentTypeCode" name="DocumentTypeCode"
                                        formControlName="documentTypeCodeInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El código del tipo es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="ion-input mb-4">
                                    <div class="input-control">
                                        <label for="DocumentTypeFormat" class="form-label"
                                            [ngClass]="{'ng-invalid' : (documentTypeFormat.invalid && (documentTypeFormat.touched || documentTypeFormat.dirty))}">
                                            Formato
                                        </label>
                                        <select class="form-control" style="z-index: 1;" id="DocumentTypeFormat"
                                            name="DocumentTypeFormat"
                                            formControlName="documentTypeFormatSelect">
                                            <option value="-1">
                                                Seleccione
                                            </option>
                                            <option *ngFor="let format of formats"
                                                [value]="format.code">
                                                {{format.name}}
                                            </option>
                                        </select>

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Seleccione un formato
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>