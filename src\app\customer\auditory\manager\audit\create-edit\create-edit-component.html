<form autocomplete="off" class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? (onlyview ? 'Visualizar Auditoría' : 'Editar Auditoría') : 'Crear Auditoría'"
        [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryCode" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryCode.invalid && (auditoryCode.touched || auditoryCode.dirty))}">
                                        N° de Auditoría
                                    </label>

                                    <input type="text" class="form-control" id="AuditoryCode" name="AuditoryCode"
                                        formControlName="auditoryCodeInput">
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryCreatedAt" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryCreatedAt.invalid && (auditoryCreatedAt.touched || auditoryCreatedAt.dirty))}">
                                        Fecha de Creación
                                    </label>

                                    <input type="text" class="form-control" id="AuditoryCreatedAt"
                                        name="AuditoryCreatedAt" formControlName="auditoryCreatedAtInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryType"
                                        [ngClass]="{'ng-invalid' : (auditoryType.invalid && (auditoryType.touched || auditoryType.dirty))}">
                                        Tipo de Auditoría (*)
                                    </label>

                                    <select class="form-control" id="AuditoryType" name="AuditoryType"
                                        formControlName="auditoryTypeSelect">
                                        <option [value]="0"> Interna </option>
                                        <option [value]="1"> Externa </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El tipo de auditoría es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryLogisticCenter">
                                        Centro logístico (*)
                                    </label>
                                    <select class="form-control" id="AuditoryLogisticCenter"
                                        name="AuditoryLogisticCenter" formControlName="auditoryLogisticCenterSelect">
                                        <option value="-1">
                                            Todos
                                        </option>
                                        <option *ngFor="let center of centers" [value]="center.code">
                                            {{center.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryName.invalid && (auditoryName.touched || auditoryName.dirty))}">
                                        Nombre (*)
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control"
                                        id="AuditoryName" name="AuditoryName" formControlName="auditoryNameInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre de la auditoría es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <app-date-multiple-input [(value)]="_dates" name="AuditoryDates"
                                label="Fechas de Auditoría" />
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryPolicy"
                                        [ngClass]="{'ng-invalid' : (auditoryPolicy.invalid && (auditoryPolicy.touched || auditoryPolicy.dirty))}">
                                        Norma (*)
                                    </label>

                                    <select class="form-control" id="AuditoryPolicy" name="AuditoryPolicy"
                                        formControlName="auditoryPolicySelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let policy of policies" [value]="policy.id">
                                            {{policy.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            La norma de la auditoría es obligatoria.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryOwner">
                                        Responsable
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployee()" type="text" class="form-control rounded"
                                            id="AuditoryOwner" name="AuditoryOwner" formControlName="auditoryOwnerInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryPartners">
                                        Equipo Auditor
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployeeAuditory()" type="text"
                                            class="form-control rounded" id="AuditoryPartners" name="AuditoryPartners"
                                            formControlName="auditoryPartnersInput" readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-12">
                            <ion-item lines="none"  class="goal-item">
                                <ion-label class="form-label">
                                    Objetivos
                                </ion-label>
                                <ion-buttons slot="end">
                                    <ion-button size="default" shape="round" (click)="addGoal()"
                                        aria-label="Agregar objetivo">
                                        <ion-icon name="add" slot="icon-only"></ion-icon>
                                    </ion-button>
                                </ion-buttons>
                            </ion-item>

                            <ion-list formArrayName="auditoryGoalsList">
                                <ion-item *ngFor="let group of auditoryGoals.controls; let i = index"
                                    [formGroupName]="i" class="goal-input-item">
                                    <ion-input formControlName="name" placeholder="Objetivo" labelPlacement="floating"
                                        class="ion-text-capitalize goal-input">
                                    </ion-input>
                                    <ion-button slot="end" fill="clear" color="danger" size="default" shape="round"
                                        (click)="removeGoal(i)" aria-label="Eliminar meta">
                                        <ion-icon name="close" slot="icon-only"></ion-icon>
                                    </ion-button>
                                </ion-item>
                            </ion-list>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryScope" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryScope.invalid && (auditoryScope.touched || auditoryScope.dirty))}">
                                        Alcance (*)
                                    </label>

                                    <textarea style="text-transform: uppercase;" class="form-control" id="AuditoryScope"
                                        name="AuditoryScope" formControlName="auditoryScopeInput">
                                    </textarea>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El alcance de la auditoría es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryCriteria" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryCriteria.invalid && (auditoryCriteria.touched || auditoryCriteria.dirty))}">
                                        Criterios de Auditoría (*)
                                    </label>

                                    <textarea style="text-transform: uppercase;" class="form-control"
                                        id="AuditoryCriteria" name="AuditoryCriteria"
                                        formControlName="auditoryCriteriaInput">
                                    </textarea>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El criterio de la auditoría es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryDocument" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryDocument.invalid && (auditoryDocument.touched || auditoryDocument.dirty))}">
                                        Documento Auditor (*)
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control"
                                        id="AuditoryDocument" name="AuditoryDocument"
                                        formControlName="auditoryDocumentInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El documento de la auditoría es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" *ngIf="id">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryCaption" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryCaption.invalid && (auditoryCaption.touched || auditoryCaption.dirty))}">
                                        Leyenda
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control"
                                        id="AuditoryCaption" name="AuditoryCaption"
                                        formControlName="auditoryCaptionInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="col-12">
                                <ion-item lines="none"  class="goal-item">
                                    <ion-label class="form-label">
                                        Conclusiones
                                    </ion-label>
                                    <ion-buttons slot="end">
                                        <ion-button size="default" shape="round" (click)="addFinding()"
                                            aria-label="Agregar conclusion">
                                            <ion-icon name="add" slot="icon-only"></ion-icon>
                                        </ion-button>
                                    </ion-buttons>
                                </ion-item>
    
                                <ion-list formArrayName="auditoryFindingsList">
                                    <ion-item *ngFor="let group of auditoryFindings.controls; let i = index"
                                        [formGroupName]="i" class="goal-input-item">
                                        <ion-input formControlName="name" placeholder="Conclusion" labelPlacement="floating"
                                            class="ion-text-capitalize goal-input">
                                        </ion-input>
                                        <ion-button slot="end" fill="clear" color="danger" size="default" shape="round"
                                            (click)="removeFinding(i)" aria-label="Eliminar conclusion">
                                            <ion-icon name="close" slot="icon-only"></ion-icon>
                                        </ion-button>
                                    </ion-item>
                                </ion-list>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="Documentos Anexos">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <app-resource-preview *ngFor="let resource of item.attachments; index as i;"
                                [resource]="resource.path" [type]="resource.type" [icon]="resource.icon"
                                [name]="resource.fileName" [showRemove]="true" (onRemove)="onRemoveResource(i)">
                            </app-resource-preview>
                        </div>

                        <div class="col-12 mb-3">
                            <app-file-uploader [size]="size" [files]="true" [documents]="true"
                                (onUploadItem)="onUploadItem($event)">
                            </app-file-uploader>
                        </div>

                        <div *ngIf="uploadResources.length > 0" class="col-12 mb-3">
                            <label class="fz-normal fw-bold text-dark mb-0">
                                Recursos pendientes de publicación
                            </label>
                            <hr>
                        </div>

                        <div *ngFor="let resource of uploadResources; index as i;" class="col-12">
                            <app-file-preview [resource]="resource" [type]="resource.type"
                                (onRemove)="onRemoveUploadResource(i)">
                            </app-file-preview>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="Resumen" *ngIf="id">
                    <app-auditory-result-dashboard [auditory]="item" *ngIf="item?.id">
                    </app-auditory-result-dashboard>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [showSaveButton]="!onlyview" [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>