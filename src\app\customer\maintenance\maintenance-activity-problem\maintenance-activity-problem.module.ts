import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceActivityProblemRoutingModule } from './maintenance-activity-problem.routing.module';
import { MaintenanceActivityProblemDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceActivityProblemCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    MaintenanceActivityProblemRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceActivityProblemDashboardComponent,
    MaintenanceActivityProblemCreateEditComponent
  ]
})
export class MaintenanceActivityProblemModule { }
