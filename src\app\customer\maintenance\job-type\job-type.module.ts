import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { JobTypeRoutingModule } from './job-type.routing.module';
import { JobTypeDashboardComponent } from './dashboard/dashboard.component';
import { JobTypeCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    JobTypeRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    JobTypeDashboardComponent,
    JobTypeCreateEditComponent
  ]
})
export class JobTypeModule { }
