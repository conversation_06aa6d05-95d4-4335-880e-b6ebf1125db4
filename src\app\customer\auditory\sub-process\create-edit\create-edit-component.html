<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar subproceso' : 'Crear subproceso'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="SubProcessName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (subProcessName.invalid && (subProcessName.touched || subProcessName.dirty))}">
                                        Nombre (*)
                                    </label>
                                    <input type="text" class="form-control" id="SubProcessName" name="SubProcessName"
                                        formControlName="subProcessNameInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="SubProcessCode" class="form-label"
                                        [ngClass]="{'ng-invalid' : (subProcessCode.invalid && (subProcessCode.touched || subProcessCode.dirty))}">
                                        Código (*)
                                    </label>
                                    <input type="text" class="form-control" id="SubProcessCode" name="SubProcessCode"
                                        formControlName="subProcessCodeInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El código es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="ion-input mb-4">
                                    <div class="input-control">
                                        <label for="ProcessProcess" class="form-label"
                                            [ngClass]="{'ng-invalid' : (subProcessProcess.invalid && (subProcessProcess.touched || subProcessProcess.dirty))}">
                                            Proceso
                                        </label>
                                        <select class="form-control" style="z-index: 1;" id="ProcessProcess"
                                            name="ProcessProcess"
                                            formControlName="subProcessProcessSelect">
                                            <option value="-1">
                                                Seleccione
                                            </option>
                                            <option *ngFor="let proc of process"
                                                [value]="proc.id">
                                                {{proc.name}}
                                            </option>
                                        </select>

                                        <ion-row class="input-validation">
                                            <ion-col size="12">
                                                Seleccione un proceso
                                            </ion-col>
                                        </ion-row>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label">
                                        Elaborador
                                        <a style="color:deepskyblue" [routerLink]="" (click)="clearCreator()">Limpiar</a>
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindPosition()" type="text" class="form-control rounded"
                                            id="SubProcessCreator" name="SubProcessCreator" formControlName="subProcessCreatorInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>