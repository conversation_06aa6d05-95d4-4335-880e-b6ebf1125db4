<form [formGroup]="modalForm" class="row">

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de la charla
        </label>
        <hr>
    </div>

    <div class="col-6 mb-4">
        <div class="form-group">
            <label class="form-label" for="MeetCenter">
                Centro logístico (*)
            </label>
            <select id="MeetCenter" name="MeetCenter" class="form-control" formControlName="meetCenterSelect">
                <option value="-1">
                    Seleccione
                </option>
                <option *ngFor="let center of centers" [value]="center.code">
                    {{center.name}}
                </option>
            </select>
        </div>
    </div>

    <div class="col-6 mb-4">
        <div class="form-group">
            <label class="form-label" for="MeetType">
                Tipo (*)
            </label>
            <select id="MeetType" name="MeetType" class="form-control" formControlName="meetTypeSelect">
                <option *ngFor="let meetType of meetTypeArray" [value]="meetType.value">
                    {{meetType.label}}
                </option>
            </select>
        </div>
    </div>

    <div class="col-6 mb-4">
        <div class="form-group">
            <label class="form-label" for="MeetTurn">
                Turno (*)
            </label>
            <select id="MeetTurn" name="MeetTurn" class="form-control" formControlName="meetTurnSelect">
                <option *ngFor="let meetTurn of meetTurnArray" [value]="meetTurn.value">
                    {{meetTurn.label}}
                </option>
            </select>
        </div>
    </div>

    <div class="col-6 mb-4">
        <div class="form-group">
            <label class="form-label" for="MeetStatus">
                Estado
            </label>
            <select id="MeetStatus" name="MeetStatus" class="form-control" formControlName="meetStatusSelect">
                <option *ngFor="let meetStatus of meetStatusArray" [value]="meetStatus.value">
                    {{meetStatus.label}}
                </option>
            </select>
        </div>
    </div>

    <div class="col-12 mb-4">

        <div class="ion-input">
            <div class="input-control">

                <label for="MeetTitle" class="form-label"
                    [ngClass]="{'ng-invalid' : (meetTitle.invalid && (meetTitle.touched || meetTitle.dirty))}">
                    Tema (*)
                </label>

                <textarea type="text" class="form-control" id="MeetTitle" name="MeetTitle"
                    formControlName="meetTitleInput"></textarea>

                <ion-row class="input-validation">
                    <ion-col size="12">
                        El tema es obligatorio.
                    </ion-col>
                </ion-row>

                <div class="input-length">
                    {{meetTitle?.value?.length || 0}}/255
                </div>

            </div>
        </div>

    </div>

    <div class="col-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label for="MeetCode" class="form-label">
                    Código
                </label>
                <input type="text" class="form-control" id="MeetCode" name="MeetCode" formControlName="meetCodeInput"
                    readonly />
            </div>
        </div>
    </div>

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información del expositor
        </label>
        <hr>
    </div>

    <div *ngIf="!completed" class="col-12 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="MeetLeader">
                    Buscar expositor (*)
                </label>
                <div class="input-group action">
                    <input (click)="showFindUser()" class="form-control rounded" id="MeetLeader" name="MeetLeader"
                        value="Presiona para buscar un usuario" readonly />
                    <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                </div>
            </div>
        </div>
    </div>

    <ng-container *ngIf="meet.leader">

        <div class="col-6 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="MeetLeaderDocumentType">
                        Tipo documento
                    </label>
                    <input class="form-control" id="MeetLeaderDocumentType" name="MeetLeaderDocumentType"
                        value="{{meet?.leader?.documentType?.name}}" readonly />
                </div>
            </div>
        </div>

        <div class="col-6 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="MeetLeaderDocument">
                        Nº Documento
                    </label>
                    <input class="form-control" id="MeetLeaderDocument" name="MeetLeaderDocument"
                        value="{{meet?.leader?.document}}" readonly />
                </div>
            </div>
        </div>

        <div class="col-12 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="MeetLeaderName">
                        Nombres y apellidos
                    </label>
                    <textarea class="form-control" id="MeetLeaderName" name="MeetLeaderName" rows="2"
                        value="{{meet?.leader?.name}} {{meet?.leader?.surname}} {{meet?.leader?.secondSurname}}"
                        readonly></textarea>
                </div>
            </div>
        </div>

    </ng-container>

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de auditoría
        </label>
        <hr>
    </div>

    <div *ngIf="meet.id" class="col-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label for="MeetCreationUser" class="form-label">
                    Creado por
                </label>
                <input type="text" class="form-control" id="MeetCreationUser" name="MeetCreationUser"
                    value="{{meet?.creationUser?.name}} {{meet?.creationUser?.surname}} {{meet?.creationUser?.secondSurname}}"
                    readonly />
            </div>
        </div>
    </div>

    <div *ngIf="meet.id" class="col-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label for="MeetCreationTime" class="form-label">
                    Creado el
                </label>
                <input type="text" class="form-control" id="MeetCreationTime" name="MeetCreationTime"
                    value="{{meet?.creationTime | luxonFormat: 'yyyy/MM/dd HH:mm'}}" readonly />
            </div>
        </div>
    </div>

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de inicio y finalización
        </label>
        <hr>
    </div>

    <div class="col-6 mb-4">
        <app-date-input name="MeetTime" label="Fecha (*)" [(value)]="meetDate" [disabled]="completed" />
    </div>

    <div class="col-12"></div>

    <div class="col-6 mb-4">
        <app-time-input name="MeetTime" label="Hora Inicio (*)" [(value)]="meetStartTime" [disabled]="completed" />
    </div>

    <div class="col-6 mb-4">
        <app-time-input name="MeetTime" label="Hora Fin (*)" [(value)]="meetEndTime" [disabled]="completed" />
    </div>

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Recursos publicados
        </label>
        <hr>
    </div>

    <div *ngIf="meet.meetResources.length == 0">
        <label class="d-block text-center fz-normal text-dark mb-0">
            Sin recursos publicados
        </label>
    </div>

    <div class="col-12 mb-3">
        <app-resource-preview *ngFor="let meetResource of meet.meetResources; index as i;"
            [resource]="meetResource.path" [type]="meetResource.type" [icon]="meetResource.icon"
            [name]="meetResource.name" [info]="meetResource.size" [removed]="meetResource.remove"
            (onRemove)="onRemoveResource(i)" [showRemove]="!completed">
        </app-resource-preview>
    </div>

    <ng-container *ngIf="!completed">

        <div class="col-12 mb-3">
            <label class="fz-normal fw-bold text-dark mb-0">
                Material y cumplimiento
            </label>
            <hr>
        </div>

        <div class="col-12 mb-3">
            <app-file-uploader [size]="size" [images]="true" [files]="true" (onUploadItem)="onUploadItem($event)">
            </app-file-uploader>
        </div>

        <div class="col-12">
            <div class="alert alert-warning" role="alert">
                Ud. puede seleccionar un archivo WORD (doc, docx), PDF (.pdf), con un tamaño máximo de 15MB
            </div>
        </div>

        <div *ngIf="meet?.uploadResources?.length > 0" class="col-12 mb-3">
            <label class="fz-normal fw-bold text-dark mb-0">
                Recursos pendientes de publicación
            </label>
            <hr>
        </div>

        <div *ngFor="let resource of meet.uploadResources; index as i;" class="col-12">
            <app-file-preview [resource]="resource" [type]="resource.type" (onRemove)="onRemoveUploadResource(i)">
            </app-file-preview>
        </div>
    </ng-container>

</form>