import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AppFindMaintenanceMicroSystemComponent } from '@components/find-micro-system/find-micro-system.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { MaintenanceMicroSystemDto } from '@proxies/maintenance-micro-system.proxy';
import { MaintenanceNanoSystemDto, MaintenanceNanoSystemMaintenanceMicroSystemDto, MaintenanceNanoSystemServiceProxy } from '@proxies/maintenance-nano-system.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-nano-system-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MaintenanceNanoSystemCreateEditComponent extends ViewComponent implements OnInit {

    private maintenanceNanoSystemServiceProxy: MaintenanceNanoSystemServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;
    @Input() maintenanceMicroSystem: MaintenanceNanoSystemMaintenanceMicroSystemDto;

    item: MaintenanceNanoSystemDto = new MaintenanceNanoSystemDto();

    loaded: boolean = false;
    disabled: boolean = false;

    modalForm!: FormGroup;

    get maintenanceNanoSystemName(): AbstractControl {
        return this.modalForm.controls['maintenanceNanoSystemNameInput'];
    };

    get maintenanceNanoSystemCode(): AbstractControl {
        return this.modalForm.controls['maintenanceNanoSystemCodeInput'];
    };

    get maintenanceMicroSystemLabel(): string {
        return this.item?.maintenanceMicroSystem ? `${this.item.maintenanceMicroSystem.name} (${this.item.maintenanceMicroSystem.code})` : '';
    }

    get maintenanceMacroSystemLabel(): string {
        return this.item?.maintenanceMicroSystem ? `${this.item.maintenanceMicroSystem?.maintenanceMacroSystem?.name} (${this.item.maintenanceMicroSystem?.maintenanceMacroSystem?.code})` : '';
    }

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceNanoSystemServiceProxy = _injector.get(MaintenanceNanoSystemServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            maintenanceNanoSystemNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            maintenanceNanoSystemCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])]         
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        try {
            this.item = await this.getMaintenanceNanoSystem(this.id);

            if (this.item.id) {
                this.maintenanceNanoSystemName.setValue(this.item.name);
                this.maintenanceNanoSystemCode.setValue(this.item.code);                
            } else if (this.maintenanceMicroSystem) {
                this.item.maintenanceMicroSystem = this.maintenanceMicroSystem;
            }

            this.loaded = true;
        } catch {
            await this.dialog.dismiss();
        }

        await loading.dismiss()
    }

    showFindMaintenanceMicroSystem(): void {
        this.dialog.showWithData<MaintenanceMicroSystemDto>({
            component: AppFindMaintenanceMicroSystemComponent
        }).then((response) => {
            if (response.data.result) {
                this.item.maintenanceMicroSystem = new MaintenanceNanoSystemMaintenanceMicroSystemDto().fromJS(response.data.result);
            }
        });
    }

    clearMaintenanceMicroSystem(): void {
        this.item.maintenanceMicroSystem = null;
    }

    async save(): Promise<void> {

        this.item.name = this.maintenanceNanoSystemName.value;
        this.item.code = this.maintenanceNanoSystemCode.value;

        if (this.item.maintenanceMicroSystem === null || this.item.maintenanceMicroSystem === undefined) {
            this.message.info('El Sistema 1 es obligatorio es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código es obligatorio', 'Aviso');
            return;
        }
        
        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.maintenanceNanoSystemServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.maintenanceNanoSystemServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private getMaintenanceNanoSystem(id: number): Promise<MaintenanceNanoSystemDto> {
        return new Promise<MaintenanceNanoSystemDto>((resolve, reject) => {
            if (id) {
                this.maintenanceNanoSystemServiceProxy
                    .get(this.id)
                    .subscribe({
                        next: (response) => resolve(response),
                        error: () => reject()
                    });
            } else {
                resolve(new MaintenanceNanoSystemDto().fromJS({}));
            }
        });
    }
}