<div class="row">

    <div class="col-12 mb-2">
        <div class="ion-input">
            <div class="input-control">

                <label class="form-label fz-normal d-block">
                    Información de alarma
                </label>

                <hr>

            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmCenter">
                    Centro logístico
                </label>
                <input class="form-control" id="AlarmCenter" name="AlarmCenter"
                    value="{{alarm?.event?.nombreCentroLog}}" readonly />
            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmType">
                    Alarma
                </label>
                <input class="form-control" id="AlarmType" name="AlarmType" value="{{alarm?.event?.alarma}}" readonly />
            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmCreationTime">
                    Fecha y hora
                </label>
                <input class="form-control" id="AlarmCreationTime" name="AlarmCreationTime"
                    value="{{alarm?.event?.messageDateTime | luxonFormat: 'dd/MM/yyyy HH:mm:ss'}}" readonly />
            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmProperty">
                    Almacén
                </label>
                <input class="form-control" id="AlarmProperty" name="AlarmCenter"
                    value="{{alarm?.event?.nombrePropiedad}}" readonly />
            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmCustomer">
                    Cliente
                </label>
                <textarea class="form-control" id="AlarmCustomer" name="AlarmCustomer"
                    value="{{alarm?.event?.rucCliente}} - {{alarm?.event?.nombreCliente}}" rows="2" readonly></textarea>
            </div>
        </div>
    </div>

    <div class="col-12 mb-2">
        <div class="ion-input">
            <div class="input-control">

                <label class="form-label fz-normal d-block">
                    Información de evento
                </label>

                <hr>

            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmName">
                    Evento
                </label>
                <input class="form-control" id="AlarmName" name="AlarmName" value="{{alarm?.alarmType?.name}}"
                    readonly />
            </div>
        </div>
    </div>

    <div *ngIf="alarm.treatment" class="col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmName">
                    Estado
                </label>
                <input class="form-control" id="AlarmName" name="AlarmName"
                    value="{{alarm?.treatment.status == alarmStatuses.pending ? 'Pendiente de atención' : alarm?.treatment.status == alarmStatuses.process ? 'En curso' : alarm?.treatment.status == alarmStatuses.completed ? 'Atendido' : ''}}"
                    readonly />
            </div>
        </div>
    </div>

    <div class="col-12"></div>

    <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmUser">
                    Usuario
                </label>
                <input class="form-control" id="AlarmUser" name="AlarmUser"
                    value="{{alarm?.event?.fUserName || alarm?.event?.fUser}}" readonly>
            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="AlarmZone">
                    Zona
                </label>
                <input class="form-control" id="AlarmZone" name="AlarmZone"
                    value="{{alarm?.event?.fZoneName || alarm?.event?.fZone}}" readonly>
            </div>
        </div>
    </div>

    <ng-container *ngIf="alarm.treatment">

        <div class="col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="AlarmTreatmentIsReal">
                        ¿Es real?
                    </label>
                    <input class="form-control" id="AlarmTreatmentIsReal" name="AlarmTreatmentIsReal"
                        value="{{alarm?.treatment?.status == alarmStatuses.pending ? '' : (alarm?.treatment?.isReal ? 'SI' : 'NO')}}"
                        readonly>
                </div>
            </div>
        </div>

        <div class="col-sm-12 col-md-4 col-lg-4 col-xl-4 col-xxl-4 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="AlarmTreatmentIsReal">
                        Estado real
                    </label>
                    <input class="form-control" id="AlarmTreatmentIsReal" name="AlarmTreatmentIsReal"
                        value="{{alarm?.treatment?.canceled ? 'Finalizado' : 'En curso'}}" readonly>
                </div>
            </div>
        </div>

        <div *ngIf="alarm?.treatment?.alarmTreatmentReason" class="col-sm-12 col-md-8 col-lg-8 col-xl-8 col-xxl-8 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="AlarmTreatmentReason">
                        Razón
                    </label>
                    <input class="form-control" id="AlarmTreatmentReason" name="AlarmTreatmentReason"
                        value="{{alarm?.treatment?.alarmTreatmentReason?.name}}" readonly>
                </div>
            </div>
        </div>

        <div class="col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="AlarmTreatmentDescription">
                        Descripción
                    </label>
                    <textarea class="form-control" id="AlarmTreatmentDescription" name="AlarmTreatmentDescription"
                        value="{{alarm?.treatment?.description}}" rows="2" readonly></textarea>
                </div>
            </div>
        </div>

        <div class="col-12 mb-3">
            <label class="fz-normal fw-bold text-dark mb-0">
                Recursos publicados
            </label>
            <hr>
        </div>

        <div *ngIf="alarm?.treatment?.alarmTreatmentResources?.length == 0">
            <label class="d-block text-center fz-normal text-dark mb-0">
                Sin recursos publicados
            </label>
        </div>

        <div class="col-12 mb-3">
            <app-resource-preview
                *ngFor="let alarmTreatmentResource of alarm?.treatment?.alarmTreatmentResources; index as i;"
                [resource]="alarmTreatmentResource.path" [type]="alarmTreatmentResource.type"
                [icon]="alarmTreatmentResource.icon" [name]="alarmTreatmentResource.name"
                [info]="alarmTreatmentResource.size" [removed]="alarmTreatmentResource.remove" [showRemove]="false">
            </app-resource-preview>
        </div>

    </ng-container>

</div>