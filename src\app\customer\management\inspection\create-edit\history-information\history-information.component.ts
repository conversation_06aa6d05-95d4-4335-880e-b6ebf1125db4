import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { InspectionDto, InspectionOperationType } from '@proxies/inspection.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';

@Component({
    selector: 'app-inspection-history-information',
    templateUrl: 'history-information.component.html',
    styleUrls: [
        'history-information.component.scss'
    ]
})
export class InspectionHistoryInformationComponent extends ViewComponent implements OnInit {

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    @Input() inspection!: InspectionDto;
    @Input() completed!: boolean;
    
    inspectionOperationType = {
        created: InspectionOperationType.Created,
        modified: InspectionOperationType.Modified,
        modifiedWithoutDate: InspectionOperationType.ModifiedWithoutDate,
        pending: InspectionOperationType.Pending,
        process: InspectionOperationType.Process,
        executed: InspectionOperationType.Executed,
        canceled: InspectionOperationType.Canceled,
        rejected: InspectionOperationType.Rejected,
        contract: InspectionOperationType.Contract,
        completed: InspectionOperationType.Completed
    }

    private skipCount: number;
    private maxResultCount: number;

    constructor(_injector: Injector) {
        super(_injector);

        this.skipCount = 0;
        this.table.defaultRecordsCountPerPage = 5;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;
    }

    ngOnInit(): void {
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        for (let item of this.inspection.inspectionOperations) {
            item.isHidden = true;
            if (index >= skipCount && result < maxResultCount) {
                item.isHidden = false;
                result++;
            }

            index++;
        }

        this.table.totalRecordsCount = index;
    }
}