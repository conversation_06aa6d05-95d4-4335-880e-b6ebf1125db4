import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { PersonAreaDto, PersonAreaServiceProxy } from '@proxies/person-area.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-job-type-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class PersonAreaCreateEditComponent extends ViewComponent implements OnInit {

    private personAreaServiceProxy: PersonAreaServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: PersonAreaDto = new PersonAreaDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get personAreaName(): AbstractControl {
        return this.modalForm.controls['personAreaNameInput'];
    };

    get personAreaHasMultipleOperationTaskPerDay(): AbstractControl {
        return this.modalForm.controls['personAreaHasMultipleOperationTaskPerDaySelect'];
    };

    get personAreaCode(): AbstractControl {
        return this.modalForm.controls['personAreaCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.personAreaServiceProxy = _injector.get(PersonAreaServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            personAreaNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            personAreaEnabledSelect: ['true', [Validators.required]],
            personAreaHasMultipleOperationTaskPerDaySelect: ['false', [Validators.required]],
            personAreaCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.personAreaServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.personAreaName.setValue(this.item.name);
                        this.personAreaHasMultipleOperationTaskPerDay.setValue(this.item.hasMultipleOperationTaskPerDay || 'false');
                        this.personAreaCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new PersonAreaDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.personAreaName.value;
        this.item.hasMultipleOperationTaskPerDay = this.personAreaHasMultipleOperationTaskPerDay.value == 'true';
        this.item.code = this.personAreaCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de tipo de trabajo es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.personAreaServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Área actualizada exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.personAreaServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Área creada exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}