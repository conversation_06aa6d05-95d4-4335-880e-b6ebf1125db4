import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceServiceRequestEnsuranceRoutingModule } from './maintenance-service-request-ensurance.routing.module';
import { MaintenanceServiceRequestEnsuranceDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceServiceRequestEnsuranceCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    MaintenanceServiceRequestEnsuranceRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceServiceRequestEnsuranceDashboardComponent,
    MaintenanceServiceRequestEnsuranceCreateEditComponent
  ]
})
export class MaintenanceServiceRequestEnsuranceModule { }
