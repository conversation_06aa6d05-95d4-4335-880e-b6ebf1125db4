import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AuditoryPolicyDto } from '@proxies/auditory/manager/audit-policy.proxy';
import { AuditoryRequerimentDto, AuditoryRequerimentServiceProxy } from '@proxies/auditory/manager/audit-requeriment.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-create-edit-requeriment',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})

export class RequerimentCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;
    @Input() policy!: AuditoryPolicyDto;

    private requerimentServiceProxy: AuditoryRequerimentServiceProxy;
    private formBuilder: FormBuilder;

    item: AuditoryRequerimentDto = new AuditoryRequerimentDto();
    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get requerimentName(): AbstractControl {
        return this.modalForm.controls['requerimentNameInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.requerimentServiceProxy = _injector.get(AuditoryRequerimentServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            requerimentNameInput: ['', Validators.compose([Validators.required])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.requerimentServiceProxy
                .get(this.id)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: (response) => {
                        this.item = response;
                        this.requerimentName.setValue(this.item.name);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        }
        else {
            await loading.dismiss();
            this.disabled = false;
        }
    }

    async save(): Promise<void> {
        this.item.policyId = this.policy.id;
        this.item.name = this.requerimentName.value;

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.requerimentServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Requisito actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.requerimentServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Requisito creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}