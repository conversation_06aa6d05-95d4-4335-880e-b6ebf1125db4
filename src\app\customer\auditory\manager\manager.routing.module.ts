import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'audit',
    data: { permission: 'Pages.Auditory.Manager.Audit' },
    loadChildren: () => import('./audit/audit.module').then(p => p.AuditModule)
  },
  {
    path: 'policy',
    data: { permission: 'Pages.Auditory.Manager.Policy' },
    loadChildren: () => import('./policy/policy.module').then(p => p.PolicyModule)
  },
]

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class ManagerRoutingModule { }
