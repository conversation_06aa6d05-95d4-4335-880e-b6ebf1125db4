import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { MacroProcessServiceProxy, MacroProcessDto } from '@proxies/auditory/macro-process.proxy'
import { AuditoryPolicyDto, AuditoryPolicyServiceProxy } from '@proxies/auditory/manager/audit-policy.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-create-edit-policy',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})

export class PolicyCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;

    private policyServiceProxy: AuditoryPolicyServiceProxy;
    private formBuilder: FormBuilder;

    item: AuditoryPolicyDto = new AuditoryPolicyDto();
    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get policyName(): AbstractControl {
        return this.modalForm.controls['policyNameInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.policyServiceProxy = _injector.get(AuditoryPolicyServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            policyNameInput: ['', Validators.compose([Validators.required])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.policyServiceProxy
                .get(this.id)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: (response) => {
                        this.item = response;
                        this.policyName.setValue(this.item.name);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        }
        else {
            await loading.dismiss();
            this.disabled = false;
        }
    }

    async save(): Promise<void> {
        this.item.name = this.policyName.value;

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.policyServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Norma actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.policyServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Norma creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}