import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SpecificProtectionEquipmentDashboardComponent } from './dashboard/dashboard.component';

const routes: Routes = [
    {
        path: '',
        children: [
            { path: 'dashboard', component: SpecificProtectionEquipmentDashboardComponent },
            { path: '', pathMatch: 'full', redirectTo: 'dashboard' },
            { path: '**', redirectTo: 'dashboard' }
        ]
    }
];

@NgModule({
    imports: [
        RouterModule.forChild(routes)
    ],
    exports: [RouterModule]
})
export class SpecificProtectionEquipmentRoutingModule { }