<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal title="Finalizar charla" [disabled]="disabled" size="normal">
        <app-modal-body>
            <div class="row">

                <div class="col-6 mb-4">
                    <app-date-input name="MeetTime" label="Fecha" [(value)]="meetTime"
                        [disabled]="true" />
                </div>

                <div class="col-6 mb-4"></div>

                <div class="col-6 mb-4">
                    <app-time-input name="MeetStartTime" label="Hora inicio" [(value)]="meetStartTime"
                        [disabled]="true" />
                </div>

                <div class="col-6 mb-4">
                    <app-time-input name="MeetEndTime" label="Hora fin" [(value)]="meetEndTime"
                        [disabled]="true" />
                </div>

                <div class="col-12 mb-4">

                    <div class="ion-input">
                        <div class="input-control">

                            <label for="MeetDescription" class="form-label">
                                Comentarios
                            </label>

                            <textarea type="text" class="form-control" id="MeetDescription"
                                name="MeetDescription" formControlName="meetDescriptionInput"
                                rows="4"></textarea>

                            <div class="input-length">
                                {{meetDescription?.value?.length || 0}}/25000
                            </div>

                        </div>
                    </div>

                </div>

                <div class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Material y cumplimiento
                    </label>
                    <hr>
                </div>
            
                <div class="col-12 mb-3">
                    <app-file-uploader [size]="size" [images]="true" [files]="true" (onUploadItem)="onUploadItem($event)">
                    </app-file-uploader>
                </div>
            
                <div class="col-12">
                    <div class="alert alert-warning" role="alert">
                        Ud. puede seleccionar un archivo WORD (doc, docx), PDF (.pdf), con un tamaño máximo de 15MB
                    </div>
                </div>
            
                <div *ngIf="uploadResources?.length > 0" class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Recursos pendientes de publicación
                    </label>
                    <hr>
                </div>
            
                <div *ngFor="let resource of uploadResources; index as i;" class="col-12">
                    <app-file-preview [resource]="resource" [type]="resource.type" (onRemove)="onRemoveUploadResource(i)">
                    </app-file-preview>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>