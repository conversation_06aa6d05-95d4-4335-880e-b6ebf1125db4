import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AreaPositionRoutingModule } from './area-position.routing.module';
import { AreaPositionDashboardComponent } from './dashboard/dashboard.component';
import { AreaPositionCreateEditComponent } from './create-edit/create-edit-component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';

@NgModule({
  imports: [
    AreaPositionRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    AreaPositionDashboardComponent,
    AreaPositionCreateEditComponent
  ]
})
export class AreaPositionModule { }