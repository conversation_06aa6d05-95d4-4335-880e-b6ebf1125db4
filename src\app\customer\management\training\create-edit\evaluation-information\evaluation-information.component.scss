.card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        padding: 0.75rem 1rem;
        border-radius: 8px 8px 0 0;

        h6 {
            color: #495057;
            font-weight: 600;
        }
    }

    .card-body {
        padding: 1rem;
    }
}

.form-check {
    .form-check-input {
        margin-top: 0.125rem;
    }

    .form-check-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-left: 0.25rem;
    }
}

.input-length {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: right;
    margin-top: 0.25rem;
}

.alert {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0;
    border-radius: 4px;

    &.alert-success {
        background-color: #d1edff;
        border-color: #bee5eb;
        color: #0c5460;

        ion-icon {
            color: #0c5460;
        }
    }

    &.alert-warning {
        background-color: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;

        ion-icon {
            color: #856404;
        }
    }

    &.alert-info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;

        h6 {
            margin-bottom: 0.5rem;
            font-weight: 600;

            ion-icon {
                margin-right: 0.5rem;
            }
        }

        ul {
            margin-left: 1rem;

            li {
                margin-bottom: 0.25rem;
                font-size: 0.875rem;
            }
        }
    }

    small {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
}

.d-flex {
    display: flex;

    &.justify-content-between {
        justify-content: space-between;
    }

    &.justify-content-end {
        justify-content: flex-end;
    }

    &.align-items-center {
        align-items: center;
    }

    &.gap-2 {
        gap: 0.5rem;
    }
}

.flex-grow-1 {
    flex-grow: 1;
}

.me-3 {
    margin-right: 1rem;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

// Responsive adjustments
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
    }

    .col-8, .col-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .d-flex.align-items-center {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;

        .form-check {
            margin-right: 0;
            margin-bottom: 0.5rem;
        }
    }
}

// Custom styling for disabled state
.form-control:disabled,
.form-check-input:disabled {
    background-color: #f8f9fa;
    opacity: 0.8;
}

ion-button[disabled] {
    opacity: 0.6;
    pointer-events: none;
}
