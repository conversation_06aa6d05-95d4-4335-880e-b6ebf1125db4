import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { RoleDto, RoleServiceProxy } from '@proxies/role.proxy';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize } from 'rxjs';
import { RoleCreateEditComponent } from '../create-edit/create-edit-component';
import { AppFileDownloadService } from '@core/services/file-download.service';

@Component({
    selector: 'app-role-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class RoleDashboardComponent extends ViewComponent {

    private roleServiceProxy: RoleServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    staticStates = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    defaultStates = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.roleServiceProxy = _injector.get(RoleServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: RoleCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: RoleDto) {
        this.dialog.showWithData<boolean>({
            component: RoleCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(role: RoleDto) {
        this.message.confirm(`¿Estas seguro de eliminar el rol "${role.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.roleServiceProxy
                    .delete(role.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el rol satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.roleServiceProxy
            .getAll(
                event?.filters?.['name']?.['value'],
                event?.filters?.['isStatic']?.['value'],
                event?.filters?.['isDefault']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: RoleDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Administration.Role.Modify'
                ],
                callback: () => this.editItem(role)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Administration.Role.Delete'
                ],
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.roleServiceProxy
            .export(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['isStatic']?.['value'],
                this.dataTable?.filters?.['isDefault']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}