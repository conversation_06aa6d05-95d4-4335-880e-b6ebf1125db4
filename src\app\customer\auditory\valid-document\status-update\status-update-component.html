<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="'Pasar a Obsoleto'" [disabled]="disabled">
        <app-modal-body>

            <div class="row">
                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label for="DocumentStatusDate" class="form-label"
                                [ngClass]="{'ng-invalid' : (documentStatusDate.invalid && (documentStatusDate.touched || documentStatusDate.dirty))}">
                                Fecha de Registro
                            </label>
    
                            <input type="text" class="form-control" id="DocumentStatusDate" name="DocumentStatusDate"
                                formControlName="documentStatusDateInput">
    
                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    La fecha de registro es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label for="DocumentStatusReason" class="form-label"
                                [ngClass]="{'ng-invalid' : (documentStatusReason.invalid && (documentStatusReason.touched || documentStatusDate.dirty))}">
                                Motivo
                            </label>
    
                            <input type="text" class="form-control" id="DocumentStatusReason" name="DocumentStatusReason"
                                formControlName="documentStatusReasonInput">
    
                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El motivo es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>
            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>