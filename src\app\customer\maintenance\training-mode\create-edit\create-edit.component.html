<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar tipo de capacitación dirigida' : 'Crear tipo de capacitación dirigida'"
        [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="TrainingModeName" class="form-label"
                                [ngClass]="{'ng-invalid' : (trainingModeName.invalid && (trainingModeName.touched || trainingModeName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="TrainingModeName" name="TrainingModeName"
                                formControlName="trainingModeNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="TrainingModeEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="TrainingModeEnabled" name="TrainingModeEnabled"
                                formControlName="trainingModeEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="TrainingModeCapacityRequired">
                                ¿Require aforo? (*)
                            </label>
                            <select class="form-control" id="TrainingModeCapacityRequired"
                                name="TrainingModeCapacityRequired"
                                formControlName="trainingModeCapacityRequiredSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="TrainingModeGenerateCertificate">
                                ¿Genera certificado? (*)
                            </label>
                            <select class="form-control" id="TrainingModeGenerateCertificate"
                                name="TrainingModeGenerateCertificate"
                                formControlName="trainingModeGenerateCertificateSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="TrainingModeCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="TrainingModeCode" name="TrainingModeCode"
                                formControlName="trainingModeCodeInput">
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>