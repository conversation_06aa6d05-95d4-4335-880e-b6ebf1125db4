<form class="row" [formGroup]="modalForm">

    <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">

        <div class="ion-input">
            <div class="input-control">

                <label for="OperationTaskCode" class="form-label">
                    Código interno proyecto (*)
                </label>

                <input type="text" class="form-control" id="OperationTaskCode" name="OperationTaskCode"
                    formControlName="operationTaskCodeInput">

            </div>
        </div>

    </div>

    <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
        <div class="form-group">
            <label class="form-label" for="OperationTaskHasIntegration">
                ¿Tiene proyecto? (*)
            </label>
            <select (change)="onHasIntegrationChange($event)" id="OperationTaskHasIntegration"
                name="OperationTaskHasIntegration" class="form-control"
                formControlName="operationTaskHasIntegrationSelect">
                <option value="true">
                    Si
                </option>
                <option value="false">
                    No
                </option>
            </select>
        </div>
    </div>

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información del proyecto
        </label>
        <hr>
    </div>

    <ng-container *ngIf="operationTaskHasIntegration.value == 'true'; else withoutIntegration">

        <div *ngIf="editable" class="col-12 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="OperationTaskProject">
                        Buscar proyecto (*)
                    </label>
                    <div class="input-group action">
                        <input (click)="showFindProject()" class="form-control rounded" id="OperationTaskProject"
                            name="OperationTaskProject" value="Presiona para buscar un proyecto" readonly />
                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                    </div>
                </div>
            </div>
        </div>

        <ng-container *ngIf="operationTask.projectCode">

            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                <div class="ion-input">
                    <div class="input-control">

                        <label for="OperationTaskProjectCode" class="form-label">
                            Código
                        </label>

                        <input type="text" class="form-control" id="OperationTaskProjectCode"
                            name="OperationTaskProjectCode" value="{{operationTask?.projectCode}}" [disabled]="true">

                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                <div class="ion-input">
                    <div class="input-control">

                        <label for="OperationTaskCenterName" class="form-label">
                            Centro logístico
                        </label>

                        <input type="text" class="form-control" id="OperationTaskCenterName"
                            name="OperationTaskCenterName" value="{{operationTask?.centerName}}" [disabled]="true">

                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
                <div class="ion-input">
                    <div class="input-control">

                        <label for="OperationTaskProjectName" class="form-label">
                            Nombre
                        </label>

                        <textarea type="text" class="form-control" id="OperationTaskProjectName"
                            name="OperationTaskProjectName" value="{{operationTask?.projectName}}" [disabled]="true"
                            rows="2"></textarea>

                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
                <div class="ion-input">
                    <div class="input-control">

                        <label for="OperationTaskCustomer" class="form-label">
                            Cliente
                        </label>

                        <textarea type="text" class="form-control" id="OperationTaskCustomer"
                            name="OperationTaskCustomer" value="{{customer}}" [disabled]="true" rows="2"></textarea>

                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
                <div class="ion-input">
                    <div class="input-control">

                        <label for="OperationTaskLocation" class="form-label">
                            Ubicación
                        </label>

                        <textarea type="text" class="form-control" id="OperationTaskLocation"
                            name="OperationTaskLocation" value="{{operationTask?.locationName}}" [disabled]="true"
                            rows="2"></textarea>

                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                <div class="ion-input">
                    <div class="input-control">

                        <label for="OperationTaskManagementName" class="form-label">
                            Gerencia
                        </label>

                        <input type="text" class="form-control" id="OperationTaskManagementName"
                            name="OperationTaskManagementName" value="{{operationTask?.managementName}}"
                            [disabled]="true">

                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                <div class="ion-input">
                    <div class="input-control">

                        <label for="OperationTaskAreaName" class="form-label">
                            Área
                        </label>

                        <input type="text" class="form-control" id="OperationTaskAreaName" name="OperationTaskAreaName"
                            value="{{operationTask?.areaName}}" [disabled]="true">

                    </div>
                </div>
            </div>

        </ng-container>

    </ng-container>

    <ng-template #withoutIntegration>

        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskProjectName" class="form-label"
                        [ngClass]="{'ng-invalid' : (projectName.invalid && (projectName.touched || projectName.dirty))}">
                        Nombre (*)
                    </label>

                    <textarea type="text" class="form-control" id="OperationTaskProjectName"
                        name="OperationTaskProjectName" formControlName="projectNameInput" rows="2"></textarea>

                    <ion-row class="input-validation">
                        <ion-col size="12">
                            El nombre del proyecto es obligatorio.
                        </ion-col>
                    </ion-row>

                </div>
            </div>
        </div>

        <div *ngIf="editable" class="col-12 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="OperationTaskCustomerSearch">
                        Buscar cliente (*)
                    </label>
                    <div class="input-group action">
                        <input (click)="showFindCustomer()" class="form-control rounded"
                            id="OperationTaskCustomerSearch" name="OperationTaskCustomerSearch"
                            value="Presiona para buscar un cliente" readonly />
                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskCustomer" class="form-label">
                        Cliente (*)
                    </label>

                    <textarea type="text" class="form-control" id="OperationTaskCustomer" name="OperationTaskCustomer"
                        value="{{customer}}" [disabled]="true" rows="2"></textarea>

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskLocation" class="form-label"
                        [ngClass]="{'ng-invalid' : (projectName.invalid && (projectName.touched || projectName.dirty))}">
                        Ubicación (*)
                    </label>

                    <textarea type="text" class="form-control" id="OperationTaskLocation" name="OperationTaskLocation"
                        formControlName="locationInput" rows="2"></textarea>

                    <ion-row class="input-validation">
                        <ion-col size="12">
                            La ubicación del proyecto es obligatoria.
                        </ion-col>
                    </ion-row>

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskCenter" class="form-label">
                        Centro logístico (*)
                    </label>
                    <select class="form-control" id="OperationTaskCenter" name="OperationTaskCenter"
                        formControlName="centerSelect">
                        <option value="-1">
                            Seleccione
                        </option>
                        <option *ngFor="let item of centers" [value]="item.code">
                            {{item.name}}
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskManagement" class="form-label">
                        Gerencia (*)
                    </label>

                    <select class="form-control" id="OperationTaskManagement" name="OperationTaskManagement"
                        formControlName="managementSelect">
                        <option value="-1">
                            Seleccione
                        </option>
                        <option *ngFor="let item of managements" [value]="item.prcCode">
                            {{item.prcName}}
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskArea" class="form-label">
                        Área (*)
                    </label>

                    <select class="form-control" id="OperationTaskArea" name="OperationTaskArea"
                        formControlName="areaSelect">
                        <option value="-1">
                            Seleccione
                        </option>
                        <option *ngFor="let item of areas" [value]="item.prcCode">
                            {{item.prcName}}
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskMovement" class="form-label">
                        Ingreso/Gasto (*)
                    </label>

                    <select class="form-control" id="OperationTaskMovement" name="OperationTaskMovement"
                        formControlName="movementSelect">
                        <option value="-1">
                            Seleccione
                        </option>
                        <option *ngFor="let item of incomeExpenses" [value]="item.prcCode">
                            {{item.prcName}}
                        </option>
                    </select>
                </div>
            </div>
        </div>

    </ng-template>

</form>