<div class="row">

    <div *ngIf="editable" class="col-12 text-end mb-4">
        <ion-button (click)="onAddPerson()" class="ion-option" size="small" color="primary" fill="solid">
            <ion-icon name="add"></ion-icon>
            <ion-label class="fz-small d-none d-lg-inline-block">
                Agregar persona
            </ion-label>
        </ion-button>
    </div>

    <div class="col-12 text-end mb-4">
        <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
            <ion-icon name="refresh"></ion-icon>
        </ion-button>

        <ion-button (click)="getData()" class="ion-option me-2" color="tertiary" fill="solid">
            <ion-icon name="search"></ion-icon>
        </ion-button>
    </div>

    <div class="col-12">
        <p-table #dataTable sortMode="multiple" [value]="table.records" [rows]="table.defaultRecordsCountPerPage"
            [paginator]="false" [lazy]="true" [scrollable]="true" ScrollWidth="100%" scrollDirection="horizontal"
            [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th *ngIf="editable" style="min-width: 130px; max-width: 130px; width: 130px">
                        Acciones
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        T. Documento
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        Nº Documento
                    </th>
                    <th style="min-width: 200px">
                        Nombre
                    </th>
                    <th style="min-width: 250px; max-width: 250px; width: 250px">
                        Correo electrónico
                    </th>
                    <th style="min-width: 250px;">
                        Empresa
                    </th>
                    <th style="min-width: 150px; width: 150px; max-width: 150px;">
                        Área
                    </th>
                    <th style="min-width: 150px; width: 150px; max-width: 150px;">
                        Cargo
                    </th>
                    <th style="min-width: 150px; width: 150px; max-width: 150px;">
                        Área (BSF)
                    </th>
                </tr>
                <tr>
                    <th *ngIf="editable" style="min-width: 130px; max-width: 130px; width: 130px">
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        <p-columnFilter field="documentType" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-multiSelect [ngModel]="value" (onClear)="filter()" (onChange)="filter($event.value)"
                                    [options]="documentTypeArray" [showHeader]="false" [showClear]="true"
                                    placeholder="Todos" optionLabel="label" appendTo="body"></p-multiSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        <p-columnFilter field="document" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Document Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 200px">
                        <p-columnFilter field="name" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Name Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 250px; max-width: 250px; width: 250px">
                        <p-columnFilter field="emailAddress" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="EmailAddress Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 250px;">
                        <p-columnFilter field="company" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Company Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 150px; width: 150px; max-width: 150px;">
                        <p-columnFilter field="area" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Company Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 150px; width: 150px; max-width: 150px;">
                        <p-columnFilter field="job" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Company Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        <p-columnFilter field="personArea" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-multiSelect [ngModel]="value" (onClear)="filter()" (onChange)="filter($event.value)"
                                    [options]="personAreaArray" [showHeader]="false" [showClear]="true"
                                    placeholder="Todos" optionLabel="label" appendTo="body"></p-multiSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr>
                    <td *ngIf="editable" class="action-column"
                        style="min-width: 130px; max-width: 130px; width: 130px">
                        <ion-button (click)="showActions($event, record, rowIndex)" class="ion-action" color="primary"
                            fill="solid" size="small">
                            <ion-label class="fz-small">
                                Acciones
                            </ion-label>
                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                        </ion-button>
                    </td>
                    <td class="action-column" style="min-width: 150px; max-width: 150px; width: 150px">
                        {{record?.person?.documentType?.name}}
                    </td>
                    <td class="action-column" style="min-width: 150px; max-width: 150px; width: 150px">
                        {{record?.person?.document}}
                    </td>
                    <td style="min-width: 200px">
                        {{record?.person?.name}}
                    </td>
                    <td class="action-column" style="min-width: 250px; max-width: 250px; width: 250px">
                        {{record?.person?.emailAddress}}
                    </td>
                    <td class="action-column" style="min-width: 250px;">
                        {{record?.person?.company}}
                    </td>
                    <td style="min-width: 150px; width: 150px; max-width: 150px;">
                        {{record?.person?.area}}
                    </td>
                    <td style="min-width: 150px; width: 150px; max-width: 150px;">
                        {{record?.person?.job}}
                    </td>
                    <td style="min-width: 150px; width: 150px; max-width: 150px;">
                        {{record?.person?.personArea?.name}}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
        <div class="primeng-paging-container">
            <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator (onPageChange)="getData($event)"
                [totalRecords]="table.totalRecordsCount" [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                [showCurrentPageReport]="true" dropdownAppendTo="body"
                currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
            </p-paginator>
        </div>
    </div>

</div>