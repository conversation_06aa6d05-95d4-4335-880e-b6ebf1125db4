import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'audit-logs',
    data: { permission: 'Pages.Administration.AuditLogs' },
    loadChildren: () => import('./audit-log/audit-log.module').then(p => p.AuditLogModule)
  },
  {
    path: 'settings',
    data: { permission: 'Pages.Administration.Settings' },
    loadChildren: () => import('./configuration/configuration.module').then(p => p.ConfigurationModule)
  },
  {
    path: 'maintenance',
    data: { permission: 'Pages.Administration.Maintenance' },
    loadChildren: () => import('./maintenance/maintenance.module').then(p => p.MaintenanceModule)
  },
  {
    path: 'roles',
    data: { permission: 'Pages.Administration.Role' },
    loadChildren: () => import('./role/role.module').then(p => p.RoleModule)
  },
  {
    path: 'templates',
    data: { permission: 'Pages.Administration.Template' },
    loadChildren: () => import('./template/template.module').then(p => p.TemplateModule)
  },
  {
    path: 'users',
    data: { permission: 'Pages.Administration.User' },
    loadChildren: () => import('./user/user.module').then(p => p.UserModule)
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class AdministrationRoutingModule { }
