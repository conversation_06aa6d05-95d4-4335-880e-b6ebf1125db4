import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { AlarmRoutingModule } from './alarm.routing.module';
import { AlarmDashboardComponent } from './dashboard/dashboard.component';
import { ComponentModule } from '@components/component.module';
import { AlarmShowComponent } from './show/show.component';
import { AlarmGeneralInformationComponent } from './show/general-information/general-information.component';
import { AlarmHistoryInformationComponent } from './show/history-information/history-information.component';

@NgModule({
  imports: [
    AlarmRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule,
    ComponentModule
  ],
  declarations: [
    AlarmDashboardComponent,
    AlarmShowComponent,
    AlarmGeneralInformationComponent,
    AlarmHistoryInformationComponent
  ]
})
export class AlarmModule { }
