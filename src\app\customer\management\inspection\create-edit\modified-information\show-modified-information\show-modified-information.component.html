<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal title="Ver detalles de reprogramación">
        <app-modal-body>
            <div class="row">

                <div class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Información reprogramación
                    </label>
                    <hr>
                </div>

                <div class="col-6 mb-4">
                    <app-date-input name="InspectionTime" label="Fecha" [(value)]="inspectionDate" [disabled]="true" />
                </div>

                <div class="col-6 mb-4">
                    <app-time-input name="InspectionTime" label="Hora" [(value)]="inspectionTime" [disabled]="true" />
                </div>

                <div class="col-6 mb-4">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label" for="InspectionModifiedType">
                                Motivo
                            </label>
                            <select [disabled]="true" class="form-control" id="InspectionModifiedType"
                                name="InspectionModifiedType" formControlName="inspectionModifiedTypeSelect">
                                <option [value]="inspectionModes.none">
                                    Seleccione
                                </option>
                                <option [value]="inspectionModes.internal">
                                    Por BSF
                                </option>
                                <option [value]="inspectionModes.external">
                                    A solicitud del cliente
                                </option>
                                <option [value]="inspectionModes.propertyUnavailable">
                                    Bodega cerrada
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-12 mb-4">

                    <div class="ion-input">
                        <div class="input-control">

                            <label for="InspectionModifiedObservation" class="form-label">
                                Comentarios
                            </label>

                            <textarea readonly type="text" class="form-control" id="InspectionModifiedObservation"
                                name="InspectionModifiedObservation"
                                formControlName="inspectionModifiedObservationInput" rows="4"></textarea>

                        </div>
                    </div>

                </div>

                <div class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Recursos publicados
                    </label>
                    <hr>
                </div>

                <div *ngIf="inspectionModify?.inspectionModifiedResources?.length == 0" class="col-12 mb-3">
                    <label class="fz-small d-block text-center text-dark mb-0">
                        Sin recursos publicados
                    </label>
                </div>

                <div class="col-12 mb-3">
                    <app-resource-preview
                        *ngFor="let inspectionModifiedResource of inspectionModify.inspectionModifiedResources; index as i;"
                        [resource]="inspectionModifiedResource.path" [type]="inspectionModifiedResource.type"
                        [icon]="inspectionModifiedResource.icon" [name]="inspectionModifiedResource.name"
                        [info]="inspectionModifiedResource.size" [showRemove]="false">
                    </app-resource-preview>
                </div>              
                
                <div class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Información de publicación
                    </label>
                    <hr>
                </div>

                <div class="col-6 mb-4">

                    <div class="ion-input">
                        <div class="input-control">

                            <label for="InspectionModifiedCreationTime" class="form-label">
                                Programado el
                            </label>

                            <input type="text" class="form-control" id="InspectionModifiedCreationTime"
                                name="InspectionModifiedCreationTime"
                                formControlName="inspectionModifiedCreationTimeInput">

                        </div>
                    </div>

                </div>

                <div class="col-6 mb-4">

                    <div class="ion-input">
                        <div class="input-control">

                            <label for="InspectionModifiedCreationUser" class="form-label">
                                Programado por
                            </label>

                            <input type="text" class="form-control" id="InspectionModifiedCreationUser"
                                name="InspectionModifiedCreationUser"
                                formControlName="inspectionModifiedCreationUserInput">

                        </div>
                    </div>

                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [showSaveButton]="false" />
    </app-modal>
</form>