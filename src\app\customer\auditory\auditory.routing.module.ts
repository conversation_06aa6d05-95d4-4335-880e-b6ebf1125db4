import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'document',
    data: { permission: 'Pages.Auditory.Document' },
    loadChildren: () => import('./document/document.module').then(p => p.DocumentModule)
  },
  {
    path: 'valid-document',
    data: { permission: 'Pages.Auditory.Valid' },
    loadChildren: () => import('./valid-document/valid-document.module').then(p => p.ValidDocumentModule)
  },
  {
    path: 'obsolet',
    data: { permission: 'Pages.Auditory.Obsolete' },
    loadChildren: () => import('./obsolet/obsolet.module').then(p => p.ObsoletModule)
  },
  {
    path: 'document-external',
    data: { permission: 'Pages.Auditory.DocumentExternal' },
    loadChildren: () => import('./document-external/document-external.module').then(p => p.DocumentExternalModule)
  },
  {
    path: 'subprocess',
    data: { permission: 'Pages.Auditory.SubProcess' },
    loadChildren: () => import('./sub-process/sub-process.module').then(p => p.SubProcessModule)
  },
  {
    path: 'process',
    data: { permission: 'Pages.Auditory.Process' },
    loadChildren: () => import('./process/process.module').then(p => p.ProcessModule)
  },
  {
    path: 'macroprocess',
    data: { permission: 'Pages.Auditory.MacroProcess' },
    loadChildren: () => import('./macro-process/macro-process.module').then(p => p.MacroProcessModule)
  },
  {
    path: 'models',
    data: { permission: 'Pages.Auditory.Models' },
    loadChildren: () => import('./area-position/area-position.module').then(p => p.AreaPositionModule)
  },
  {
    path: 'notification',
    data: { permission: 'Pages.Auditory.Notification' },
    loadChildren: () => import('./document-notification/document-notification.module').then(p => p.DocumentNotificationModule)
  },
  {
    path: 'notificationstatus',
    data: { permission: 'Pages.Auditory.Notification' },
    loadChildren: () => import('./document-notification-status/document-notification-status.module').then(p => p.DocumentNotificationStatusModule)
  },
  {
    path: 'documenttype',
    data: { permission: 'Pages.Auditory.DocumentType' },
    loadChildren: () => import('./document-type/document-type.module').then(p => p.DocumentTypeModule)
  },
  {
    path: 'management',
    data: { permission: 'Pages.Auditory.Management' },
    loadChildren: () => import('./area/area.module').then(p => p.AreaModule)
  },
  {
    path: 'manager',
    data: { permission: 'Pages.Auditory.Manager' },
    loadChildren: () => import('./manager/manager.module').then(p => p.ManagerModule)
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class AuditoryRoutingModule { }
