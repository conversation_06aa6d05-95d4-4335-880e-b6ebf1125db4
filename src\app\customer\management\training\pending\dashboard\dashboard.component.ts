import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { TrainingPendingAttendanceDto, TrainingServiceProxy } from '@proxies/training.proxy';
import { Table } from 'primeng/table';
import { Subscription, finalize } from 'rxjs';
import { Paginator } from 'primeng/paginator';
import { TrainingPendingViewComponent } from '../view-training/view-training.component';

@Component({
    selector: 'app-training-pending-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class TrainingPendingDashboardComponent extends ViewComponent implements OnInit {

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    private subscription: Subscription = new Subscription();

    data: TrainingPendingAttendanceDto[] = [];
    totalRecords: number = 0;
    loading: boolean = false;

    constructor(
        _injector: Injector,
        private trainingServiceProxy: TrainingServiceProxy
    ) {
        super(_injector);
    }

    ngOnInit(): void {
        this.getData();
    }

    ngOnDestroy(): void {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    getData(): void {
        this.loading = true;

        // Obtenemos el correo del usuario actual directamente de la sesión
        const emailAddress = this.session.user.emailAddress;

        this.trainingServiceProxy.getPendingAttendance(emailAddress)
            .pipe(finalize(() => this.loading = false))
            .subscribe({
                next: (response) => {
                    this.data = response.items;
                    console.log(this.data);
                    this.totalRecords = response.totalCount;
                },
                error: (error) => {
                    console.error('Error loading pending trainings:', error);
                    this.message.error('Error al cargar los pendientes de capacitación', 'Error');
                }
            });
    }

    override refresh(): void {
        this.getData();
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();
        // Implementar exportación si es necesario
        await loading.dismiss();
        this.notify.success('Funcionalidad de exportación pendiente de implementar', 5000);
    }

    getStatusLabel(status: string): string {
        switch (status) {
            case 'Pending':
                return 'Pendiente';
            case 'Started':
                return 'Iniciado';
            case 'Completed':
                return 'Completado';
            case 'Canceled':
                return 'Cancelado';
            case 'Changed':
                return 'Reprogramado';
            default:
                return status;
        }
    }

    getStatusColor(status: string): string {
        switch (status) {
            case 'Pendiente de Asistencia':
                return 'warning';
            
            case 'Finalizado':
                return 'success';
            case 'Pendiente de Evaluación':
                return 'danger';
             
            default:
                return 'medium';
        }
    }

    formatDate(date: any): string {
        if (!date) return '';
        return date.toFormat('dd/MM/yyyy HH:mm');
    }

    viewItem(item: TrainingPendingAttendanceDto): void {
        console.log('Opening modal with item:', item);
        console.log('hasEvaluation value:', item.hasEvaluation);
        console.log('isEvaluationCompleted value:', item.isEvaluationCompleted);

        this.dialog.showWithData<boolean>({
            component: TrainingPendingViewComponent,
            componentProps: {
                id: item.id,
                subscriptionId: item.subscriptionId,
                hasEvaluation: item.hasEvaluation,
                isEvaluationCompleted: item.isEvaluationCompleted, // Pasar el estado de completado
                activeIndex: 0 ,// Empezar en el primer tab (archivos)
                code: item.code,
                attended: item.attended,
                watchedVideo: item.watchedVideo
            }
        }).then((shouldRefresh) => {
            // Refrescar datos solo si hubo cambios (evaluación enviada, satisfacción guardada, etc.)
            if (shouldRefresh) {
                console.log('Refreshing pending trainings list due to changes');
                this.getData();
            } else {
                console.log('No changes detected, skipping refresh');
            }
        });
    }

    showActions(event: any, item: TrainingPendingAttendanceDto): void {
        this.popover.show(event, [
            {
                label: 'Visualizar',
                permissions: [
                    'Pages.Management.Training.Pending'
                ],
                callback: () => this.viewItem(item)
            }
        ]);
    }
}
