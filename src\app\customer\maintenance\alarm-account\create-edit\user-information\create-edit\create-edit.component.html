<div class="h-100 w-100">
    <app-modal [title]="id ? 'Editar usuario de alarma' : 'Crear usuario de alarma'" [disabled]="disabled">
        <app-modal-body>
            <form class="row" [formGroup]="modalForm">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmUserName" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmUserName.invalid && (alarmUserName.touched || alarmUserName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmUserName" name="AlarmUserName"
                                formControlName="alarmUserNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmUserPartition" class="form-label">
                                Partición
                            </label>

                            <input type="text" class="form-control" id="AlarmUserPartition" name="AlarmUserPartition"
                                formControlName="alarmUserPartitionInput">
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmUserCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmUserCode.invalid && (alarmUserCode.touched || alarmUserCode.dirty))}">
                                Código (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmUserCode" name="AlarmUserCode"
                                formControlName="alarmUserCodeInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El código es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

            </form>

        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</div>