import { Component, Injector, Input, OnInit } from '@angular/core';
import { AppFindPersonComponent } from '@components/find-person/find-person.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { OperationTaskProgramDetailDto, OperationTaskProgramDto, OperationTaskProgramPersonDto } from '@proxies/operation-task-program.proxy';
import { PersonDto } from '@proxies/person.proxy';
import { DateTime } from 'luxon';

@Component({
    selector: 'app-operation-task-program-create-edit-person',
    templateUrl: 'create-edit-person.component.html',
    styleUrls: [
        'create-edit-person.component.scss'
    ]
})
export class OperationTaskProgramCreateEditPersonComponent extends ViewComponent implements OnInit {

    @Input() editable!: boolean;
    @Input() index!: number;
    @Input() operationTaskProgram!: OperationTaskProgramDto;
    @Input() detail!: OperationTaskProgramDetailDto;

    startTime!: Date;
    endTime!: Date;

    constructor(injector: Injector) {
        super(injector);
    }

    ngOnInit(): void {
        this.startTime = this.detail?.startTime?.toJSDate();
        this.endTime = this.detail?.endTime?.toJSDate();
    }

    onAddPerson(): void {
        this.dialog.showWithData<PersonDto>({
            component: AppFindPersonComponent
        }).then(async (response) => {
            if (response.data.result) {
                const person: PersonDto = response.data.result;

                if (this.operationTaskProgram.operationTaskProgramDetails.findIndex(p => !p.remove && p.person?.id === person.id) === -1) {
                    this.detail.person = new OperationTaskProgramPersonDto().fromJS(person);
                } else {
                    this.message.info('La persona ya se agregada encuentra en la lista');
                }
            }
        });
    }

    async save(): Promise<void> {
        if (this.detail.person === undefined || this.detail.person === null) {
            this.message.info('Debe seleccionar una persona antes de continuar');
            return;
        }

        const parsedStartTime: DateTime = DateTime.fromJSDate(this.startTime);
        const parsedEndTime: DateTime = DateTime.fromJSDate(this.endTime);

        if (!parsedStartTime.isValid) {
            this.message.info('La fecha de inicio es obligatoria');
            return;
        }

        if (!parsedEndTime.isValid) {
            this.message.info('La fecha fin es obligatoria');
            return;
        }

        const startTime: DateTime = DateTime.fromObject({
            year: 2000,
            month: 1,
            day: 1,
            hour: parsedStartTime.hour,
            minute: parsedStartTime.minute,
            second: 0,
            millisecond: 0
        });

        const endTime: DateTime = DateTime.fromObject({
            year: 2000,
            month: 1,
            day: 1,
            hour: parsedEndTime.hour,
            minute: parsedEndTime.minute,
            second: 0,
            millisecond: 0
        });

        if (Math.trunc(startTime.diff(endTime, 'minutes').minutes) > 0) {
            this.message.info('La fecha fin no puede ser menor a la fecha de inicio');
            return;
        }

        this.detail.startTime = startTime;
        this.detail.endTime = endTime;

        await this.dialog.dismiss(this.detail);
    }
}