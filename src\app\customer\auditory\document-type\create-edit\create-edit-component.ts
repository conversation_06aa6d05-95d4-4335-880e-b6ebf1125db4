import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AuditoryDocumentTypeServiceProxy, DocumentTypeDto } from '@proxies/auditory/document-type.proxy'
import { finalize } from 'rxjs';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})

export class DocumentTypeCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;

    private auditoryDocumentTypeServiceProxy: AuditoryDocumentTypeServiceProxy;
    private formBuilder: FormBuilder;

    item: DocumentTypeDto = new DocumentTypeDto();
    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get documentTypeCode(): AbstractControl {
        return this.modalForm.controls['documentTypeCodeInput'];
    };

    get documentTypeName(): AbstractControl {
        return this.modalForm.controls['documentTypeNameInput'];
    };

    get documentTypeFormat(): AbstractControl {
        return this.modalForm.controls['documentTypeFormatSelect'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.auditoryDocumentTypeServiceProxy = _injector.get(AuditoryDocumentTypeServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            documentTypeCodeInput: ['', Validators.compose([Validators.required])],
            documentTypeNameInput: ['', Validators.compose([Validators.required])],
            documentTypeFormatSelect: ['-1', Validators.compose([Validators.required])],
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.auditoryDocumentTypeServiceProxy
                .get(this.id)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: (response) => {
                        this.item = response;
                        this.documentTypeName.setValue(this.item.name);
                        this.documentTypeCode.setValue(this.item.code);
                        this.documentTypeFormat.setValue(this.item.format);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        }
        else {
            await loading.dismiss();
            this.disabled = false;
        }
    }

    async save(): Promise<void> {
        this.item.code = this.documentTypeCode.value;
        this.item.name = this.documentTypeName.value;
        this.item.format = this.documentTypeFormat.value;

        if (!this.item.code) {
            this.message.info('El código es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.format) {
            this.message.info('El formato es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.auditoryDocumentTypeServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.auditoryDocumentTypeServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    formats: any[] = [
        { name: "Predeterminado", code: "1"  },
        { name: "Anexo", code: "2"  }
    ]
}