<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar Macroproceso' : 'Crear Macropro<PERSON>'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="MacroProcessName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (macroProcessName.invalid && (macroProcessName.touched || macroProcessName.dirty))}">
                                        Nombre (*)
                                    </label>
                                    <input type="text" class="form-control" id="MacroProcessName" name="MacroProcessName"
                                        formControlName="macroProcessNameInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="MacroProcessCode" class="form-label"
                                        [ngClass]="{'ng-invalid' : (macroProcessCode.invalid && (macroProcessCode.touched || macroProcessCode.dirty))}">
                                        Código (*)
                                    </label>
                                    <input type="text" class="form-control" id="MacroProcessCode" name="MacroProcessCode"
                                        formControlName="macroProcessCodeInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El código es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>