import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { UserMethodDto } from '@proxies/user.proxy';
import { finalize } from 'rxjs';
import { TemplateDto, TemplateServiceProxy } from '@proxies/template.proxy';

const enum Steps {
    Search,
    Typing
}

@Component({
    selector: 'app-create-edit-user',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class TemplateCreateEditComponent extends ViewComponent implements OnInit {

    private templateServiceProxy: TemplateServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: TemplateDto = new TemplateDto();

    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get name(): AbstractControl {
        return this.modalForm.controls['nameInput'];
    };

    get key(): AbstractControl {
        return this.modalForm.controls['keyInput'];
    };

    get value(): AbstractControl {
        return this.modalForm.controls['valueInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.templateServiceProxy = _injector.get(TemplateServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            nameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            keyInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            valueInput: ['', Validators.compose([Validators.required, Validators.maxLength(25_000)])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();

        this.templateServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    this.item = response;

                    this.name.setValue(this.item.name);
                    this.name.disable();
                    this.key.setValue(this.item.id);
                    this.key.disable();
                    this.value.setValue(this.item.value);

                    this.loaded = true;
                },
                error: () => this.dialog.dismiss()
            });
    }


    async save(): Promise<void> {

        this.modalForm.markAllAsTouched();

        let data = new UserMethodDto();

        this.item.name = this.name.value;
        this.item.id = this.key.value;
        this.item.value = this.value.value;

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        this.templateServiceProxy
            .update(this.item)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: () => {
                    this.notify.success('Usuario actualizado exitosamente', 5000);
                    this.dialog.dismiss(true);
                }
            });
    }
}