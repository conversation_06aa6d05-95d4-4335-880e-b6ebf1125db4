import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'lost-item',
    data: { permission: 'Pages.Cargos.LostItem' },
    loadChildren: () => import('./lost-item/lost-item.module').then(p => p.LostItemModule)
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class CargosRoutingModule { }