<div class="h-100 w-100">
    <app-modal [title]="id ? 'Editar cuenta de alarma' : 'Crear cuenta de alarma'" size="large" [disabled]="disabled">
        <app-modal-body>
            <form class="row" [formGroup]="modalForm">

                <div class="col-sm-12 col-md-12 col-lg-8 col-xl-8 col-xxl-8">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmAccountName" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmAccountName.invalid && (alarmAccountName.touched || alarmAccountName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmAccountName" name="AlarmAccountName"
                                formControlName="alarmAccountNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-sm-6 col-md-6 col-lg-4 col-xl-4 col-xxl-4">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="AlarmAccountCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (alarmAccountCode.invalid && (alarmAccountCode.touched || alarmAccountCode.dirty))}">
                                Código (*)
                            </label>

                            <input type="text" class="form-control" id="AlarmAccountCode" name="AlarmAccountCode"
                                formControlName="alarmAccountCodeInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El código es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div *ngIf="loaded" class="col-12">
                    <p-tabView *ngIf="['Pages.Maintenance.AlarmPartition.Modify', 'Pages.Maintenance.AlarmUser.Modify', 'Pages.Maintenance.AlarmZone.Modify'] | permissionAny"
                        [scrollable]="true">
                        <p-tabPanel *ngIf="'Pages.Maintenance.AlarmUser.Modify' | permission" header="Usuarios">
                            <app-alarm-user-dashboard [alarmAccount]="item" />
                        </p-tabPanel>
                        <p-tabPanel *ngIf="'Pages.Maintenance.AlarmPartition.Modify' | permission" header="Particiones">
                            <app-alarm-partition-dashboard [alarmAccount]="item" />
                        </p-tabPanel>
                        <p-tabPanel *ngIf="'Pages.Maintenance.AlarmZone.Modify' | permission"header="Zonas">
                            <app-alarm-zone-dashboard [alarmAccount]="item" />
                        </p-tabPanel>
                    </p-tabView>
                </div>

            </form>

        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</div>