import { mergeMap } from 'rxjs/operators';
import { Injectable, Injector } from "@angular/core";
import { Observable, of } from "rxjs";
import { AppHttpRequestService } from '@core/services/http.request.service';
import { IFromJsonConvertable, IJsonConvertable, IToJsonConvertable, PagedResultDto } from '@core/models/mappings';
import { EntityDto, FileExportDto, UploadResource } from '@core/utils/core.request';
import { DateTime } from 'luxon';
import { bytesToSize, resource, resourceType } from '@core/utils/tools';
import { IntegrationBusinessPartnerDto } from './integration.proxy';
import { PersonDto } from './person.proxy';

@Injectable()
export class TrainingServiceProxy {
    private request: AppHttpRequestService;

    constructor(_injector: Injector) {
        this.request = _injector.get(AppHttpRequestService);
    }

    getAll(code: string | undefined, title: string | undefined, trainingTypeId: number | undefined, trainingModeId: number | undefined, center: string | undefined, location: string | undefined, trainingStartTime: DateTime | undefined, trainingEndTime: DateTime | undefined, status: TrainingStatus | undefined, sorting: string | undefined, maxResultCount: number | undefined, skipCount: number | undefined): Observable<PagedResultDto<TrainingDto>> {
        let url = '/api/services/app/Training/GetAll?';
        if (code !== null && code !== undefined)
            url += "Code=" + encodeURIComponent("" + code) + "&";
        if (title !== null && title !== undefined)
            url += "Title=" + encodeURIComponent("" + title) + "&";
        if (trainingTypeId !== null && trainingTypeId !== undefined)
            url += "TrainingTypeId=" + encodeURIComponent("" + trainingTypeId) + "&";
        if (trainingModeId !== null && trainingModeId !== undefined)
            url += "TrainingModeId=" + encodeURIComponent("" + trainingModeId) + "&";
        if (center !== null && center !== undefined)
            url += "Center=" + encodeURIComponent("" + center) + "&";
        if (location !== null && location !== undefined)
            url += "Location=" + encodeURIComponent("" + location) + "&";
        if (trainingStartTime !== null && trainingStartTime !== undefined && trainingStartTime.isValid)
            url += "TrainingStartTime=" + encodeURIComponent("" + trainingStartTime.toJSON()) + "&";
        if (trainingEndTime !== null && trainingEndTime !== undefined && trainingEndTime.isValid)
            url += "TrainingEndTime=" + encodeURIComponent("" + trainingEndTime.toJSON()) + "&";
        if (status !== null && status !== undefined)
            url += "Status=" + encodeURIComponent("" + status) + "&";
        if (sorting !== null && sorting !== undefined)
            url += "Sorting=" + encodeURIComponent("" + sorting) + "&";
        if (maxResultCount !== null && maxResultCount !== undefined)
            url += "MaxResultCount=" + encodeURIComponent("" + maxResultCount) + "&";
        if (skipCount !== null && skipCount !== undefined)
            url += "SkipCount=" + encodeURIComponent("" + skipCount) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new PagedResultDto<TrainingDto>().fromJS(data, TrainingDto))));
    }

    export(code: string | undefined, title: string | undefined, trainingTypeId: number | undefined, trainingModeId: number | undefined, center: string | undefined, location: string | undefined, trainingStartTime: DateTime | undefined, trainingEndTime: DateTime | undefined, status: TrainingStatus | undefined, sorting: string | undefined, maxResultCount: number | undefined, skipCount: number | undefined): Observable<FileExportDto> {
        let url = '/api/services/app/Training/Export?';
        if (code !== null && code !== undefined)
            url += "Code=" + encodeURIComponent("" + code) + "&";
        if (title !== null && title !== undefined)
            url += "Title=" + encodeURIComponent("" + title) + "&";
        if (trainingTypeId !== null && trainingTypeId !== undefined)
            url += "TrainingTypeId=" + encodeURIComponent("" + trainingTypeId) + "&";
        if (trainingModeId !== null && trainingModeId !== undefined)
            url += "TrainingModeId=" + encodeURIComponent("" + trainingModeId) + "&";
        if (center !== null && center !== undefined)
            url += "Center=" + encodeURIComponent("" + center) + "&";
        if (location !== null && location !== undefined)
            url += "Location=" + encodeURIComponent("" + location) + "&";
        if (trainingStartTime !== null && trainingStartTime !== undefined && trainingStartTime.isValid)
            url += "TrainingStartTime=" + encodeURIComponent("" + trainingStartTime.toJSON()) + "&";
        if (trainingEndTime !== null && trainingEndTime !== undefined && trainingEndTime.isValid)
            url += "TrainingEndTime=" + encodeURIComponent("" + trainingEndTime.toJSON()) + "&";
        if (status !== null && status !== undefined)
            url += "Status=" + encodeURIComponent("" + status) + "&";
        if (sorting !== null && sorting !== undefined)
            url += "Sorting=" + encodeURIComponent("" + sorting) + "&";
        if (maxResultCount !== null && maxResultCount !== undefined)
            url += "MaxResultCount=" + encodeURIComponent("" + maxResultCount) + "&";
        if (skipCount !== null && skipCount !== undefined)
            url += "SkipCount=" + encodeURIComponent("" + skipCount) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new FileExportDto().fromJS(data))));
    }

    exportCertification(trainingId: number | undefined, trainingAssistanceId: number | undefined): Observable<FileExportDto> {
        let url = '/api/services/app/Training/ExportCertification?';
        if (trainingId !== null && trainingId !== undefined)
            url += "TrainingId=" + encodeURIComponent("" + trainingId) + "&";
        if (trainingAssistanceId !== null && trainingAssistanceId !== undefined)
            url += "TrainingAssistanceId=" + encodeURIComponent("" + trainingAssistanceId) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new FileExportDto().fromJS(data))));
    }
    exportCertificationRecord(trainingId: number): Observable<FileExportDto> {
        let url = '/api/services/app/Training/ExportCertificationRecord?';
        if (trainingId !== null && trainingId !== undefined)
            url += "Id=" + encodeURIComponent("" + trainingId) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new FileExportDto().fromJS(data))));
    }
    get(id: number): Observable<TrainingDto> {
        let url = '/api/services/app/Training/Get?';
        if (id !== null && id !== undefined)
            url += "Id=" + encodeURIComponent("" + id) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new TrainingDto().fromJS(data))));
    }

    create(item: TrainingDto): Observable<EntityDto<number>> {
        const url = '/api/services/app/Training/Create';
        const body: string = item.toJSON();

        return this.request.post(url, body).pipe(mergeMap((data: any) => of(new EntityDto<number>().fromJS(data))));
    }

    start(item: EntityDto<number>): Observable<void> {
        const url = '/api/services/app/Training/Start';
        const body: string = item.toJSON();

        return this.request.post(url, body);
    }

    cancel(item: EntityDto<number>): Observable<void> {
        const url = '/api/services/app/Training/Cancel';
        const body: string = item.toJSON();

        return this.request.post(url, body);
    }

    complete(item: TrainingCompleteDto): Observable<void> {
        const url = '/api/services/app/Training/Complete';
        const body: string = item.toJSON();

        return this.request.post(url, body);
    }

    update(item: TrainingDto): Observable<void> {
        const url = '/api/services/app/Training/Update';
        const body: string = item.toJSON();

        return this.request.put(url, body);
    }

    change(item: TrainingChangeDto): Observable<void> {
        const url = '/api/services/app/Training/Change';
        const body: string = item.toJSON();

        return this.request.post(url, body);
    }

    delete(id: number): Observable<void> {
        let url = '/api/services/app/Training/Delete?';
        if (id !== null && id !== undefined)
            url += "Id=" + encodeURIComponent("" + id) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.delete(url);
    }

    getPendingAttendance(emailAddress: string): Observable<PagedResultDto<TrainingPendingAttendanceDto>> {
        let url = '/api/services/app/Training/GetPendingAttendance?';
        if (emailAddress !== null && emailAddress !== undefined)
            url += "EmailAddress=" + encodeURIComponent("" + emailAddress) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new PagedResultDto<TrainingPendingAttendanceDto>().fromJS(data, TrainingPendingAttendanceDto))));
    }

    updateWatchedVideo(subscriptionId: number): Observable<void> {
        let url = '/api/services/app/TrainingSubscription/UpdateWatchedVideo';

        return this.request.put(url, { id: subscriptionId });
    }

    getTrainingEvaluation(trainingId: number): Observable<TrainingEvaluationDto> {
        let url = '/api/services/app/TrainingEvaluation/GetByTrainingId?';
        if (trainingId !== null && trainingId !== undefined)
            url += "Id=" + encodeURIComponent("" + trainingId) + "&";
        url = url.replace(/[?&]$/, "");

        return this.request.get(url).pipe(mergeMap((data: any) => of(new TrainingEvaluationDto().fromJS(data))));
    }

    submitTrainingEvaluation(evaluationSubmission: TrainingEvaluationSubmissionDto): Observable<void> {
        let url = '/api/services/app/TrainingEvaluation/ResolveEvaluation';

        return this.request.post(url, evaluationSubmission.toJSON());
    }

    saveSatisfaction(satisfaction: TrainingSatisfactionDto): Observable<void> {
        let url = '/api/services/app/PublicAccessTraining/CreateCalification';

        return this.request.post(url, satisfaction.toJSON());
    }
    updateCalificationState(id: number): Observable<void> {
        let url = '/api/services/app/TrainingSubscription/UpdateCalificationState' ;
         

        return this.request.put(url,{id: id} );
    }
}

export class TrainingDto implements IJsonConvertable<TrainingDto> {
    id!: number;
    title!: string;
    capacity!: number;
    center!: string;
    leader!: string;
    description!: string;
    location!: string;
    trainingTime!: DateTime;
    trainingStartTime!: DateTime;
    trainingEndTime!: DateTime;
    trainingUrl!: string;
    session!: TrainingSession;
    sessionTime!: DateTime;
    sessionDescription!: string;
    sessionCalification!: number;
    endTrainingTime?: DateTime;
    trainingMode!: TrainingTrainingModeDto;
    trainingType!: TrainingTrainingTypeDto;
    status!: TrainingStatus;
    code!: string;
    recordType?: TrainingRecordType;
    hasAssistenceRecord? : boolean;
    responsibleRecordId? : number;
    isObligatory?: boolean;
    hasEvaluation?: boolean;
    uploadResources!: UploadResource[];
    trainingResources!: TrainingTrainingResourceDto[];
    trainingCompleteResources!: TrainingTrainingCompleteResourceDto[];
    trainingCustomers!: TrainingTrainingCustomerDto[];
    trainingSubscriptions!: TrainingTrainingSubscriptionDto[];
    trainingInscriptions!: TrainingTrainingInscriptionDto[];
    trainingAssistances!: TrainingTrainingAssistanceDto[];
    trainingCalifications!: TrainingTrainingCalificationDto[];
    trainingOperations!: TrainingTrainingOperationDto[];

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.title = data["title"];
            this.capacity = data["capacity"];
            this.center = data["center"];
            this.leader = data["leader"];
            this.description = data["description"];
            this.location = data["location"];
            this.trainingTime = data["trainingTime"] ? DateTime.fromISO(data["trainingTime"]) : <any>undefined;
            this.trainingStartTime = data["trainingStartTime"] ? DateTime.fromISO(data["trainingStartTime"]) : <any>undefined;
            this.trainingEndTime = data["trainingEndTime"] ? DateTime.fromISO(data["trainingEndTime"]) : <any>undefined;
            this.trainingUrl = data["trainingUrl"];
            this.session = data["session"];
            this.sessionTime = data["sessionTime"] ? DateTime.fromISO(data["sessionTime"]) : <any>undefined;
            this.sessionDescription = data["sessionDescription"];
            this.sessionCalification = data["sessionCalification"];
            this.endTrainingTime = data["endTrainingTime"] ? DateTime.fromISO(data["endTrainingTime"]) : <any>undefined;
            this.trainingMode = data["trainingMode"] ? new TrainingTrainingModeDto().fromJS(data["trainingMode"]) : <any>undefined;
            this.trainingType = data["trainingType"] ? new TrainingTrainingTypeDto().fromJS(data["trainingType"]) : <any>undefined;
            this.status = data["status"];
            this.code = data["code"];
            this.recordType = data["recordType"];
            this.hasAssistenceRecord = data["hasAssistenceRecord"];
            this.responsibleRecordId = data["responsibleRecordId"];
            this.isObligatory = data["isObligatory"];
            this.hasEvaluation = data["hasEvaluation"];
            this.trainingResources = [];
            this.trainingCompleteResources = [];
            this.trainingCustomers = [];
            this.trainingSubscriptions = [];
            this.trainingInscriptions = [];
            this.trainingAssistances = [];
            this.trainingCalifications = [];
            this.trainingOperations = [];

            if (Array.isArray(data["trainingResources"])) {
                for (let item of data["trainingResources"])
                    this.trainingResources.push(new TrainingTrainingResourceDto().fromJS(item));
            }
            if (Array.isArray(data["trainingCompleteResources"])) {
                for (let item of data["trainingCompleteResources"])
                    this.trainingCompleteResources.push(new TrainingTrainingCompleteResourceDto().fromJS(item));
            }
            if (Array.isArray(data["trainingCustomers"])) {
                for (let item of data["trainingCustomers"])
                    this.trainingCustomers.push(new TrainingTrainingCustomerDto().fromJS(item));
            }
            if (Array.isArray(data["trainingSubscriptions"])) {
                for (let item of data["trainingSubscriptions"])
                    this.trainingSubscriptions.push(new TrainingTrainingSubscriptionDto().fromJS(item));
            }
            if (Array.isArray(data["trainingInscriptions"])) {
                for (let item of data["trainingInscriptions"])
                    this.trainingInscriptions.push(new TrainingTrainingInscriptionDto().fromJS(item));
            }
            if (Array.isArray(data["trainingAssistances"])) {
                for (let item of data["trainingAssistances"])
                    this.trainingAssistances.push(new TrainingTrainingAssistanceDto().fromJS(item));
            }
            if (Array.isArray(data["trainingCalifications"])) {
                for (let item of data["trainingCalifications"])
                    this.trainingCalifications.push(new TrainingTrainingCalificationDto().fromJS(item));
            }
            if (Array.isArray(data["trainingOperations"])) {
                for (let item of data["trainingOperations"])
                    this.trainingOperations.push(new TrainingTrainingOperationDto().fromJS(item));
            }
        }
    }

    fromJS(data: any): TrainingDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["title"] = this.title;
        data["capacity"] = this.capacity;
        data["center"] = this.center;
        data["leader"] = this.leader;
        data["description"] = this.description;
        data["location"] = this.location;
        data["trainingTime"] = this.trainingTime ? this.trainingTime.toJSON() : <any>undefined;
        data["trainingStartTime"] = this.trainingStartTime ? this.trainingStartTime.toJSON() : <any>undefined;
        data["trainingEndTime"] = this.trainingEndTime ? this.trainingEndTime.toJSON() : <any>undefined;
        data["trainingUrl"] = this.trainingUrl;
        data["session"] = this.session;
        data["sessionTime"] = this.sessionTime ? this.sessionTime.toJSON() : <any>undefined;
        data["sessionCalification"] = this.sessionCalification;
        data["endTrainingTime"] = this.endTrainingTime ? this.endTrainingTime.toJSON() : <any>undefined;
        data["trainingMode"] = this.trainingMode ? this.trainingMode.toJSON() : <any>undefined;
        data["trainingType"] = this.trainingType ? this.trainingType.toJSON() : <any>undefined;
        data["code"] = this.code;
        data["hasAssistenceRecord"] = this.hasAssistenceRecord;
        data["responsibleRecordId"] = this.responsibleRecordId;
        data["recordType"] = this.recordType;
        data["isObligatory"] = this.isObligatory;
        data["hasEvaluation"] = this.hasEvaluation;
        data["uploadResources"] = [];
        data["trainingResources"] = [];
        data["trainingCompleteResources"] = [];
        data["trainingCustomers"] = [];
        data["trainingSubscriptions"] = [];
        data["trainingInscriptions"] = [];
        data["trainingAssistances"] = [];
        data["trainingCalifications"] = [];

        if (Array.isArray(this.uploadResources)) {
            for (let item of this.uploadResources)
                data["uploadResources"].push(item.toJSON());
        }
        if (Array.isArray(this.trainingResources)) {
            for (let item of this.trainingResources)
                data["trainingResources"].push(item.toJSON());
        }
        if (Array.isArray(this.trainingCompleteResources)) {
            for (let item of this.trainingCompleteResources)
                data["trainingCompleteResources"].push(item.toJSON());
        }
        if (Array.isArray(this.trainingCustomers)) {
            for (let item of this.trainingCustomers)
                data["trainingCustomers"].push(item.toJSON());
        }
        if (Array.isArray(this.trainingSubscriptions)) {
            for (let item of this.trainingSubscriptions)
                data["trainingSubscriptions"].push(item.toJSON());
        }
        if (Array.isArray(this.trainingInscriptions)) {
            for (let item of this.trainingInscriptions)
                data["trainingInscriptions"].push(item.toJSON());
        }
        if (Array.isArray(this.trainingAssistances)) {
            for (let item of this.trainingAssistances)
                data["trainingAssistances"].push(item.toJSON());
        }
        if (Array.isArray(this.trainingCalifications)) {
            for (let item of this.trainingCalifications)
                data["trainingCalifications"].push(item.toJSON());
        }

        return data;
    }
}

export class TrainingCompleteDto implements IToJsonConvertable<TrainingCompleteDto> {
    id!: number;
    sessionTime!: DateTime;
    sessionDescription!: string;
    endTrainingTime?: DateTime;
    uploadResources!: UploadResource[];

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["sessionTime"] = this.sessionTime ? this.sessionTime.toJSON() : <any>undefined;
        data["sessionDescription"] = this.sessionDescription;
        data["endTrainingTime"] = this.endTrainingTime ? this.endTrainingTime.toJSON() : <any>undefined;
        data["uploadResources"] = [];

        if (Array.isArray(this.uploadResources)) {
            for (let item of this.uploadResources)
                data["uploadResources"].push(item.toJSON());
        }

        return data;
    }
}

export class TrainingTrainingModeDto implements IJsonConvertable<TrainingTrainingModeDto> {
    id!: number;
    name!: string;
    capacityRequired!: boolean;
    code!: string;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.name = data["name"];
            this.capacityRequired = data["capacityRequired"];
            this.code = data["code"];
        }
    }

    fromJS(data: any): TrainingTrainingModeDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;

        return data;
    }
}

export interface ITrainingTrainingCustomerDto {
    id: number;
    customer: IntegrationBusinessPartnerDto;
}

export class TrainingTrainingCustomerDto implements ITrainingTrainingCustomerDto, IJsonConvertable<TrainingTrainingCustomerDto> {
    id!: number;
    customer!: IntegrationBusinessPartnerDto;

    //readonly
    remove!: boolean;
    isHidden!: boolean;

    constructor(data?: ITrainingTrainingCustomerDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.customer = data["customer"] ? new IntegrationBusinessPartnerDto().fromJS(data["customer"]) : <any>undefined;
        }
    }

    fromJS(data: any): TrainingTrainingCustomerDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["customer"] = this.customer ? this.customer.toEntity() : <any>undefined;
        data["remove"] = this.remove;

        return data;
    }
}

export class TrainingTrainingTypeDto implements IJsonConvertable<TrainingTrainingTypeDto> {
    id!: number;
    name!: string;
    code!: string;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.name = data["name"];
            this.code = data["code"];
        }
    }

    fromJS(data: any): TrainingTrainingTypeDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;

        return data;
    }
}

export class TrainingTrainingResourceDto implements IJsonConvertable<TrainingTrainingResourceDto> {
    id!: number;
    name!: string;
    resource!: string;
    size!: string;
    mimeType!: string;

    //readonly
    type?: 'video' | 'image' | 'file';
    extension?: string;
    icon?: string;
    path?: string;
    remove!: boolean;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.name = data["name"];
            this.resource = data["resource"];
            this.size = bytesToSize(data["size"]);
            this.mimeType = data["mimeType"];
            this.type = data["type"];
            this.path = resource(data["resource"]);

            const resourceInfo = resourceType(this.mimeType);

            this.type = resourceInfo.type;
            this.extension = resourceInfo.extension;
            this.icon = resourceInfo.icon;
        }
    }

    fromJS(data: any): TrainingTrainingResourceDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["remove"] = this.remove;

        return data;
    }
}

export class TrainingTrainingCompleteResourceDto implements IJsonConvertable<TrainingTrainingCompleteResourceDto> {
    id!: number;
    name!: string;
    resource!: string;
    size!: string;
    mimeType!: string;

    //readonly
    type?: 'video' | 'image' | 'file';
    extension?: string;
    icon?: string;
    path?: string;
    remove!: boolean;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.name = data["name"];
            this.resource = data["resource"];
            this.size = bytesToSize(data["size"]);
            this.mimeType = data["mimeType"];
            this.type = data["type"];
            this.path = resource(data["resource"]);

            const resourceInfo = resourceType(this.mimeType);

            this.type = resourceInfo.type;
            this.extension = resourceInfo.extension;
            this.icon = resourceInfo.icon;
        }
    }

    fromJS(data: any): TrainingTrainingCompleteResourceDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["remove"] = this.remove;

        return data;
    }
}

export interface ITrainingTrainingSubscriptionDto {
    emailAddress: string;
}

export class TrainingTrainingSubscriptionDto implements ITrainingTrainingSubscriptionDto, IJsonConvertable<TrainingTrainingSubscriptionDto> {
    id!: number;
    emailAddress!: string;
    sended!: boolean;

    //readonly
    remove!: boolean;
    isHidden!: boolean;

    constructor(data?: ITrainingTrainingSubscriptionDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.emailAddress = data["emailAddress"];
            this.sended = data["sended"];
        }
    }

    fromJS(data: any): TrainingTrainingSubscriptionDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["emailAddress"] = this.emailAddress;
        data["remove"] = this.remove;

        return data;
    }
}

export class TrainingTrainingInscriptionDto implements IJsonConvertable<TrainingTrainingInscriptionDto> {
    id!: number;
    creationTime!: DateTime;
    documentType!: TrainingDocumentTypeDto;
    document!: string;
    name!: string;
    emailAddress!: string;
    company!: string;
    status!: TrainingInscriptionStatus;
    code!: string;
    sended!: boolean;

    //readonly
    remove!: boolean;
    isHidden!: boolean;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.creationTime = data["creationTime"] ? DateTime.fromISO(data["creationTime"]) : <any>undefined;
            this.documentType = data["documentType"] ? new TrainingDocumentTypeDto().fromJS(data["documentType"]) : <any>undefined;
            this.document = data["document"];
            this.name = data["name"];
            this.emailAddress = data["emailAddress"];
            this.company = data["company"];
            this.status = data["status"];
            this.code = data["code"];
            this.sended = data["sended"];
        }
    }

    fromJS(data: any): TrainingTrainingInscriptionDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["documentType"] = this.documentType ? this.documentType.toJSON() : <any>undefined;
        data["document"] = this.document;
        data["name"] = this.name;
        data["emailAddress"] = this.emailAddress;
        data["company"] = this.company;
        data["status"] = this.status;
        data["remove"] = this.remove;

        return data;
    }
}

export class TrainingDocumentTypeDto implements IJsonConvertable<TrainingDocumentTypeDto> {
    id!: number;
    name!: string;
    hasMinLength!: boolean;
    minLength!: number;
    hasMaxLength!: boolean;
    maxLength!: number;
    hasMask!: boolean;
    mask!: string;
    hasRegex!: boolean;
    regex!: string;
    code!: string;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.name = data["name"];
            this.hasMinLength = data["hasMinLength"];
            this.minLength = data["minLength"];
            this.hasMaxLength = data["hasMaxLength"];
            this.maxLength = data["maxLength"];
            this.hasMask = data["hasMask"];
            this.mask = data["mask"];
            this.hasRegex = data["hasRegex"];
            this.regex = data["regex"];
            this.code = data["code"];
        }
    }

    fromJS(data: any): TrainingDocumentTypeDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;

        return data;
    }
}

export class TrainingTrainingAssistanceDto implements IJsonConvertable<TrainingTrainingAssistanceDto> {
    id!: number;
    emailAddress!: string;
    documentType!: TrainingDocumentTypeDto;
    document!: string;
    name!: string;
    company!: string;
    certificateSended!: boolean;
    certificateSendedTime!: DateTime;
    code!: string;

    //readonly
    remove!: boolean;
    isHidden!: boolean;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.emailAddress = data["emailAddress"];
            this.documentType = data["documentType"] ? new TrainingDocumentTypeDto().fromJS(data["documentType"]) : <any>undefined;
            this.document = data["document"];
            this.name = data["name"];
            this.company = data["company"];
            this.certificateSended = data["certificateSended"];
            this.certificateSendedTime = data["certificateSendedTime"] ? DateTime.fromISO(data["certificateSendedTime"]) : <any>undefined;
            this.code = data["code"];
        }
    }

    fromJS(data: any): TrainingTrainingAssistanceDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["emailAddress"] = this.emailAddress;
        data["documentType"] = this.documentType ? this.documentType.toJSON() : <any>undefined;
        data["document"] = this.document;
        data["name"] = this.name;
        data["company"] = this.company;
        data["code"] = this.code;
        data["remove"] = this.remove;

        return data;
    }
}

export class TrainingUserDto implements IFromJsonConvertable<TrainingUserDto> {
    id!: number;
    name!: string;
    surname!: string;
    secondSurname!: string;
    emailAddress!: string;
    phoneNumber!: string;
    isActive!: boolean;
    code!: string;
    isValid!: boolean;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.name = data["name"];
            this.surname = data["surname"];
            this.emailAddress = data["emailAddress"];
            this.phoneNumber = data["phoneNumber"];
            this.isActive = data["isActive"];
            this.code = data["code"];
            this.isValid = data["isValid"];
        }
    }

    fromJS(data: any): TrainingUserDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }
}

export class TrainingTrainingCalificationDto implements IJsonConvertable<TrainingTrainingCalificationDto> {
    id!: number;
    creationTime!: DateTime;
    calification!: number;
    description!: string;
    code!: string;

    //readonly
    remove!: boolean;
    isHidden!: boolean;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.creationTime = data["creationTime"] ? DateTime.fromISO(data["creationTime"]) : <any>undefined;
            this.calification = data["calification"];
            this.description = data["description"];
            this.code = data["code"];
        }
    }

    fromJS(data: any): TrainingTrainingCalificationDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["remove"] = this.remove;

        return data;
    }
}

export interface ITrainingChangeDto {
    id: number;
    trainingTime: DateTime;
    trainingStartTime: DateTime;
    trainingEndTime: DateTime;
}

export class TrainingChangeDto implements IToJsonConvertable<TrainingChangeDto> {
    id!: number;
    trainingTime!: DateTime;
    trainingStartTime!: DateTime;
    trainingEndTime!: DateTime;

    constructor(data?: ITrainingChangeDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["trainingTime"] = this.trainingTime ? this.trainingTime.toJSON() : <any>undefined;
        data["trainingStartTime"] = this.trainingStartTime ? this.trainingStartTime.toJSON() : <any>undefined;
        data["trainingEndTime"] = this.trainingEndTime ? this.trainingEndTime.toJSON() : <any>undefined;

        return data;
    }
}

export class TrainingTrainingOperationDto implements IFromJsonConvertable<TrainingTrainingOperationDto> {
    id!: number;
    creationTime!: DateTime;
    creationUser!: TrainingUserDto;
    type!: TrainingOperationType;
    trainingTime!: DateTime;
    trainingStartTime!: DateTime;
    trainingEndTime!: DateTime;

    //readonly
    remove!: boolean;
    isHidden!: boolean;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.creationTime = data["creationTime"] ? DateTime.fromISO(data["creationTime"]) : <any>undefined;
            this.creationUser = data["creationUser"] ? new TrainingUserDto().fromJS(data["creationUser"]) : <any>undefined;
            this.type = data["type"];
            this.trainingTime = data["trainingTime"] ? DateTime.fromISO(data["trainingTime"]) : <any>undefined;
            this.trainingStartTime = data["trainingStartTime"] ? DateTime.fromISO(data["trainingStartTime"]) : <any>undefined;
            this.trainingEndTime = data["trainingEndTime"] ? DateTime.fromISO(data["trainingEndTime"]) : <any>undefined;
        }
    }

    fromJS(data: any): TrainingTrainingOperationDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }
}

export const enum TrainingSession {
    None,
    Presencial,
    Virtual
}

export const enum TrainingInscriptionStatus {
    Registered,
    Awaiting,
    Canceled
}

export const enum TrainingStatus {
    Pending,
    Changed,
    Started,
    Completed,
    Canceled
}

export const enum TrainingOperationType {
    Created,
    Changed,
    Started,
    Completed,
    Canceled,
}

export const enum TrainingRecordType {
    Induction,
    Training,
    Coaching,
    Simulacrum,
    InformativeTalk
}

export class TrainingPendingAttendanceDto implements IJsonConvertable<TrainingPendingAttendanceDto> {
    id!: number;
    title!: string;
    trainingType!: string;
    trainingMode!: string;
    trainingStartTime!: DateTime;
    trainingEndTime!: DateTime;
    location!: string;
    center!: string;
    attended: boolean = false;
    centerName!: string;
    leader!: string;
    status!: string;
    code!: string;
    subscriptionId!: number;
    watchedVideo: boolean = false;
    hasEvaluation: boolean = false; // Corregir el nombre del campo
    isEvaluationCompleted: boolean = false;

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.title = data["title"];
            this.trainingType = data["trainingType"];
            this.trainingMode = data["trainingMode"];
            this.trainingStartTime = data["trainingStartTime"] ? DateTime.fromISO(data["trainingStartTime"]) : <any>undefined;
            this.trainingEndTime = data["trainingEndTime"] ? DateTime.fromISO(data["trainingEndTime"]) : <any>undefined;
            this.location = data["location"];
            this.center = data["center"];
            this.centerName = data["centerName"];
            this.leader = data["leader"];
            this.status = data["status"];
            this.code = data["code"];
            this.attended = data["attended"];
            this.subscriptionId = data["subscriptionId"];
            this.hasEvaluation = data["hasEvaluation"];
            this.isEvaluationCompleted = data["isEvaluationCompleted"];
        }
    }

    static fromJS(data: any): TrainingPendingAttendanceDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingPendingAttendanceDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingPendingAttendanceDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["title"] = this.title;
        data["trainingType"] = this.trainingType;
        data["trainingMode"] = this.trainingMode;
        data["trainingStartTime"] = this.trainingStartTime ? this.trainingStartTime.toJSON() : <any>undefined;
        data["trainingEndTime"] = this.trainingEndTime ? this.trainingEndTime.toJSON() : <any>undefined;
        data["location"] = this.location;
        data["center"] = this.center;
        data["centerName"] = this.centerName;
        data["leader"] = this.leader;
        data["status"] = this.status;
        data["code"] = this.code;
        data["subscriptionId"] = this.subscriptionId;
        data["hasEvaluation"] = this.hasEvaluation;
        data["isEvaluationCompleted"] = this.isEvaluationCompleted;
        return JSON.stringify(data);
    }
}

export interface ITrainingPendingAttendanceDto {
    id: number;
    title: string;
    trainingType: string;
    trainingMode: string;
    trainingStartTime: DateTime;
    trainingEndTime: DateTime;
    location: string;
    center: string;
    centerName: string;
    leader: string;
    status: string;
    code: string;
    subscriptionId: number;
    hasEvaluation: boolean;
    isEvaluationCompleted: boolean;
}

export class TrainingEvaluationDto implements IJsonConvertable<TrainingEvaluationDto> {
    id!: number;
    title!: string;
    description!: string;
    isActive!: boolean;
    trainingId!: number;
    questions!: TrainingEvaluationQuestionDto[];

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.title = data["title"];
            this.description = data["description"];
            this.isActive = data["isActive"];
            this.trainingId = data["trainingId"];
            this.questions = [];

            if (Array.isArray(data["questions"])) {
                for (let item of data["questions"])
                    this.questions.push(new TrainingEvaluationQuestionDto().fromJS(item));
            }
        }
    }

    static fromJS(data: any): TrainingEvaluationDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["title"] = this.title;
        data["description"] = this.description;
        data["isActive"] = this.isActive;
        data["trainingId"] = this.trainingId;
        data["questions"] = [];

        if (Array.isArray(this.questions)) {
            for (let item of this.questions)
                data["questions"].push(item.toJSON());
        }

        return JSON.stringify(data);
    }
}

export class TrainingEvaluationQuestionDto implements IJsonConvertable<TrainingEvaluationQuestionDto> {
    id!: number;
    questionText!: string;
    optionA!: string;
    optionB!: string;
    optionC!: string;
    optionD!: string;
    order!: number;
    trainingEvaluationId!: number;
    selectedAnswer?: string; // Para almacenar la respuesta seleccionada

    init(data: any): void {
        if (data) {
            this.id = data["id"];
            this.questionText = data["questionText"];
            this.optionA = data["optionA"];
            this.optionB = data["optionB"];
            this.optionC = data["optionC"];
            this.optionD = data["optionD"];
            this.order = data["order"];
            this.trainingEvaluationId = data["trainingEvaluationId"];
        }
    }

    static fromJS(data: any): TrainingEvaluationQuestionDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationQuestionDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationQuestionDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["questionText"] = this.questionText;
        data["optionA"] = this.optionA;
        data["optionB"] = this.optionB;
        data["optionC"] = this.optionC;
        data["optionD"] = this.optionD;
        data["order"] = this.order;
        data["trainingEvaluationId"] = this.trainingEvaluationId;
        return data;
    }
}

export class TrainingEvaluationSubmissionDto implements IJsonConvertable<TrainingEvaluationSubmissionDto> {
    evaluationId!: number;
    trainingSubscriptionId!: number;
    answers!: TrainingEvaluationAnswerDto[];

    constructor() {
        this.answers = [];
    }

    init(data: any): void {
        if (data) {
            this.evaluationId = data["evaluationId"];
            this.trainingSubscriptionId = data["trainingSubscriptionId"];
            this.answers = [];

            if (Array.isArray(data["answers"])) {
                for (let item of data["answers"])
                    this.answers.push(new TrainingEvaluationAnswerDto().fromJS(item));
            }
        }
    }

    static fromJS(data: any): TrainingEvaluationSubmissionDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationSubmissionDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationSubmissionDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["evaluationId"] = this.evaluationId;
        data["trainingSubscriptionId"] = this.trainingSubscriptionId;
        data["answers"] = [];

        if (Array.isArray(this.answers)) {
            for (let item of this.answers)
                data["answers"].push(item.toJSON());
        }

        return data;
    }
}

export class TrainingEvaluationAnswerDto implements IJsonConvertable<TrainingEvaluationAnswerDto> {
    questionId!: number;
    selectedAnswer!: string;

    init(data: any): void {
        if (data) {
            this.questionId = data["questionId"];
            this.selectedAnswer = data["selectedAnswer"];
        }
    }

    static fromJS(data: any): TrainingEvaluationAnswerDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingEvaluationAnswerDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingEvaluationAnswerDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["questionId"] = this.questionId;
        data["selectedAnswer"] = this.selectedAnswer;
        return data;
    }

}

export class TrainingSatisfactionDto implements IJsonConvertable<TrainingSatisfactionDto> {
    calification!: number;
    description!: string;
    trainingId!: string;
    subscriptionId!: number;
 

    init(data: any): void {
        if (data) {
            this.calification = data["calification"];
            this.description = data["description"];
            this.trainingId = data["trainingId"];
            this.subscriptionId = data["subscriptionId"];
  
        }
    }

    static fromJS(data: any): TrainingSatisfactionDto {
        data = typeof data === 'object' ? data : {};
        let result = new TrainingSatisfactionDto();
        result.init(data);
        return result;
    }

    fromJS(data: any): TrainingSatisfactionDto {
        data = typeof data === 'object' ? data : {};
        this.init(data);
        return this;    
    }
    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["calification"] = this.calification;
        data["description"] = this.description;
        data["trainingId"] = this.trainingId;
        data["isFromDeferred"] = true;
        data["calificationId"] = this.trainingId;
        return data;
    }
}

