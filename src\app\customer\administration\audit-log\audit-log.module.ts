import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AuditLogRoutingModule } from './audit-log.routing.module';
import { AuditLogDashboardComponent } from './dashboard/dashboard.component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';
import { AuditLogDetailComponent } from './detail/detail.component';

@NgModule({
  imports: [
    AuditLogRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    AuditLogDashboardComponent,
    AuditLogDetailComponent
  ]
})
export class AuditLogModule { }