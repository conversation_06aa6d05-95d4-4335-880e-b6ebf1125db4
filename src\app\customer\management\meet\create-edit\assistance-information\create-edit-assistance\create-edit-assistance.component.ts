import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isValidDocumentType } from '@core/utils/document.util';
import { isNullEmptyOrWhiteSpace, isValidEmailAddress } from '@core/utils/tools';
import { DocumentTypeServiceProxy } from '@proxies/document-type.proxy';
import { MeetDocumentTypeDto, MeetDto, MeetMeetAssistanceDto } from '@proxies/meet.proxy';
import { PersonServiceProxy } from '@proxies/person.proxy';
import { finalize } from 'rxjs';

enum ModalStep {
    Find,
    Typing
}

@Component({
    selector: 'app-create-edit-meet-assistance',
    templateUrl: 'create-edit-assistance.component.html',
    styleUrls: [
        'create-edit-assistance.component.scss'
    ]
})
export class MeetCreateEditAssistanceComponent extends ViewComponent implements OnInit {

    private documentTypeServiceProxy: DocumentTypeServiceProxy;
    private personServiceProxy: PersonServiceProxy;
    private formBuilder: FormBuilder;

    @Input() meet!: MeetDto;
    @Input() meetAssistance!: MeetMeetAssistanceDto;
    @Input() index!: number;

    documentTypes!: MeetDocumentTypeDto[];
    modalForm!: FormGroup;

    loaded: boolean = false;
    disabled: boolean = false;
    step: ModalStep = ModalStep.Find;
    steps = {
        find: ModalStep.Find,
        typing: ModalStep.Typing
    };

    get meetDocumentType(): AbstractControl {
        return this.modalForm.controls['meetDocumentTypeSelect'];
    };

    get meetDocument(): AbstractControl {
        return this.modalForm.controls['meetDocumentInput'];
    };

    get meetName(): AbstractControl {
        return this.modalForm.controls['meetNameInput'];
    };

    get meetEmailAddress(): AbstractControl {
        return this.modalForm.controls['meetEmailAddressInput'];
    };

    get meetCompany(): AbstractControl {
        return this.modalForm.controls['meetCompanyInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.documentTypeServiceProxy = _injector.get(DocumentTypeServiceProxy);
        this.personServiceProxy = _injector.get(PersonServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            meetDocumentTypeSelect: ['-1'],
            meetDocumentInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
            meetNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            meetEmailAddressInput: ['', Validators.compose([Validators.required, Validators.email, Validators.maxLength(255)])],
            meetCompanyInput: ['', Validators.compose([Validators.required, Validators.maxLength(512)])],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.documentTypeServiceProxy
            .getAll({ maxResultCount: 1000, skipCount: 0 })
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.documentTypes = response.items.map(p => new MeetDocumentTypeDto().fromJS(p));
                    this.loaded = true;

                    this.meetDocumentType.setValue(this.meetAssistance.documentType?.id || '-1');
                    this.meetDocument.setValue(this.meetAssistance.document || '');
                    this.meetName.setValue(this.meetAssistance.name || '');
                    this.meetEmailAddress.setValue(this.meetAssistance.emailAddress || '');
                    this.meetCompany.setValue(this.meetAssistance.company || '');
                },
                error: async () => await this.dialog.dismiss()
            });

        this.step = this.index == undefined || this.index == null ?
            ModalStep.Find :
            ModalStep.Typing;
    }

    async search(): Promise<void> {
        const documentType: MeetDocumentTypeDto = this.documentTypes.find(p => p.id == this.meetDocumentType.value);

        if (!documentType) {
            this.message.info('El tipo de documento es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.meetDocument.value)) {
            this.message.info('El Nº de documento es obligatorio', 'Aviso');
            return;
        }
        if (!isValidDocumentType({
            value: this.meetDocument.value,
            hasMinLength: documentType.hasMinLength,
            minLength: documentType.minLength,
            hasMaxLength: documentType.hasMaxLength,
            maxLength: documentType.maxLength,
            hasRegex: documentType.hasRegex,
            regex: documentType.regex,
            hasMask: documentType.hasMask,
            mask: documentType.mask
        })) {
            this.message.info('El Nº de documento es inválido', 'Aviso');
            return;
        }

        const document: string = this.meetDocument.value;

        if (this.disabled)
            return;

        this.disabled = true;

        const loading = await this.loader.show();

        this.personServiceProxy
            .find(documentType.id, document)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    if (response) {
                        this.meetDocumentType.setValue(response.documentType?.id || -1);
                        this.meetDocument.setValue(response.document);
                        this.meetName.setValue(response.name);
                        this.meetEmailAddress.setValue(response.emailAddress);
                        this.meetCompany.setValue(response.company);

                        this.step = this.steps.typing;
                    } else {
                        this.meetName.setValue('');
                        this.meetEmailAddress.setValue('');
                        this.step = this.steps.typing;
                    }
                }
            });
    }

    async save(): Promise<void> {
        const documentType: MeetDocumentTypeDto = this.documentTypes.find(p => p.id == this.meetDocumentType.value);

        if (!documentType) {
            this.message.info('El tipo de documento es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.meetDocument.value)) {
            this.message.info('El Nº de documento es obligatorio', 'Aviso');
            return;
        }
        if (!isValidDocumentType({
            value: this.meetDocument.value,
            hasMinLength: documentType.hasMinLength,
            minLength: documentType.minLength,
            hasMaxLength: documentType.hasMaxLength,
            maxLength: documentType.maxLength,
            hasRegex: documentType.hasRegex,
            regex: documentType.regex,
            hasMask: documentType.hasMask,
            mask: documentType.mask
        })) {
            this.message.info('El Nº de documento es inválido', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.meetName.value)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.meetEmailAddress.value)) {
            this.message.info('El correo electrónico es obligatorio', 'Aviso');
            return;
        }
        if (!isValidEmailAddress(this.meetEmailAddress.value)) {
            this.message.info('El correo electrónico es inválido', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.meetCompany.value)) {
            this.message.info('El empresa es obligatoria', 'Aviso');
            return;
        }

        let index: number = 0;

        for (let meetAssistance of this.meet.meetAssistances) {

            if ((this.index === undefined || this.index !== index) && this.meetEmailAddress.value == meetAssistance.emailAddress) {
                this.message.info(`Ya existe un usuario registrado con el correo electrónico: ${this.meetEmailAddress.value}`, 'Aviso');
                return;
            }

            if ((this.index === undefined || this.index !== index) && (documentType.id == meetAssistance.documentType?.id && this.meetDocument.value == meetAssistance.document)) {
                this.message.info(`Ya existe un usuario registrado con el ${documentType.name} Nº ${this.meetDocument.value}`, 'Aviso');
                return;
            }

            index++;
        }

        this.meetAssistance.documentType = documentType;
        this.meetAssistance.document = this.meetDocument.value;
        this.meetAssistance.name = this.meetName.value;
        this.meetAssistance.emailAddress = this.meetEmailAddress.value;
        this.meetAssistance.company = this.meetCompany.value;

        this.dialog.dismiss(this.meetAssistance);
    }
}