import { Component, Injector, Input, OnInit, NgModule, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { UploadResource } from '@core/utils/core.request';
import { IUploadProgressRespose } from '@core/models/app-config';
import { AppAuditoryService } from '@core/services/auditory/auditory.service';
import { Status, DocumentDto, DocumentStatusDto, TagDto, DocumentTypeDto, StatusDto, MacroProcessDto, ProcessDto, SubProcessDto, DocumentServiceProxy, DocumentModifierDto, DocumentTagDto, DocumentResource, DocumentTypeFormat } from '@proxies/auditory/document.proxy';
import { EmployeeDto } from '@proxies/employee.proxy';
import { UserDto } from '@proxies/user.proxy';
import { IntegrationAreaSigDto, IntegrationManagementSigDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { AreaPositionDto, ProcessTierDto, Tier } from '@proxies/auditory/area-position.proxy'
import { AppAuditoryFindEmployeeComponent } from '@components/auditory/find-employee/find-employee.component';
import { AppFindTagComponent } from '@components/auditory/find-tag/find-tag.component';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { AppDocumentPreviewComponent } from '@components/file-preview-pdf/file-preview-pdf.component';
import { finalize } from 'rxjs';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { DocumentStatusUpdateComponent } from './status-update/status-update-component';
import { IModalAction } from '@theme/modals/modal-footer/app-modal-footer.component';
import { TagModel } from 'ngx-chips/core/tag-model'
import { DocumentNotificationDto, DocumentNotificationServiceProxy } from '@proxies/auditory/document-notification.proxy';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})
export class DocumentCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;
    @Input() onlyview: boolean = false;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    private documentServiceProxy: DocumentServiceProxy;
    private documentNotificationServiceProxy: DocumentNotificationServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private auditoryDocumentService: AppAuditoryService;
    private formBuilder: FormBuilder;

    processControl: boolean
    processTier: ProcessTierDto | undefined;

    size: number = 15_728_640;
    uploadResources: UploadResource[] = [];
    item: DocumentDto = new DocumentDto();
    disabled: boolean = false;
    types: DocumentTypeDto[] = [];
    statuses: StatusDto[];
    macroProcesses: MacroProcessDto[] = [];
    processes: ProcessDto[] = [];
    subProcesses: SubProcessDto[] = [];
    areas: IntegrationAreaSigDto[] = [];
    tags: TagDto[];
    managements: IntegrationManagementSigDto[];
    users: UserDto[];
    loaded: boolean = false;
    isOnPreview: boolean = false;
    showToReview: boolean = false;
    showReview: boolean = false;
    showAprove: boolean = false;
    showValid: boolean = false;
    showReject: boolean = false;
    allowOverWrite: boolean = false;

    modalForm!: FormGroup;
    modalActions: IModalAction[] = [
        { label: 'Rechazar', color: 'red', class: 'button button-solid btn btn-danger', fill: '', name: 'reject', call: () => this.updateStatus(false) },
        { label: 'Pasar a Vigente', color: 'red', class: 'button button-solid btn btn-danger', fill: '', name: 'valid', call: () => this.updateStatusAll() },
        { label: 'Pasar a Revisión', color: 'red', class: 'button button-solid btn btn-danger', fill: '', name: 'toreview', call: () => this.updateStatus(true) },
        { label: 'Pasar a Revisado', color: 'red', class: 'button button-solid btn btn-danger', fill: '', name: 'review', call: () => this.updateStatus(true) },
        { label: 'Pasar a Aprobado', color: 'red', class: 'button button-solid btn btn-danger', fill: '', name: 'approve', call: () => this.updateStatus(true) },
    ];

    availableUsers: DocumentModifierDto[] = [];
    availableNotification: DocumentNotificationDto[] = [];
    availableTags: DocumentTagDto[] = [];
    tableStatuses: DocumentStatusDto[] = [];
    locationTypes: string[] = ["VIRTUAL", "FÍSICO"];
    provisionTypes: string[] = ["ARCHIVAMIENTO", "ELIMINACIÓN"];

    get documentName(): AbstractControl {
        return this.modalForm.controls['documentNameInput'];
    };

    get documentCode(): AbstractControl {
        return this.modalForm.controls['documentCodeInput'];
    };

    get documentVersion(): AbstractControl {
        return this.modalForm.controls['documentVersionInput'];
    };

    get documentCreatedBy(): AbstractControl {
        return this.modalForm.controls['documentCreatedByInput'];
    };

    get documentStatus(): AbstractControl {
        return this.modalForm.controls['documentStatusSelect'];
    };

    get documentArea(): AbstractControl {
        return this.modalForm.controls['documentAreaSelect'];
    };

    get documentManagement(): AbstractControl {
        return this.modalForm.controls['documentManagementSelect'];
    };

    get documentType(): AbstractControl {
        return this.modalForm.controls['documentTypeSelect'];
    };

    get documentMacroProcess(): AbstractControl {
        return this.modalForm.controls['documentMacroProcessSelect'];
    };

    get documentProcess(): AbstractControl {
        return this.modalForm.controls['documentProcessSelect'];
    };

    get documentSubProcess(): AbstractControl {
        return this.modalForm.controls['documentSubProcessSelect'];
    };

    get documentModifier(): AbstractControl {
        return this.modalForm.controls['documentModifierSelect'];
    };

    get documentNotification(): AbstractControl {
        return this.modalForm.controls['documentNotificationSelect'];
    };

    get documentTag(): AbstractControl {
        return this.modalForm.controls['documentTagSelect'];
    };

    get documentLocation(): AbstractControl {
        return this.modalForm.controls['documentLocationInput'];
    };

    get documentLocationType(): AbstractControl {
        return this.modalForm.controls['documentLocationTypeSelect'];
    };

    get documentProvision(): AbstractControl {
        return this.modalForm.controls['documentProvisionSelect'];
    };

    get documentRetentionTime(): AbstractControl {
        return this.modalForm.controls['documentRetentionTimeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.documentServiceProxy = _injector.get(DocumentServiceProxy);
        this.documentNotificationServiceProxy = _injector.get(DocumentNotificationServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.auditoryDocumentService = _injector.get(AppAuditoryService);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            documentTypeSelect: [{ value: '-1', disabled: true }, Validators.compose([Validators.required])],
            documentStatusSelect: [{ value: '1', disabled: true }, Validators.compose([Validators.required])],
            documentManagementSelect: ['-1', Validators.compose([Validators.required])],
            documentMacroProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentSubProcessSelect: ['-1', Validators.compose([Validators.required])],
            documentAreaSelect: ['-1', Validators.compose([Validators.required])],
            documentNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(100)])],
            documentCodeInput: [{ value: '', disabled: true }, Validators.compose([])],
            documentVersionInput: [{ value: '1', disabled: true }, Validators.compose([])],
            documentTagSelect: [{ value: this.availableTags, readonly: this.onlyview }],
            documentLocationTypeSelect: ['-1'],
            documentLocationInput: [''],
            documentRetentionTimeInput: [''],
            documentProvisionSelect: ['-1'],
            documentNotificationSelect: [this.availableNotification],
            documentModifierSelect: [this.availableUsers],
            documentCreatedByInput: [{ value: this.session?.user?.name + ' ' + this.session?.user?.surname, disabled: true }, Validators.compose([])]
        });
    }

    async ngOnInit(): Promise<void> {
        this.processControl = this.session?.processTier?.find(x => x.processid == 0) != null;
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (this.onlyview) {
            this.modalForm.disable();
        }

        this.documentServiceProxy
            .get(this.id)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    this.item = response;
                    this.processTier = this.session?.processTier?.find(x => x.processid.toString() == this.item.documentProcess.processid && x.tiers.length > 0);
                    console.log(this.processTier);
                    const documentprocess = this.processes.filter(x => x.id == this.item.documentProcess.processid)[0];
                    this.documentName.setValue(this.item.name.toUpperCase() || '');
                    this.documentCode.setValue(this.item.code || '');
                    this.documentVersion.setValue(this.item.version || '1');
                    this.documentType.setValue(this.item.documentTypeId || '-1');
                    this.documentStatus.setValue(this.item.documentStatusId || '-1');
                    this.documentManagement.setValue(this.item.documentManagementCode || '-1');
                    this.documentArea.setValue(this.item.documentAreaCode || '-1');
                    this.documentMacroProcess.setValue(documentprocess.macroprocessid || '-1');
                    this.documentProcess.setValue(documentprocess.id || '-1');
                    this.documentSubProcess.setValue(this.item.documentProcessId || '-1');
                    this.documentLocation.setValue(this.item.location)
                    this.documentLocationType.setValue(this.item.locationType)
                    this.documentProvision.setValue(this.item.provision)
                    this.documentRetentionTime.setValue(this.item.retentionTime)

                    this.documentMacroProcess.disable();
                    this.documentProcess.disable();
                    this.documentManagement.disable();
                    this.documentArea.disable();
                    if (this.item.documentStatusId != Status.WorkingOn && this.item.documentStatusId != Status.Rejected) {
                        this.documentName.disable();
                    }

                    if (this.processTier?.tiers?.includes(Tier.Owner) || this.processControl) {
                        this.table.records = this.getDataStatus(this.item.documentStatuses.slice(0, 10))
                        this.table.totalRecordsCount = this.item.documentStatuses.length
                    }
                    else {
                        let resultado = Object.values(
                            this.item.documentStatuses.reduce((acc, item) => {
                                if (
                                    !acc[item.statusid] ||
                                    item.createdat > acc[item.statusid].createdat
                                ) {
                                    acc[item.statusid] = item;
                                }
                                return acc;
                            }, {} as Record<number, DocumentStatusDto>)
                        );
                        resultado = resultado.filter(x =>
                            x.statusid == Status.Approved ||
                            x.statusid == Status.Reviewed ||
                            x.statusid == Status.WorkingOn)
                        this.item.documentStatuses = resultado;
                        this.table.records = this.getDataStatus(this.item.documentStatuses.slice(0, 10))
                        this.table.totalRecordsCount = this.item.documentStatuses.length
                    }

                    let creator = this.auditoryDocumentService.users.find(x => x.emailAddress == this.item.createdBy);
                    this.documentCreatedBy.setValue(creator?.name + ' ' + creator?.surname)
                    for (let tagdata of this.item.documentTags) {
                        tagdata.name = this.tags.find(tag => tag.id === tagdata.tagid)?.name || '';
                        this.availableTags.push(tagdata);
                    }
                    this.documentTag.setValue(this.availableTags);
                    for (let modifierData of this.item.documentModifiers) {
                        modifierData.name = this.users.find(user => user.id === modifierData.id)?.emailAddress || '';
                        this.availableUsers.push(modifierData);
                    }
                    this.documentModifier.setValue(this.availableUsers);

                    this.availableNotification = this.item.documentNotifications;
                    this.documentNotification.setValue(this.availableNotification);

                    this.showReview = this.item.documentStatusId == Status.ToReview && this.processTier?.tiers?.includes(Tier.Reviewer);
                    this.showAprove = this.item.documentStatusId == Status.Reviewed && this.processTier?.tiers?.includes(Tier.Approval);
                    this.showValid = (this.processControl && this.item.createdBy == this.session.user?.emailAddress && this.item.documentStatusId != Status.Valid && !this.processTier?.tiers?.includes(Tier.Modifier)) ||
                        (this.processControl && this.item.documentStatusId == Status.Approved);
                    this.showReject = this.showReview || this.showAprove || (this.processControl && this.item.documentStatusId == Status.Approved)
                    this.showToReview = ((this.item.documentStatusId == Status.WorkingOn || this.item.documentStatusId == Status.Rejected) && !this.processControl) ||
                        ((this.item.documentStatusId == Status.WorkingOn || this.item.documentStatusId == Status.Rejected) && this.processControl &&
                            this.processTier?.tiers?.includes(Tier.Modifier));

                    this.modalActions = this.modalActions.filter(x => !this.onlyview && 
                        ((x.name == 'toreview' && this.showToReview) ||
                        ((this.processControl || this.processTier != null) && 
                            ((x.name == 'reject' && this.showReject) ||
                                (x.name == 'valid' && this.showValid) ||
                                (x.name == 'review' && this.showReview) ||
                                (x.name == 'approve' && this.showAprove))))
                    )

                    //if (this.disableEdit()) {
                    //    this.onlyview = true;
                    //    this.modalForm.disable();
                    //}

                    //if (this.onlyOverWrite()) {
                    //    this.allowOverWrite = true;
                    //}

                    this.loaded = true;
                },
                error: async () => await this.dialog.dismiss()
            });
    }

    disableEdit(): boolean {
        return (this.item.documentStatusId != Status.WorkingOn &&
            this.item.documentStatusId != Status.Rejected) &&
            !(this.processControl &&
                this.item.documentStatusId == Status.Approved)
    }

    onlyOverWrite(): boolean {
        return ((this.processTier?.tiers?.includes(Tier.Reviewer) &&
            this.item.documentStatusId == Status.ToReview) ||
            (this.processTier?.tiers?.includes(Tier.Approval) &&
                this.item.documentStatusId == Status.Reviewed) && !this.onlyview)
    }

    async save(): Promise<void> {
        this.item.name = this.documentName.value.toUpperCase();
        this.item.documentTypeId = this.documentType.value;
        this.item.documentStatusId = this.documentStatus.value;
        this.item.documentManagementCode = this.documentManagement.value;
        this.item.documentAreaCode = this.documentArea.value;
        this.item.version = this.documentVersion.value;
        this.item.documentProcessId = this.documentSubProcess.value;
        this.item.location = (this.documentLocation.value ?? "").toUpperCase();
        this.item.locationType = this.documentLocationType.value;
        this.item.provision = this.documentProvision.value;
        this.item.retentionTime = this.documentRetentionTime.value;
        this.item.documentTags = [];
        for (let tagdata of this.documentTag.value) {
            let tag = new DocumentTagDto();
            tag.documentid = this.item.id;
            tag.tagid = tagdata.tagid;
            this.item.documentTags.push(tag);
        }
        this.item.documentNotifications = [];
        for (let notificationdata of this.documentNotification.value) {
            let notification = new DocumentNotificationDto();
            notification.documentid = this.item.id;
            notification.email = notificationdata.email;
            notification.read = notificationdata.read;
            notification.send = notificationdata.send;
            this.item.documentNotifications.push(notification);
        }
        this.item.documentModifiers = [];
        for (let modifierdata of this.documentModifier.value) {
            let modifier = new DocumentModifierDto();
            modifier.documentid = this.item.id;
            modifier.email = modifierdata.email;
            this.item.documentModifiers.push(modifier);
        }
        this.item.uploadResources = this.uploadResources;
        this.item.filePath = this.item.uploadResources.length != 0 ? this.item.uploadResources[0].name : this.item.filePath;

        if (this.item.documentTypeId < 0) {
            this.message.info('El tipo del documento es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.documentManagementCode) {
            this.message.info('La gerencia es obligatoria', 'Aviso');
            return;
        }

        if (!this.item.documentAreaCode) {
            this.message.info('El area es obligatoria', 'Aviso');
            return;
        }

        if (this.uploadResources.length == 0 && !this.id) {
            this.message.info('Debe ingresar un archivo de manera obligatoria', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            if (this.uploadResources.length == 0) {
                this.documentServiceProxy
                    .update(this.item)
                    .pipe(finalize(async () => {
                        this.disabled = false;
                        await loading.dismiss();
                    })).subscribe({
                        next: () => {
                            this.notify.success('Documento actualizado exitosamente', 5000);
                            this.dialog.dismiss(true);
                        }
                    });
            } else {
                this.uploads(this.item, async () => {
                    this.documentServiceProxy
                        .update(this.item)
                        .pipe(finalize(async () => {
                            this.disabled = false;
                            await loading.dismiss();
                        })).subscribe({
                            next: () => {
                                this.notify.success('Documento actualizado exitosamente', 5000);
                                this.dialog.dismiss(true);
                            }
                        });
                });
            }
        } else {
            this.uploads(this.item, async () => {
                this.documentServiceProxy
                    .create(this.item)
                    .pipe(finalize(async () => {
                        this.disabled = false;
                        await loading.dismiss();
                    })).subscribe({
                        next: () => {
                            this.notify.success('Documento creado exitosamente', 5000);
                            this.dialog.dismiss(true);
                        }
                    });
            });
        }
    }


    getData(event?: TableLazyLoadEvent): void {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        let take = this.table.getMaxResultCount(this.paginator, event);
        let skip = this.table.getSkipCount(this.paginator, event);

        this.tableStatuses = [...this.item.documentStatuses];
        this.table.totalRecordsCount = this.tableStatuses.length;
        this.table.records = this.getDataStatus(this.tableStatuses.slice(skip, take + skip));
    }

    getStatus(data: number): string {
        return this.auditoryDocumentService.filter?.statuses?.find(y => y.id == data)?.name
    }

    getDataStatus(data: DocumentStatusDto[]): any {
        return data.map(x => {
            let usr = this.auditoryDocumentService.users?.find(y => y.emailAddress == x.createdby)
            return {
                ...x,
                status: this.auditoryDocumentService.filter?.statuses?.find(y => y.id == x.statusid)?.name,
                user: usr?.name + ' ' + usr?.surname
            }
        });
    }

    private getInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.documentServiceProxy
                .getInputs()
                .subscribe({
                    next: (response) => {
                        this.macroProcesses = response.macroProcess;
                        this.subProcesses = response.subProcess;
                        this.processes = response.process;
                        this.tags = response.tags;
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private getAreasAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllAreaSig().subscribe({
                next: (response) => {
                    this.areas = response.items;
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private getManagementAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllManagementSig().subscribe({
                next: (response) => {
                    this.managements = response.items;
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private async loadInputs(): Promise<void> {
        await Promise.all([
            this.getInputs(),
            this.getAreasAsync(),
            this.getManagementAsync()
        ]);
        this.types = this.auditoryDocumentService.filter.documentTypes;
        this.statuses = this.auditoryDocumentService.filter.statuses;
        this.users = this.auditoryDocumentService.users;
    }

    loadResource(): void {
        let filename = this.item.code + " - " + this.item.name + '.' + this.item.filePath.split('.').pop();
        this.onPreviewFile(this.documentServiceProxy
            .getResource(this.item.fileName, filename, DocumentResource.Pdf));
        this.saveAsRead();
    }

    downloadResource(): void {
        let filename = this.item.code + " - " + this.item.name + '.' + this.item.filePath.split('.').pop();
        window.open(this.documentServiceProxy
            .getResource(this.item.fileName, filename, DocumentResource.Original), '_blank');     
    }

    get processSelect() {
        const selectedValue = this.documentMacroProcess?.value;
        return this.processes.filter(x => x.macroprocessid == selectedValue);
    }

    get subProcessSelect() {
        const selectedValue = this.documentProcess?.value;
        return this.subProcesses.filter(x => x.processid == selectedValue);
    }

    get areasSelect() {
        const selectedValue = this.documentManagement?.value;
        return this.areas.filter(x => x.managementSigQuery?.code == selectedValue);
    }

    onUploadItem(event: UploadResource): void {
        this.uploadResources ??= [];
        if (!event.name.endsWith(".xlsx") && !event.name.endsWith(".docx") && !event.name.endsWith(".pptx")) {
            this.notify.error('El archivo seleccionado debe tener como extension .docx, .xlsx o .pptx');
            return;
        }
        if (this.uploadResources.length == 1) {
            this.notify.warn('Ya ha seleccionado un archivo.', 3000);
            return;
        }

        if (this.id) {
            this.message.confirm('Al guardar el archivo seleccionado sobreescribirá al existente. ¿Desea continuar?', 'Confirmar', async (confirmation) => {
                if (confirmation) {
                    this.notify.success('Archivo seleccionado satisfactoriamente.', 3000);
                    this.uploadResources.push(event);
                    if (!this.documentName.value) {
                        this.documentName.setValue(event.name.toUpperCase());
                    }
                }
            })
        } else {
            this.notify.success('Archivo seleccionado satisfactoriamente.', 3000);
            this.uploadResources.push(event);
            if (!this.documentName.value) {
                this.documentName.setValue(event.name.toUpperCase());
            }
        }

    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.uploadResources ??= [];
                this.uploadResources.splice(index, 1);
            }
        });
    }

    get viewDocumentRelWithId() {
        const selectedValue = this.documentType?.value;
        return this.types.find(x => x.id == selectedValue)?.format == DocumentTypeFormat.Related.toString();
    }

    saveAsRead(): void {
        if (this.item.documentStatusId == Status.Valid) {
            let notification = new DocumentNotificationDto();
            notification.documentid = this.id;
            this.documentNotificationServiceProxy.updateRead(notification).subscribe();
        }
    }

    showDownload(): boolean {
        return this.processControl ||
            this.processTier != null ||
            this.item.documentModifiers.find(x => x.email == this.session?.user?.emailAddress) != null
    }

    onPreviewFile(url: string): void {
        if (this.isOnPreview)
            return;

        this.isOnPreview = true;
        this.dialog.show({
            component: AppDocumentPreviewComponent,
            cssClass: 'transparent',
            componentProps: {
                url: url,
                fileType: 'pdf',
                showAction: true,
                button: {
                    label: 'Marcar como leído',
                    action: () => this.saveAsRead()
                },
                disableRightClick: !this.processControl && !this.processTier?.tiers?.includes(Tier.Owner)
            }
        }).then(() => this.isOnPreview = false)
            .catch(() => this.isOnPreview = false);
    }

    updateStatus(nextstatus: boolean) {
        if (this.uploadResources.length != 0) {
            this.message.confirm("El reemplazo del archivo aún no se ha Guardado, realmente desea continuar?", 'Aviso', async (confirmation) => {
                if (confirmation) {
                    this.dialog.showWithData<boolean>({
                        component: DocumentStatusUpdateComponent,
                        componentProps: {
                            id: this.id,
                            currentstatus: this.item.documentStatusId,
                            nextstatus: nextstatus,
                            processed: (result) => {
                                if (result) {
                                    this.dialog.dismiss(true);
                                }
                            }
                        }
                    });
                }
            });
        } else {
            this.dialog.showWithData<boolean>({
                component: DocumentStatusUpdateComponent,
                componentProps: {
                    id: this.id,
                    currentstatus: this.item.documentStatusId,
                    nextstatus: nextstatus,
                    processed: (result) => {
                        if (result) {
                            this.dialog.dismiss(true);
                        }
                    }
                }
            });
        }
    }

    updateStatusAll() {
        if (this.uploadResources.length != 0) {
            this.message.confirm("El reemplazo del archivo aún no se ha Guardado, realmente desea continuar?", 'Aviso', async (confirmation) => {
                if (confirmation) {
                    this.item.documentStatusId = Status.Approved;
                    this.dialog.showWithData<boolean>({
                        component: DocumentStatusUpdateComponent,
                        componentProps: {
                            id: this.id,
                            currentstatus: Status.Approved,
                            nextstatus: true,
                            processed: (result) => {
                                if (result) {
                                    this.dialog.dismiss(true);
                                }
                            }
                        }
                    });
                }
            });
        } else {
            this.dialog.showWithData<boolean>({
                component: DocumentStatusUpdateComponent,
                componentProps: {
                    id: this.id,
                    currentstatus: Status.Approved,
                    nextstatus: true,
                    processed: (result) => {
                        if (result) {
                            this.dialog.dismiss(true);
                        }
                    }
                }
            });
        }
    }

    showFindTag(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<TagDto>({
            component: AppFindTagComponent
        }).then((response) => {
            if (response.data.result) {
                let tag = new TagDto().fromJS(response.data.result);
                let newtag = new DocumentTagDto();
                let exist = this.availableTags.filter(x => x.tagid == tag.id);
                if (!exist.length) {
                    newtag.tagid = tag.id;
                    newtag.name = tag.name;
                    this.availableTags.push(newtag);
                } else {
                    this.notify.error('La etiqueta ya ha se encuentra añadida.', 3000)
                }
            }
        });
    }
    showFindEmployee(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto[]>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableEmployee: true,
                selecteds: this.availableNotification.map(x => x.email)
            }
        }).then((response) => {
            if (response.data.result) {
                let employees: EmployeeDto[] = [];
                if (Array.isArray(response.data.result)) {
                    for (let item of response.data.result)
                        employees.push(new EmployeeDto().fromJS(item));
                }
                for (let item of employees) {
                    let notification = new DocumentNotificationDto();
                    let exist = this.availableNotification.filter(x => x.email == item.employee?.email);
                    if (!exist.length && item.employee?.email) {
                        notification.id = item.employee?.empid;
                        notification.email = item.employee?.email;
                        notification.name = item.employee?.email;
                        this.availableNotification.push(notification);
                    }
                }
            }
        });
    }
    showFindUser(): void {
        if (this.onlyview)
            return;

        this.dialog.showWithData<EmployeeDto>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                onlyAvaliableUser: true,
                selecteds: this.availableUsers.map(x => x.email)
            }
        }).then((response) => {
            if (response.data.result) {
                let employees: EmployeeDto[] = [];
                if (Array.isArray(response.data.result)) {
                    for (let item of response.data.result)
                        employees.push(new EmployeeDto().fromJS(item));
                }
                for (let item of employees) {
                    let modifier = new DocumentModifierDto();
                    let exist = this.availableUsers.filter(x => x.email == item.employee?.email);
                    if (!exist.length && item.employee?.email) {
                        modifier.id = item.employee?.empid;
                        modifier.name = item.employee?.email;
                        modifier.email = item.employee?.email;
                        this.availableUsers.push(modifier);
                    }
                }
            }
        });
    }

    removeNotification($event: TagModel): void {
        this.availableNotification = this.availableNotification.filter(x => x.email != $event["email"])
        this.documentNotification.setValue(this.availableNotification);
    }

    removeUser($event: TagModel): void {
        this.availableUsers = this.availableUsers.filter(x => x.id != $event["id"])
        this.documentModifier.setValue(this.availableUsers);
    }

    removeTag($event: TagModel): void {
        this.availableTags = this.availableTags.filter(x => x.tagid != $event["tagid"])
        this.documentTag.setValue(this.availableTags);
    }

    clearModifier(): void {
        this.availableUsers.length = 0;
    }

    clearNotification(): void {
        this.availableNotification.length = 0;
    }

    getOverWrite() {

    }

    private uploads(newDocument: DocumentDto, callback: () => void) {
        if (newDocument.uploadResources.length) {
            this.dialog.show({
                component: AppFileUploadProgressComponent,
                componentProps: {
                    source: 1,
                    files: newDocument.uploadResources.map(p => p.file),
                    processed: (data: IUploadProgressRespose) => {
                        if (data.completed) {
                            let index: number = 0;

                            for (let token of data.tokens) {
                                newDocument.fileName = token;
                                index++;
                            }

                            callback();
                        } else {
                            this.message.error(data.message);
                            this.disabled = false;
                        }
                    }
                }
            });
        } else {
            callback();
        }
    }
}