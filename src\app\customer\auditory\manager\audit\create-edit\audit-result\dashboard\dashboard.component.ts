import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AuditoryDto } from '@proxies/auditory/manager/audit.proxy';
import { AuditoryResultDto, AuditoryResultServiceProxy, AuditoryResultStatus } from '@proxies/auditory/manager/audit-result.proxy';
import { AuditoryResultCreateEditComponent } from '../create-edit/create-edit-component';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { IFilterOption } from '@core/models/filters';
import { IntegrationServiceProxy } from '@proxies/integration.proxy';
import { SubProcessDto } from '@proxies/auditory/document.proxy';

@Component({
    selector: 'app-auditory-result-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class AuditoryResultDashboardComponent extends ViewComponent implements OnInit {

    private auditServiceProxy: AuditoryResultServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;

    @Input() auditory!: AuditoryDto;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    auditoryResultTypeArray: IFilterOption<number>[];
    logisticCenterArray: IFilterOption<string>[];
    processArray: IFilterOption<number>[];
    statusArray: IFilterOption<number>[];
    subprocess: SubProcessDto[];
    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.auditServiceProxy = _injector.get(AuditoryResultServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
    }

    ngOnInit() {
        
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: AuditoryResultCreateEditComponent,
            componentProps: {
                audit: this.auditory
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: AuditoryResultDto): void {
        this.dialog.showWithData<boolean>({
            component: AuditoryResultCreateEditComponent,
            componentProps: {
                audit: this.auditory,
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    previewItem(role: AuditoryResultDto) {
        this.dialog.showWithData<boolean>({
            component: AuditoryResultCreateEditComponent,
            componentProps: {
                id: role.id,
                onlyview: true
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.auditServiceProxy
            .getAll(
                this.auditory?.id,
                this.dataTable?.filters?.['AuditoryTypeId']?.['value'],
                (<IFilterOption<string>[]>this.dataTable?.filters?.['LogisticCenterId']?.['value'])?.map(x => x.value) || [],
                this.dataTable?.filters?.['ProcessId']?.['value'],
                this.dataTable?.filters?.['StatusId']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: AuditoryResultDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                hide: false,
                callback: () => this.editItem(role)
            },
            {
                label: 'Visualizar',
                hide: false,
                callback: () => this.previewItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    private getFiltersAsync(): Promise<void> {
         return new Promise((resolve, reject) => {
             this.auditServiceProxy.getFilters().subscribe({
                 next: (response) => {
                     this.auditoryResultTypeArray = this.parseFilter(response.types);
                     this.processArray = this.parseFilter(response.process);
                     this.subprocess = response.subprocess;
                     resolve()
                 },
                 error: (err) => reject(err)
             });
         });
    }

    private getCenters(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.integrationServiceProxy
                .getAllLogiticCenters()
                .subscribe({
                    next: (response) => {
                        this.logisticCenterArray = this.parseFilter(response.items);
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private async loadFilters(): Promise<void> {
        await Promise.all([
            this.getFiltersAsync(),
            this.getCenters()
        ]);
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }
    
    private parseFilterCode(data: any[]): any {
        return data.map(p => {
            return {
                label: p.code,
                value: p.id ?? p.code
            };
        });
    }

    getCenter(code: string): string {
        return this.logisticCenterArray.find(item => item.value === code)?.label || 'Todas';
    }

    getType(code: number): string {
        return this.auditoryResultTypeArray.find(item => item.value === code)?.label;
    }

    getProcess(code: number): string {
        return this.processArray.find(item => item.value.toString() == this.subprocess.find(x => x.id == code)?.processid)?.label;
    }

    getStatus(code: number): string {
        if(code == AuditoryResultStatus.Pending)
            return "Pendiente"
        else
            return ""
    }
}
