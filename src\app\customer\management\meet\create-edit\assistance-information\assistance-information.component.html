<div class="row">

    <ng-container *ngIf="!completed">

        <div class="col-12 mb-3">
            <label class="fz-normal fw-bold text-dark mb-0">
                Agregar por código
            </label>
            <hr>
        </div>

        <div class="col-sm-12 col-md-8 col-lg-6 col-xl-4 col-xxl-4">

            <div class="ion-input">
                <div class="form-group input-control">
                    <label for="MeetAssistanceCode" class="form-label">
                        Código (*)
                    </label>
                    <div class="form-control-group">
                        <input [disabled]="finding" type="text" class="form-control" [(ngModel)]="findCode"
                            id="MeetAssistanceCode" name="MeetAssistanceCode" maxlength="32" />
                        <ion-button (click)="find()" [disabled]="finding" class="form-action-right" fill="clear"
                            size="small">
                            <ion-icon name="send"></ion-icon>
                        </ion-button>
                    </div>
                </div>
            </div>

        </div>

        <div class="col-12 mb-4">
            <ion-note class="fz-note text-muted">
                Presiona el botón de enviar para agregar automáticamente al usuario por el código ingresado
            </ion-note>
        </div>
    </ng-container>

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de asistencia
        </label>
        <hr>
    </div>

    <div class="col-12 text-end mb-4">
        <ion-button *ngIf="!completed" (click)="createItem()" class="ion-option" size="small" color="primary"
            fill="solid">
            <ion-icon name="add"></ion-icon>
            <ion-label class="fz-small d-none d-lg-inline-block">
                Agregar asistencia
            </ion-label>
        </ion-button>
    </div>

    <div class="col-12 text-end mb-4">
        <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
            <ion-icon name="refresh"></ion-icon>
        </ion-button>

        <ion-button (click)="getData()" class="ion-option me-2" color="tertiary" fill="solid">
            <ion-icon name="search"></ion-icon>
        </ion-button>
    </div>

    <div class="col-12">
        <p-table #dataTable sortMode="multiple" [value]="table.records" [rows]="table.defaultRecordsCountPerPage"
            [paginator]="false" [lazy]="true" [scrollable]="true" ScrollWidth="100%" scrollDirection="horizontal"
            [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th *ngIf="!completed" style="min-width: 130px; max-width: 130px; width: 130px">
                        Acciones
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        T. Documento
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        Nº Documento
                    </th>
                    <th style="min-width: 200px">
                        Nombre
                    </th>
                    <th style="min-width: 250px; max-width: 250px; width: 250px">
                        Correo electrónico
                    </th>
                    <th style="min-width: 250px;">
                        Empresa
                    </th>
                </tr>
                <tr>
                    <th *ngIf="!completed" style="min-width: 130px; max-width: 130px; width: 130px">
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        <p-columnFilter field="documentType" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-multiSelect [ngModel]="value" (onClear)="filter()" (onChange)="filter($event.value)"
                                    [options]="documentTypeArray" [showHeader]="false" [showClear]="true"
                                    placeholder="Todos" optionLabel="label" appendTo="body"></p-multiSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        <p-columnFilter field="document" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Document Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 200px">
                        <p-columnFilter field="name" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Name Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 250px; max-width: 250px; width: 250px">
                        <p-columnFilter field="emailAddress" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="EmailAddress Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 250px;">
                        <p-columnFilter field="company" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText enter aria-label="Company Filter" [ngModel]="value"
                                    (ngModelChange)="filter($event)" (onEnter)="getData()">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr>
                    <td *ngIf="!completed" class="action-column" style="min-width: 130px; max-width: 130px; width: 130px">
                        <ion-button (click)="showActions($event, record, rowIndex)" class="ion-action" color="primary"
                            fill="solid" size="small">
                            <ion-label class="fz-small">
                                Acciones
                            </ion-label>
                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                        </ion-button>
                    </td>
                    <td class="action-column" style="min-width: 150px; max-width: 150px; width: 150px">
                        {{record?.documentType?.name}}
                    </td>
                    <td class="action-column" style="min-width: 150px; max-width: 150px; width: 150px">
                        {{record?.document}}
                    </td>
                    <td style="min-width: 200px">
                        {{record?.name}}
                    </td>
                    <td class="action-column" style="min-width: 250px; max-width: 250px; width: 250px">
                        {{record?.emailAddress}}
                    </td>
                    <td class="action-column" style="min-width: 250px;">
                        {{record?.company}}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
        <div class="primeng-paging-container">
            <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator (onPageChange)="getData($event)"
                [totalRecords]="table.totalRecordsCount" [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                [showCurrentPageReport]="true" dropdownAppendTo="body"
                currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
            </p-paginator>
        </div>
    </div>

</div>