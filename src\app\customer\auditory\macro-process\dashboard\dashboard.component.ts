import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { MacroProcessCreateEditComponent } from '../create-edit/create-edit-component';
import { IFilterOption } from '@core/models/filters';
import { MacroProcessDto, MacroProcessServiceProxy } from '@proxies/auditory/macro-process.proxy';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class MacroProcessDashboardComponent extends ViewComponent implements OnInit {

    private macroProcessServiceProxy: MacroProcessServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.macroProcessServiceProxy = _injector.get(MacroProcessServiceProxy);
    }

    ngOnInit() {

    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: MacroProcessCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: MacroProcessDto): void {
        this.dialog.showWithData<boolean>({
            component: MacroProcessCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(obj: MacroProcessDto) {
        this.message.confirm(`¿Estas seguro de eliminar el macroproceso "${obj.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.macroProcessServiceProxy
                    .delete(obj.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: (response) => {
                            if(response.IsSuccess) {
                                this.notify.success('Se ha eliminado satisfactoriamente', 5000);
                                this.getData();   
                            }
                            else {
                                this.notify.error('Ocurrio un error en la eliminación.', 5000);
                            }
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.macroProcessServiceProxy
            .getAll(
                this.dataTable?.filters?.['Code']?.['value'],
                this.dataTable?.filters?.['Name']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: MacroProcessDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Auditory.MacroProcess'
                ],
                callback: () => this.editItem(role)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Auditory.MacroProcess'
                ],
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}
