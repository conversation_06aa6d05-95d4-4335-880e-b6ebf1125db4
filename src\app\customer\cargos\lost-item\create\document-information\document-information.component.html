<div class="row">

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Im<PERSON><PERSON>s
        </label>
        <hr>
    </div>

    <div class="col-12 mb-3" *ngIf="registerCharge?.images !== null">
        <div class="person-resource">

            <div class="person-resource-box">
                <img *ngIf="registerCharge?.images" (click)="showResource(registerCharge?.images?.resource)"
                    class="person-resource__image" [src]="getResource(registerCharge?.images?.resource)"
                    [alt]="registerCharge?.images?.name" aria-label="Image Resource" />

              
            </div> 

        </div>
    </div>
    <div class="col-12 mb-3"  *ngIf="registerCharge?.images === null">
        <app-file-uploader [images]="true" [files]="false" [videos]="false" (onUploadItem)="onUploadImage($event)">
        </app-file-uploader>
    </div>

    <div class="col-12"   *ngIf="registerCharge?.images === null">
        <div class="alert alert-warning" role="alert">
            Ud. puede seleccionar una imágen (jpg, jpeg, jpe, png) con un tamaño máximo de 15MB
        </div>
    </div>

    <div *ngIf="imageUploadResource" class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Recurso pendiente de publicación
        </label>
        <hr>
    </div>

    <app-file-preview *ngIf="imageUploadResource" [resource]="imageUploadResource" [type]="imageUploadResource.type"
        (onRemove)="onRemoveUploadImage()" />

</div>


<div class="col-12 mb-3">
    <label class="fz-normal fw-bold text-dark mb-0">
        Documentos
    </label>
    <hr>
</div>

<div class="col-12 mb-3" *ngIf="registerCharge?.documents === null">
    <app-file-uploader [images]="false" [files]="true" [videos]="false" (onUploadItem)="onUploadDocument ($event)">
    </app-file-uploader>
</div>

<div class="col-12" *ngIf="registerCharge?.documents === null">
    <div class="alert alert-warning" role="alert">
        Ud. puede seleccionar un documento con un tamaño máximo de 15MB
    </div>
</div>
<div class="col-12 mb-3">

    <app-resource-preview *ngIf="registerCharge?.documents !== null"
        [resource]="getResource(registerCharge?.documents?.resource)" type="file" icon="pdf"
        [name]="registerCharge?.documents?.fileName" [info]="registerCharge?.documents?.size"
        (onRemove)="onRemoveDocument()">
    </app-resource-preview> 
    <!-- <app-resource-preview *ngIf="registerCharge?.documents !== null"
        [resource]="getResource(registerCharge?.documents?.resource)" type="file" icon="pdf"
        [name]="registerCharge?.documents?.fileName" [info]="registerCharge?.documents?.size"
        (onRemove)="onRemoveDocument()">
    </app-resource-preview> -->
</div>



<div *ngIf="documentUploadResource" class="col-12 mb-3">
    <label class="fz-normal fw-bold text-dark mb-0">
        Recurso pendiente de publicación
    </label>
    <hr>
</div>
<!-- 
<app-file-preview *ngIf="documentUploadResource" [resource]="documentUploadResource"
    [type]="documentUploadResource.type" (onRemove)="onRemoveUploadDocument()"/> -->