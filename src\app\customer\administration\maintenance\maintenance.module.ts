import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MaintenanceRoutingModule } from './maintenance.routing.module';
import { MaintenanceDashboardComponent } from './dashboard/dashboard.component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';

@NgModule({
  imports: [
    MaintenanceRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    MaintenanceDashboardComponent
  ]
})
export class MaintenanceModule { }