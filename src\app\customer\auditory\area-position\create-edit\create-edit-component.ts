import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AreaPositionServiceProxy, AreaPositionDto } from '@proxies/auditory/area-position.proxy'
import { AppAuditoryFindEmployeePositionComponent } from '@components/auditory/find-position/find-employee-position.component'
import { AppAuditoryFindEmployeeComponent } from '@components/auditory/find-employee/find-employee.component'
import { finalize } from 'rxjs';
import { IntegrationEmployeePositionDto } from '@proxies/integration.proxy';
import { EmployeeDto } from '@proxies/employee.proxy';
import { ProcessDto, ProcessServiceProxy } from '@proxies/auditory/process.proxy';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})

export class AreaPositionCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;

    private areaPositionServiceProxy: AreaPositionServiceProxy;
    private processServiceProxy: ProcessServiceProxy;
    private formBuilder: FormBuilder;

    item: AreaPositionDto = new AreaPositionDto();
    process: ProcessDto[] = [];
    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get areaPositionProcess(): AbstractControl {
        return this.modalForm.controls['areaPositionProcessSelect'];
    };

    get areaPositionName(): AbstractControl {
        return this.modalForm.controls['areaPositionNameInput'];
    };

    get areaPositionTierCreate(): AbstractControl {
        return this.modalForm.controls['areaPositionTierCreateInput'];
    };

    get areaPositionTierReview(): AbstractControl {
        return this.modalForm.controls['areaPositionTierReviewInput'];
    };

    get areaPositionTierApprove(): AbstractControl {
        return this.modalForm.controls['areaPositionTierApproveInput'];
    };

    get areaPositionTierControl(): AbstractControl {
        return this.modalForm.controls['areaPositionTierControlInput'];
    };

    get areaPositionOwner(): AbstractControl {
        return this.modalForm.controls['areaPositionOwnerInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.areaPositionServiceProxy = _injector.get(AreaPositionServiceProxy);
        this.processServiceProxy = _injector.get(ProcessServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            areaPositionProcessSelect: ['-1', Validators.compose([Validators.required])],
            areaPositionNameInput: ['', Validators.compose([Validators.required])],
            areaPositionTierCreateInput: ['Seleccione un puesto creador...', Validators.compose([Validators.required])],
            areaPositionTierReviewInput: ['Seleccione un puesto revisor...', Validators.compose([Validators.required])],
            areaPositionTierApproveInput: ['Seleccione un puesto aprobador...', Validators.compose([Validators.required])],
            areaPositionTierControlInput: ['SUPERVISOR DE GESTION DE CALIDAD', Validators.compose([Validators.required])],
            areaPositionOwnerInput: ['Seleccione un responsable...', Validators.compose([Validators.required])]
        });
    }

    private loadInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.processServiceProxy
                .getAllFilter()
                .subscribe({
                    next: (response) => {
                        this.process = response.items
                        resolve();

                    }, error: () => reject()
                });
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (this.id) {
            this.areaPositionServiceProxy
                .get(this.id)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: (response) => {
                        this.item = response;
                        this.areaPositionProcess.setValue(this.item.processid);
                        this.areaPositionName.setValue(this.item.name);
                        this.areaPositionTierCreate.setValue(this.item.tiercreate);
                        this.areaPositionTierReview.setValue(this.item.tierreview);
                        this.areaPositionTierApprove.setValue(this.item.tierapprove);
                        this.areaPositionTierControl.setValue(this.item.tiercontrol);
                        this.areaPositionOwner.setValue(this.item.owner);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        }
        else {
            await loading.dismiss();
            this.disabled = false;
        }
    }

    async save(): Promise<void> {
        this.item.processid = this.areaPositionProcess.value;
        this.item.name = this.areaPositionName.value;
        this.item.tiercreate = this.areaPositionTierCreate.value;
        this.item.tierreview = this.areaPositionTierReview.value;
        this.item.tierapprove = this.areaPositionTierApprove.value;
        this.item.tiercontrol = this.areaPositionTierControl.value;
        this.item.owner = this.areaPositionOwner.value;

        if (!this.item.processid) {
            this.message.info('El proceso es obligatoria', 'Aviso');
            return;
        }

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.tiercreate) {
            this.message.info('El nivel creador es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.tierreview) {
            this.message.info('El nivel revisor es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.tierapprove) {
            this.message.info('El nivel aprobador es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.tiercontrol) {
            this.message.info('El nivel control SIG es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.owner) {
            this.message.info('El responsable es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        this.areaPositionServiceProxy
            .getAll(null, this.item.processid, null, 1, 0)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: (response) => {
                    if(response.totalCount > 0 && response.items[0].id != this.id) {
                        this.notify.error('El proceso ya posee un modelo de autorización.', 5000);
                        return;
                    }

                    if (this.id) {
                        this.areaPositionServiceProxy
                            .update(this.item)
                            .pipe(finalize(async () => {
                                this.disabled = false;
                                await loading.dismiss();
                            })).subscribe({
                                next: () => {
                                    this.notify.success('Modelo actualizado exitosamente', 5000);
                                    this.dialog.dismiss(true);
                                }
                            });
                    } else {
                        this.areaPositionServiceProxy
                            .create(this.item)
                            .pipe(finalize(async () => {
                                this.disabled = false;
                                await loading.dismiss();
                            })).subscribe({
                                next: () => {
                                    this.notify.success('Documento creado exitosamente', 5000);
                                    this.dialog.dismiss(true);
                                }
                            });
                    }
                }
            });
    }

    showFindPosition(control: string): void {
        this.dialog.showWithData<IntegrationEmployeePositionDto>({
            component: AppAuditoryFindEmployeePositionComponent
        }).then((response) => {
            if (response.data.result) {
                let obj = new IntegrationEmployeePositionDto().fromJS(response.data.result);
                if (control == 'create')
                    this.areaPositionTierCreate.setValue(obj.name);
                if (control == 'review')
                    this.areaPositionTierReview.setValue(obj.name);
                if (control == 'approve')
                    this.areaPositionTierApprove.setValue(obj.name);
            }
        });
    }

    showFindEmployee(): void {
        this.dialog.showWithData<EmployeeDto>({
            component: AppAuditoryFindEmployeeComponent,
            componentProps: {
                hideSelectAll: true
            }
        }).then((response) => {
            if (response.data.result) {
                if (Array.isArray(response.data.result)) {
                    for (let item of response.data.result) {
                        let obj = new EmployeeDto().fromJS(item);
                        this.areaPositionOwner.setValue(obj.employee?.email);
                    }
                }
            }
        });
    }
}