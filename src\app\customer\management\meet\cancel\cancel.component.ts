
import { Component, Injector, Input, OnInit } from "@angular/core";
import { AbstractControl, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ViewComponent } from "@core/inheritance/app-component.base";
import { MeetCancelDto, MeetDto, MeetServiceProxy, MeetStatus } from "@proxies/meet.proxy";
import { finalize } from "rxjs";

@Component({
    selector: 'app-meet-cancel',
    templateUrl: 'cancel.component.html',
    styleUrls: [
        'cancel.component.scss'
    ]
})
export class MeetCancelComponent extends ViewComponent implements OnInit {

    private meetServiceProxy: MeetServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;

    item!: MeetDto;
    loaded!: boolean;
    disabled!: boolean;
    modalForm!: FormGroup;

    meetTime!: Date;
    meetStartTime!: Date;
    meetEndTime!: Date;

    get meetDescription(): AbstractControl {
        return this.modalForm.controls['meetDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.meetServiceProxy = _injector.get(MeetServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            meetDescriptionInput: ['', Validators.compose([Validators.required, Validators.maxLength(25000)])],
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.meetServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;
                    this.meetTime = response.startTime.toJSDate();
                    this.meetStartTime = response.startTime.toJSDate();
                    this.meetEndTime = response.endTime.toJSDate();

                    if (this.item.status == MeetStatus.Canceled) {
                        this.message.info('No se puede realizar la operación debido a que la capacitación ha sido cancelada.', 'Aviso');
                        this.dialog.dismiss();
                        return;
                    }

                    if (this.item.status == MeetStatus.Executed) {
                        this.message.info('No se puede realizar la operación debido a que la charla ha sido ejecutada.', 'Aviso');
                        this.dialog.dismiss();
                        return;
                    }

                },
                error: async () => await this.dialog.dismiss()
            });
    }

    async save(): Promise<void> {
        if (this.disabled)
            return;

        this.disabled = true;

        const loading = await this.loader.show();

        const input: MeetCancelDto = new MeetCancelDto();

        input.id = this.item.id;
        input.description = this.meetDescription.value;

        this.meetServiceProxy
            .cancel(input)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: () => {
                    this.notify.success('Charla finalizada satisfactoriamente', 5000);
                    this.dialog.dismiss(true);
                }
            });

    }
}
