import { Component, Injector, Input, OnInit } from "@angular/core";
import { AbstractControl, FormGroup } from "@angular/forms";
import { ViewComponent } from "@core/inheritance/app-component.base";
import { MeetDto } from "@proxies/meet.proxy";

@Component({
    selector: 'app-meet-cancel-information',
    templateUrl: 'cancel-information.component.html',
    styleUrls: [
        'cancel-information.component.scss'
    ]
})
export class MeetCancelInformationComponent extends ViewComponent implements OnInit {

    @Input() meet!: MeetDto;
    @Input() modalForm!: FormGroup;
    @Input() completed!: boolean;
    
    meetCancelDate!: Date;
    meetCancelTime!: Date;

    get trainingSessionDescription(): AbstractControl {
        return this.modalForm.controls['meetCancelDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);
    }

    ngOnInit(): void {
        this.meetCancelDate = this.meet.cancelTime?.toJSDate();
        this.meetCancelTime = this.meet.cancelTime?.toJSDate();
    }
}