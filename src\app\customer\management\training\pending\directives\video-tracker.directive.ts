import { Directive, ElementRef, EventEmitter, Input, Output, AfterViewInit, OnDestroy } from '@angular/core';

@Directive({
    selector: '[appVideoTracker]'
})
export class VideoTrackerDirective implements AfterViewInit, OnDestroy {

    @Input() videoData: any;
    @Output() onVideoStarted = new EventEmitter<any>();
    @Output() onVideoEnded = new EventEmitter<any>();

    private videoElement: HTMLVideoElement | null = null;
    private playListener: (() => void) | null = null;
    private endedListener: (() => void) | null = null;
    private observer: MutationObserver | null = null;

    constructor(private el: ElementRef) {}

    ngAfterViewInit(): void {
        // Buscar el elemento video dentro del componente
        this.findAndAttachToVideo();
        
        // Observar cambios en el DOM por si el video se carga dinámicamente
        this.setupMutationObserver();
    }

    ngOnDestroy(): void {
        this.removeEventListeners();
        if (this.observer) {
            this.observer.disconnect();
        }
    }

    private findAndAttachToVideo(): void {
        const videoElement = this.el.nativeElement.querySelector('video');
        if (videoElement) {
            this.attachToVideo(videoElement);
        }
    }

    private setupMutationObserver(): void {
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const element = node as Element;
                            const video = element.querySelector ? element.querySelector('video') : null;
                            if (video) {
                                this.attachToVideo(video as HTMLVideoElement);
                            } else if (element.tagName === 'VIDEO') {
                                this.attachToVideo(element as HTMLVideoElement);
                            }
                        }
                    });
                }
            });
        });

        this.observer.observe(this.el.nativeElement, {
            childList: true,
            subtree: true
        });
    }

    private attachToVideo(videoElement: HTMLVideoElement): void {
        if (this.videoElement === videoElement) {
            return; // Ya está adjunto a este video
        }

        // Remover listeners anteriores si existen
        this.removeEventListeners();

        this.videoElement = videoElement;

        // Agregar listener para cuando empiece a reproducir
        this.playListener = () => {
            console.log('Video started playing:', this.videoData?.name);
            this.onVideoStarted.emit(this.videoData);
        };

        // Agregar listener para cuando termine
        this.endedListener = () => {
            console.log('Video ended:', this.videoData?.name);
            this.onVideoEnded.emit(this.videoData);
        };

        this.videoElement.addEventListener('play', this.playListener);
        this.videoElement.addEventListener('ended', this.endedListener);

        console.log('Video tracker attached to:', this.videoData?.name);
    }

    private removeEventListeners(): void {
        if (this.videoElement && this.playListener) {
            this.videoElement.removeEventListener('play', this.playListener);
        }
        if (this.videoElement && this.endedListener) {
            this.videoElement.removeEventListener('ended', this.endedListener);
        }
        this.playListener = null;
        this.endedListener = null;
    }
}
