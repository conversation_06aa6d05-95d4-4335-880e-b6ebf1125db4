import { AfterViewInit, Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { FindLogisticCenterComponent   } from '@components/find-logistic-center/find-logistic-center.component';
import { AppSignaturePadComponent } from '@components/signature/signature.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IUploadProgressRespose } from '@core/models/app-config';
import { RegisterCharge, RegisterChargeView } from '@core/models/modelsSCA/register-charge';
import { LogisticCenterDto, LogisticCenterService } from '@core/services/servicesSCA/logistic-center.service';
import { PersonService } from '@core/services/servicesSCA/person.service';
import { ResourceScaService } from '@core/services/servicesSCA/resource-sca.service';
import { UploadResource } from '@core/utils/core.request';
import { isNullEmptyOrWhiteSpace, urlToFile } from '@core/utils/tools';
import { PersonCreateEditComponent } from 'src/app/customer/management/person/create-edit/create-edit-component';

@Component({
  selector: 'app-cargo-general-information',
  templateUrl: './general-information.component.html',
  styleUrls: ['./general-information.component.scss'],
})

export class CargoGeneralInformationComponent extends ViewComponent implements AfterViewInit {
  @ViewChild('emisorPad') emisorPad!: AppSignaturePadComponent;
  @ViewChild('receptorPad') receptorPad!: AppSignaturePadComponent;

  @Input() registerCharge: RegisterChargeView
  modalForm!: FormGroup;
  isDisabled: boolean = false;
  private personaService: PersonService;
  private resourceService: ResourceScaService
  public documentTypes: any[] = [];
  public countries: any[] = [];
  public logisticCenters: any[] = [];
  private formBuilder: FormBuilder;
  mostrarDialogoRegistro: boolean = false;
  mostrarAgregarPersona: boolean = false;


  constructor(_injector: Injector, private router: Router) {
    super(_injector);
    this.personaService = _injector.get(PersonService)
    this.formBuilder = _injector.get(FormBuilder);
    this.resourceService = _injector.get(ResourceScaService);
    this.modalForm = this.formBuilder.group({
      tipoDocumentoEmisor: ['', Validators.compose([Validators.required, Validators.maxLength(15)])],
      tipoDocumentoReceptor: ['', Validators.compose([Validators.required, Validators.maxLength(15)])],
      logisticCenterId: ['', Validators.compose([Validators.required, Validators.maxLength(25)])],
      logisticCenterName: ['Presiona para buscar un centro logístico', Validators.compose([Validators.required, Validators.maxLength(120)])],
      enviadoA: ['', Validators.compose([])],
      descripcionPortador: ['', Validators.compose([])],
      numeroDocumentoEmisor: ['', Validators.compose([])],
      idEmisor: [0, Validators.compose([])],
      nombreEmisor: ['', Validators.compose([Validators.required, Validators.maxLength(25)])],
      correoEmisor: ['', Validators.compose([Validators.required, Validators.maxLength(30)])],
      numeroDocumentoReceptor: ['', Validators.compose([])],
      idReceptor: [0, Validators.compose([])],
      nombreReceptor: ['', Validators.compose([Validators.required, Validators.maxLength(30)])],
      correoReceptor: ['', Validators.compose([Validators.required, Validators.maxLength(30)])]
    });
  }

  getExisteSignatureReceptor(): boolean {
    return this.registerCharge?.signatureReceptor != null
  }

  getExisteSignatureEmisor(): boolean {
    return this.registerCharge?.signatureEmisor != null
  }
  getResource(path: string): string {
    let resultado = this.resourceService.get(path);
    // console.log(resultado)
    return resultado;
  }
  ngAfterViewInit(): void {
    this.loadData()
  }

  signaturePadOptions = {
    canvasWidth: 400,
    canvasHeight: 200,
    penColor: 'black'
  };

  public async getRegisterCharge(): Promise<RegisterCharge> {

    let date = new Date()

    let day = date.getDate()
    let month = date.getMonth() + 1
    let year = date.getFullYear()
    let registerDate: string = ""
    if (month < 10) {
      registerDate = (`${day}-0${month}-${year}`)
    } else {
      registerDate = (`${day}-${month}-${year}`)
    }
    let result = {
      personIdEmisor: this.modalForm.get("idEmisor").value,
      personIdReceptor: this.modalForm.get("idReceptor").value,
      centerId: this.modalForm.get("logisticCenterId").value,
      registerTime: `${registerDate}`,
      enviadoA: this.modalForm.get("enviadoA").value,
      remitePortador: this.modalForm.get("descripcionPortador").value,
      signatureReceptorResource: null,
      SignatureEmisorResource: null,
      DocumentResource: null,
      ImageResource: null,
    }
    console.log("Obteniendo registro para guardar")
    return result;

  }

  loadData() {
    if (this.registerCharge) {
      this.isDisabled = true
      console.log("Cargando Informacion  ", this.registerCharge)
      console.log("register document", this.registerCharge.usuarioReceptor.document);

      this.modalForm.get("tipoDocumentoEmisor").setValue(this.registerCharge.usuarioEmisor.documentTypeId);
      this.modalForm.get("tipoDocumentoReceptor").setValue(this.registerCharge.usuarioReceptor.documentTypeId);

      this.modalForm.get("tipoDocumentoEmisor").disable();
      this.modalForm.get("tipoDocumentoReceptor").disable();


      this.modalForm.get("enviadoA").setValue(this.registerCharge.enviadoA)
      this.modalForm.get("descripcionPortador").setValue(this.registerCharge.remitePortador)
      this.modalForm.get("logisticCenterId").setValue(this.registerCharge.centerLogistic.id);
      this.modalForm.get("logisticCenterName").setValue(this.registerCharge.centerLogistic.name);

      this.modalForm.get("idEmisor").setValue(this.registerCharge.usuarioEmisor.id);

      this.modalForm.get("numeroDocumentoEmisor").setValue(this.registerCharge.usuarioEmisor.document);
      this.modalForm.get("nombreEmisor").setValue(this.registerCharge.usuarioEmisor.fullName);
      this.modalForm.get("correoEmisor").setValue(this.registerCharge.usuarioEmisor.eMailAddress);

      this.modalForm.get("idReceptor").setValue(this.registerCharge.usuarioReceptor.id);

      this.modalForm.get("numeroDocumentoReceptor").setValue(this.registerCharge.usuarioReceptor.document);
      this.modalForm.get("nombreReceptor").setValue(this.registerCharge.usuarioReceptor.fullName);
      this.modalForm.get("correoReceptor").setValue(this.registerCharge.usuarioReceptor.eMailAddress);
      //*********** DISABLED ************/
      this.modalForm.get("enviadoA").disable();
      this.modalForm.get("descripcionPortador").disable();
      this.modalForm.get("logisticCenterId").disable();
      this.modalForm.get("logisticCenterName").disable();
      this.modalForm.get("idEmisor").disable();

      this.modalForm.get("numeroDocumentoEmisor").disable();
      this.modalForm.get("nombreEmisor").disable();
      this.modalForm.get("correoEmisor").disable();

      this.modalForm.get("idReceptor").disable();

      this.modalForm.get("numeroDocumentoReceptor").disable();
      this.modalForm.get("nombreReceptor").disable();
      this.modalForm.get("correoReceptor").disable();
    }
  }

  ngOnInit() {
    this.getAllDocumentTypes();
    this.getAllCountries();

    this.modalForm.get('tipoDocumentoEmisor')?.valueChanges.subscribe(() => {
      this.modalForm.patchValue({
        numeroDocumentoEmisor: '',
        nombreEmisor: '',
        correoEmisor: ''
      });
    });

    this.modalForm.get('tipoDocumentoReceptor')?.valueChanges.subscribe(() => {
      this.modalForm.patchValue({
        numeroDocumentoReceptor: '',
        nombreReceptor: '',
        correoReceptor: ''
      });
    });

    if (this.registerCharge) {

      this.loadData();
    }
  }

  showFindPosition(control: string): void {
    this.dialog.showWithData<LogisticCenterDto>({
      component: FindLogisticCenterComponent
    }).then((response) => {
      if (response.data.result) {
        if (control == 'logisticCenter') {
          this.modalForm.get(`logisticCenterId`)?.setValue(response.data.result.id);
          this.modalForm.get(`logisticCenterName`)?.setValue(response.data.result.name);
        }
      }
    });
  }

  getAllCountries() {
    this.personaService.getCountries().subscribe(
      next => {
        next.items.map(x => {
          this.countries.push({ name: x.name, id: x.id })
        })
      }
    )
  }

  getAllDocumentTypes() {
    this.personaService.getAllDocumentType().subscribe(
      next => {
        next.items.map(x => {
          this.documentTypes.push({ name: x.name, id: x.id })
        })
      }
    )
  }

  onSignatureStart() {
    console.log('Inicio de firma');
  }

  onSignatureEnd() {
    console.log('Fin de firma');
  }

  validaFirmaEmisor(control: AppSignaturePadComponent): boolean {
    if (control.isEmpty()) {
      this.message.info('La firma del emisor es obligatoria.', 'Aviso');
      return false;
    }
    return true
  }

  validaFirmaReceptor(control: AppSignaturePadComponent): boolean {
    if (control.isEmpty()) {
      this.message.info('La firma del receptor es obligatoria.', 'Aviso');
      return false;
    }
    return true
  }

  async generaFirmaFisica(control: AppSignaturePadComponent, alias: string = "receptor"): Promise<UploadResource> {
    const file: globalThis.File = await urlToFile(control.toDataURL('image/jpg', 100), `signature-${alias}.jpg`, 'image/jpg');

    let uploadResource: UploadResource = new UploadResource({
      name: file.name.toLocaleLowerCase(),
      size: file.size,
      mimeType: file.type,
      file: file
    });
    return uploadResource;
  }

  public async getFirmas(): Promise<UploadResource[]> {
    let firmas: UploadResource[] = []
    const emisorDataUrl = this.emisorPad.toDataURL();
    const receptorDataUrl = this.receptorPad.toDataURL();

    if (!this.validaFirmaEmisor(this.emisorPad)) return [];
    if (!this.validaFirmaReceptor(this.receptorPad)) return [];

    let firmaEmisor = await this.generaFirmaFisica(this.emisorPad, "emisor");
    let firmaReceptor = await this.generaFirmaFisica(this.receptorPad);
    firmas.push(firmaEmisor);
    firmas.push(firmaReceptor);
    return firmas;
  }

  borrarFirma(tipo: 'emisor' | 'receptor') {
    if (tipo === 'emisor') {
      this.emisorPad.clear();
    } else {
      this.receptorPad.clear();
    }
  }


  buscarPersona(tipo: 'emisor' | 'receptor') {
    const tipoDocCtrl = this.modalForm.get(`tipoDocumento${this.capitalize(tipo)}`);
    const nroDocCtrl = this.modalForm.get(`numeroDocumento${this.capitalize(tipo)}`);

    if (!tipoDocCtrl || !nroDocCtrl) return;

    this.personaService.getByDocumentType(tipoDocCtrl.value, nroDocCtrl.value).subscribe(next => {
      if (next != null) {
        const fullName = [next.name, next.surname, next.secondSurname]
          .filter(part => !!part && part.trim() !== '')
          .join(' ');
        this.modalForm.get(`id${this.capitalize(tipo)}`)?.setValue(next.id);
        this.modalForm.get(`nombre${this.capitalize(tipo)}`)?.setValue(fullName);
        this.modalForm.get(`correo${this.capitalize(tipo)}`)?.setValue(next.emailAddress);
      } else {
        this.mostrarDialogoRegistro = true;
      }
    });
  }

  capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }

  buscarEmisor() {
    this.buscarPersona('emisor');
  }

  buscarReceptor() {
    this.buscarPersona('receptor');
  }


  registrarPersona() {
    this.mostrarDialogoRegistro = false;

    this.dialog.showWithData<boolean>({
      component: PersonCreateEditComponent,
      componentProps: {
                sca: true,
                countries: this.countries
            }
    }).then((response) => {
      if (response.data.result)
        null;
    });
  }

}
