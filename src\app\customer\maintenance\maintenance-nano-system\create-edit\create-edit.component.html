<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar Sistema 3' : 'Crear Sistema 3'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <ng-container *ngIf="id || maintenanceMicroSystem; else creationEntity">

                    <div class="col-12">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label for="MaintenanceMacroSystemName" class="form-label">
                                    Sistema 1
                                </label>

                                <input *ngIf="maintenanceMicroSystem; else editionEntityMacro" type="text"
                                    class="form-control" id="MaintenanceMacroSystemName"
                                    name="MaintenanceMacroSystemName"
                                    value="{{maintenanceMicroSystem?.maintenanceMacroSystem?.name}} - {{maintenanceMicroSystem?.maintenanceMacroSystem?.code}}"
                                    readonly>

                                <ng-template #editionEntityMacro>
                                    <input type="text" class="form-control" id="MaintenanceMacroSystemName"
                                        name="MaintenanceMacroSystemName"
                                        value="{{item?.maintenanceMicroSystem?.maintenanceMacroSystem?.name}} - {{item?.maintenanceMicroSystem?.maintenanceMacroSystem?.code}}"
                                        readonly>
                                </ng-template>
                            </div>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label for="MaintenanceMicroSystemName" class="form-label">
                                    Sistema 2
                                </label>

                                <input *ngIf="maintenanceMicroSystem; else editionEntityMicro" type="text"
                                    class="form-control" id="MaintenanceMicroSystemName"
                                    name="MaintenanceMicroSystemName"
                                    value="{{maintenanceMicroSystem.name}} - {{maintenanceMicroSystem.code}}" readonly>

                                <ng-template #editionEntityMicro>
                                    <input type="text" class="form-control" id="MaintenanceMicroSystemName"
                                        name="MaintenanceMicroSystemName"
                                        value="{{item?.maintenanceMicroSystem?.name}} - {{item?.maintenanceMicroSystem?.code}}"
                                        readonly>
                                </ng-template>
                            </div>
                        </div>
                    </div>

                </ng-container>

                <ng-template #creationEntity>

                    <div class="col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
                        <div class="ion-input">
                            <div class="input-control">
                                <div class="input-group-close">
                                    <div class="input-group action">
                                        <input (click)="showFindMaintenanceMicroSystem()" class="form-control rounded"
                                            id="MaintenanceSystemSearch" name="MaintenanceSystemSearch"
                                            value="Presione para buscar un Sistema" readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 col-md-12 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                        <div class="ion-input">
                            <div class="input-control">
                                <label for="MaintenanceMacroSystemName" class="form-label">
                                    Sistema 1 (*)
                                </label>
                                <input type="text" class="form-control" id="MaintenanceMacroSystemName"
                                    name="MaintenanceMacroSystemName" [value]="maintenanceMacroSystemLabel" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 col-md-12 col-lg-6 col-xl-6 col-xxl-6 mb-4">
                        <div class="ion-input">
                            <div class="input-control">
                                <label for="MaintenanceMicroSystemName" class="form-label">
                                    Sistema 2 (*)
                                </label>
                                <input type="text" class="form-control" id="MaintenanceMicroSystemName"
                                    name="MaintenanceMicroSystemName" [value]="maintenanceMicroSystemLabel" readonly>
                            </div>
                        </div>
                    </div>

                </ng-template>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceNanoSystemName" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceNanoSystemName.invalid && (maintenanceNanoSystemName.touched || maintenanceNanoSystemName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceNanoSystemName"
                                name="MaintenanceNanoSystemName" formControlName="maintenanceNanoSystemNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceNanoSystemCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceNanoSystemCode.invalid && (maintenanceNanoSystemCode.touched || maintenanceNanoSystemCode.dirty))}">
                                Código (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceNanoSystemCode"
                                name="MaintenanceNanoSystemCode" formControlName="maintenanceNanoSystemCodeInput"
                                maxlength="3">
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>