import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { AlarmTreatmentReasonCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';
import { AlarmTreatmentReasonDto, AlarmTreatmentReasonServiceProxy, AlarmTreatmentReasonType } from '@proxies/alarm-treatment-reason.proxy';

@Component({
    selector: 'app-alarm-treament-reason-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class AlarmTreatmentReasonDashboardComponent extends ViewComponent {

    private alarmTreatmentReasonServiceProxy: AlarmTreatmentReasonServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    enabledStateArray: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    alarmTreatmentReasonTypeArray: IFilterOption<AlarmTreatmentReasonType>[] = [
        { label: 'Siempre', value: AlarmTreatmentReasonType.All },
        { label: 'Cuando sea real', value: AlarmTreatmentReasonType.True },
        { label: 'Cuando no sea real', value: AlarmTreatmentReasonType.False }
    ];

    alarmTreatmentReasonTypes = {
        all: AlarmTreatmentReasonType.All,
        true: AlarmTreatmentReasonType.True,
        false: AlarmTreatmentReasonType.False
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmTreatmentReasonServiceProxy = _injector.get(AlarmTreatmentReasonServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: AlarmTreatmentReasonCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: AlarmTreatmentReasonDto) {
        this.dialog.showWithData<boolean>({
            component: AlarmTreatmentReasonCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: AlarmTreatmentReasonDto) {
        this.message.confirm(`¿Estas seguro de eliminar el motivo de alarma "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.alarmTreatmentReasonServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el motivo de alarma satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.alarmTreatmentReasonServiceProxy
            .getAll(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['enabled']?.['value'],
                this.dataTable?.filters?.['type']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: AlarmTreatmentReasonDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.AlarmTreatmentReason.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.AlarmTreatmentReason.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.alarmTreatmentReasonServiceProxy
            .export(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['enabled']?.['value'],
                this.dataTable?.filters?.['type']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}