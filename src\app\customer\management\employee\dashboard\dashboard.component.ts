import { Component, Injector, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { EmployeeDto, EmployeeServiceProxy } from '@proxies/employee.proxy';
import { DimConsts, IntegrationAreaSigDto, IntegrationCenterLogisticDto, IntegrationProfitCenterDto, IntegrationRoleDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Subscription, finalize } from 'rxjs';
import { EmployeeCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';

@Component({
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class EmployeeDashboardComponent extends ViewComponent implements OnInit, On<PERSON><PERSON>roy {

    private readonly employeeServiceProxy: EmployeeServiceProxy;
    private readonly integrationServiceProxy: IntegrationServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    centers: IntegrationCenterLogisticDto[] = [];
    areas: IntegrationProfitCenterDto[] = [];
    enabledArray: IFilterOption<boolean>[] = [
        { label: 'Activo', value: true },
        { label: 'Inactivo', value: false }
    ];

    private subscription: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.employeeServiceProxy = _injector.get(EmployeeServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
    }

    editItem(data: EmployeeDto) {
        this.dialog.showWithData<boolean>({
            component: EmployeeCreateEditComponent,
            componentProps: {
                id: data.employee.empid
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    ngOnInit() {
        this.integrationServiceProxy.getAllLogiticCenters().subscribe({
            next: (response) => {
                this.centers = response.items;
            }
        });

        this.integrationServiceProxy.getAllProfitCenters(DimConsts.Area).subscribe({
            next: (response) => {
                this.areas = response.items;
            }
        });

        this.getData();
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.employeeServiceProxy
            .getAll(
                this.dataTable?.filters?.['names']?.['value'],
                this.dataTable?.filters?.['enabled']?.['value'],
                this.dataTable?.filters?.['active']?.['value'],
                (<IntegrationCenterLogisticDto[]>this.dataTable?.filters?.['centerIds']?.['value'])?.map(p => p.code) || [],
                (<IntegrationProfitCenterDto[]>this.dataTable?.filters?.['areaIds']?.['value'])?.map(p => p.prcCode) || [],
                (this.dataTable?.filters?.['areaSig']?.['value'] as IntegrationAreaSigDto)?.code,
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, data: EmployeeDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Management.Employee.Modify'
                ],
                callback: () => this.editItem(data)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
        this.getData();
    }
}