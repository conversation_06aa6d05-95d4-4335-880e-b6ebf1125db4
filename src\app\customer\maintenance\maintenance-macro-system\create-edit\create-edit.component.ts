import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { MaintenanceMacroSystemDto, MaintenanceMacroSystemServiceProxy } from '@proxies/maintenance-macro-system.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-family-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MaintenanceMacroSystemCreateEditComponent extends ViewComponent implements OnInit {

    private maintenanceMacroSystemServiceProxy: MaintenanceMacroSystemServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: MaintenanceMacroSystemDto = new MaintenanceMacroSystemDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get maintenanceMacroSystemName(): AbstractControl {
        return this.modalForm.controls['maintenanceMacroSystemNameInput'];
    };

    get maintenanceMacroSystemCode(): AbstractControl {
        return this.modalForm.controls['maintenanceMacroSystemCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceMacroSystemServiceProxy = _injector.get(MaintenanceMacroSystemServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            maintenanceMacroSystemNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            maintenanceMacroSystemCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.maintenanceMacroSystemServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.maintenanceMacroSystemName.setValue(this.item.name);
                        this.maintenanceMacroSystemCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new MaintenanceMacroSystemDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.maintenanceMacroSystemName.value;
        this.item.code = this.maintenanceMacroSystemCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.maintenanceMacroSystemServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.maintenanceMacroSystemServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}