import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MonitoringRoutingModule } from './monitoring.routing.module';
import { MonitoringDashboardComponent } from './dashboard/dashboard.component';
import { ComponentModule } from '@components/component.module';
import { MonitoringEditComponent } from './edit/edit.component';

@NgModule({
  imports: [
    MonitoringRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule,
    ComponentModule
  ],
  declarations: [
    MonitoringDashboardComponent,
    MonitoringEditComponent
  ]
})
export class MonitoringModule { }
