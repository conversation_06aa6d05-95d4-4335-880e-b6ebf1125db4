import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EmployeeDashboardComponent } from './dashboard/dashboard.component';
import { EmployeeCreateEditComponent } from './create-edit/create-edit.component';

const routes: Routes = [
  {
    path: '',
    children: [
      { path: 'dashboard', component: EmployeeDashboardComponent },
      { path: '', pathMatch: 'full', redirectTo: 'dashboard' },
      { path: '**', redirectTo: 'dashboard' }
    ]
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class EmployeeRoutingModule { }
