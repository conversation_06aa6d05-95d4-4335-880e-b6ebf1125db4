import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { MeetDto, MeetOperationType } from '@proxies/meet.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';

@Component({
    selector: 'app-meet-operation-information',
    templateUrl: 'operation-information.component.html',
    styleUrls: [
        'operation-information.component.scss'
    ]
})
export class MeetOperationInformationComponent extends ViewComponent implements OnInit {

    @Input() meet!: MeetDto;
    @Input() completed!: boolean;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    findCode: string = '';
    finding: boolean = false;

    meetOperationTypes = {
        created: MeetOperationType.Created,
        completed: MeetOperationType.Completed,
        canceled: MeetOperationType.Canceled
    }

    private skipCount: number;
    private maxResultCount: number;

    constructor(_injector: Injector) {
        super(_injector);

        this.skipCount = 0;
        this.table.defaultRecordsCountPerPage = 5;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;
    }

    ngOnInit(): void {
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        for (let item of this.meet.meetOperations) {
            item.isHidden = true;
            
            if (index >= skipCount && result < maxResultCount) {
                item.isHidden = false;
                result++;
            }

            index++;
        }

        this.table.totalRecordsCount = index;
    }
}