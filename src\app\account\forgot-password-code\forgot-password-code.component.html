<form *ngIf="loaded" [formGroup]="forgotPasswordForm" class="d-flex flex-column">

    <ion-label class="d-block text-center h4 mb-2">
        Recuperar acceso
    </ion-label>

    <small class="text-muted d-block mb-3" color="dark">
        Por favor ingresa el código de verificación que hemos enviado al correo electrónico: <b>{{emailAddress}}</b>, para restablecer la contraseña.
    </small>

    <div class="form-group mb-4">
        <label class="form-label" for="Code">
            Código de verificación
        </label>
        <div class="form-control-group">
            <input type="email" id="Code" class="form-control" formControlName="codeInput" minlength="6" maxlength="6">
            <div class="form-control-validation">
                El código debe ser de 6 dígitos
            </div>
        </div>
    </div>

    <div class="form-group mb-4">
        <label class="form-label" for="NewPassword">
            Nueva contraseña
        </label>
        <div class="form-control-group">
            <input [type]="inputNewPassword.type" id="NewPassword" class="form-control" formControlName="newPasswordInput" autocomplete="off">
            <ion-button *ngIf="newPassword.value" (click)="inputNewPassword.toggle()" class="form-action-right" fill="clear" size="small">
                <ion-icon [name]="inputNewPassword.icon"></ion-icon>
            </ion-button>
            <div class="form-control-validations" [hidden]="newPassword.valid || newPassword.pristine">
                <ul *ngIf="newPassword.errors">
                    <li [hidden]="!newPassword.errors['requireDigit']">
                        La contraseña deben tener al menos un dígito (0-9).
                    </li>
                    <li [hidden]="!newPassword.errors['requireLowercase']">
                        La contraseña deben tener al menos una minúscula (a-z).
                    </li>
                    <li [hidden]="!newPassword.errors['requireUppercase']">
                        La contraseña deben tener al menos una mayúscula (A-Z).
                    </li>
                    <li [hidden]="!newPassword.errors['requireNonAlphanumeric']">
                        La contraseña deben tener al menos una caracter no alfanumérico.
                    </li>
                    <li [hidden]="!newPassword.errors['requiredLength']">
                        La contraseña deben tener al menos {{settings ? passwordSettings.requiredLength : ''}} caracteres.
                    </li>
                    <li [hidden]="!newPassword.errors['required']">
                        La contraseña es obligatoria.
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="form-group mb-3">
        <label class="form-label" for="NewPasswordRepeat">
            Contraseña (repetir)
        </label>
        <div class="form-control-group">
            <input [type]="inputRepeatPassword.type" id="NewPasswordRepeat" class="form-control" formControlName="newPasswordRepeatInput" autocomplete="off">
            <ion-button *ngIf="newPassword.value" (click)="inputRepeatPassword.toggle()" class="form-action-right" fill="clear" size="small">
                <ion-icon [name]="inputRepeatPassword.icon"></ion-icon>
            </ion-button>
            <div class="form-control-validations" [hidden]="newPasswordRepeat.valid || newPasswordRepeat.pristine">
                <ul *ngIf="newPasswordRepeat.errors">
                    <li [hidden]="!newPasswordRepeat.errors['mustMatch']">
                        Las contraseñas deben ser iguales.
                    </li>
                    <li [hidden]="!newPasswordRepeat.errors['required']">
                        La contraseña es obligatoria.
                    </li>
                </ul>
            </div>
        </div>
    </div>
        
    <ion-button routerLink="/account/login" routerDirection="root" class="me-auto ion-no-padding mb-3" size="small" fill="clear">
        <ion-icon name="chevron-back"></ion-icon>
        <ion-label>
            Iniciar sesión
        </ion-label>
    </ion-button>

    <ion-button (click)="continue()" [disabled]="forgotPasswordForm.invalid" size="normal" expand="block">
        <ion-label>
            Restablecer contraseña
        </ion-label>
    </ion-button>

</form>