import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { EntityDto } from '@core/utils/core.request';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { AlarmAccountDto } from '@proxies/alarm-account.proxy';
import { AlarmPartitionDto, AlarmPartitionServiceProxy } from '@proxies/alarm-partition.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-alarm-partition-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class AlarmPartitionCreateEditComponent extends ViewComponent implements OnInit {

    private alarmPartitionServiceProxy: AlarmPartitionServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;
    @Input() alarmAccount: AlarmAccountDto;
    
    item: AlarmPartitionDto = new AlarmPartitionDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get alarmTypeName(): AbstractControl {
        return this.modalForm.controls['alarmTypeNameInput'];
    };

    get alarmTypeCode(): AbstractControl {
        return this.modalForm.controls['alarmTypeCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmPartitionServiceProxy = _injector.get(AlarmPartitionServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            alarmTypeNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            alarmTypeCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(2)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.alarmPartitionServiceProxy
                .get(this.alarmAccount.id, this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.alarmTypeName.setValue(this.item.name);
                        this.alarmTypeCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new AlarmPartitionDto();
            this.item.alarmAccount = new EntityDto<string>().fromJS(this.alarmAccount);
        }
    }

    async save(): Promise<void> {

        this.item.name = this.alarmTypeName.value;
        this.item.code = this.alarmTypeCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de la partición de alarma es obligatorio', 'Aviso');
            return;
        }
        if (isNullEmptyOrWhiteSpace(this.item.code)) {
            this.message.info('El código de la partición de alarma es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.alarmPartitionServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Partición de alarma actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.alarmPartitionServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Partición de alarma creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}