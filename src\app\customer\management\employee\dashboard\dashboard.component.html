<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Empleados
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button (click)="getData()" class="ion-option me-2" color="tertiary" fill="solid">
                <ion-icon name="search"></ion-icon>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns"
                        (onSort)="getData()">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Management.Employee.Modify'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th style="min-width: 350px" pSortableColumn="firstName">
                                    Recurso
                                    <p-sortIcon field="firstName"></p-sortIcon>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Centro logístico
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Área
                                </th>
                                <th style="min-width: 250px; width: 250px; max-width: 250px;">
                                    Correo electrónico
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    Estado
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    ¿Usuario?
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    Fecha de creación
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    Fecha de modificación
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Management.Employee.Modify'
                                    ] | permissionAny
                                )
                                ">
                                </th>
                                <th style="min-width: 350px">
                                    <p-columnFilter field="names" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <input pInputText enter aria-label="Name Filter" [ngModel]="value"
                                                (ngModelChange)="filter($event)" (onEnter)="getData()">
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="centerIds" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="centers"
                                                [showHeader]="false" [showClear]="true" optionLabel="name"
                                                appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    <p-columnFilter field="areaIds" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="areas" [showHeader]="false"
                                                [showClear]="true" optionLabel="prcName"
                                                appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 250px; width: 250px; max-width: 250px;">
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter field="enabled" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="enabledArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <p-columnFilter field="active" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="enabledArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px"
                                    [hidden]="
                                    !(
                                        [
                                            'Pages.Management.Employee.Modify'
                                        ] | permissionAny
                                    )
                                    ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 350px">
                                    {{record?.employee?.firstName}} {{record?.employee?.middleName}}
                                    {{record?.employee?.lastName}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.centersText}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record?.area?.prcName}}
                                </td>
                                <td style="min-width: 250px; width: 250px; max-width: 250px;">
                                    {{record.employee?.email}}
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <ng-container *ngIf="record.id">
                                        <ion-badge color="tertiary" *ngIf="record.enabled">
                                            Activo
                                        </ion-badge>
                                        <ion-badge color="danger" *ngIf="!record.enabled">
                                            Inactivo
                                        </ion-badge>
                                    </ng-container>
                                </td>
                                <td style="min-width: 150px; width: 150px; max-width: 150px;">
                                    <ng-container *ngIf="record.id">
                                        <ion-badge color="tertiary" *ngIf="record.active">
                                            Activo
                                        </ion-badge>
                                        <ion-badge color="danger" *ngIf="!record.active">
                                            Inactivo
                                        </ion-badge>
                                    </ng-container>
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record?.employee?.createDate | luxonFormat: 'dd/MM/yyyy'}}
                                </td>
                                <td style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record?.lastModificationTime | luxonFormat: 'dd/MM/yyyy HH:mm:ss'}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>