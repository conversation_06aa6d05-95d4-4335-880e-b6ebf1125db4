import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { IntegrationCostCenter, IntegrationMasterIncomeExpenseDto, IntegrationMasterManagementDto, IntegrationServiceCallLogisticCenter, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { OperationTaskDto, OperationTaskServiceProxy } from '@proxies/operation-task.proxy';
import { finalize } from 'rxjs';

const enum OperationTaskIndexes {
    GeneralInformation,
    PersonInformation
}

@Component({
    selector: 'app-operation-task-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class OperationTaskCreateEditComponent extends ViewComponent implements OnInit {

    private operationTaskServiceProxy: OperationTaskServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;
    @Input() editable!: boolean;

    item!: OperationTaskDto;
    loaded!: boolean;
    disabled!: boolean;
    modalForm!: FormGroup;
    activeIndex: OperationTaskIndexes = OperationTaskIndexes.GeneralInformation;

    areas!: IntegrationCostCenter[];
    centers!: IntegrationServiceCallLogisticCenter[];
    managements!: IntegrationMasterManagementDto[];
    incomeExpenses!: IntegrationMasterIncomeExpenseDto[];

    get operationTaskCode(): AbstractControl {
        return this.modalForm.controls['operationTaskCodeInput'];
    };

    get operationTaskHasIntegration(): AbstractControl {
        return this.modalForm.controls['operationTaskHasIntegrationSelect'];
    };

    get projectCode(): AbstractControl {
        return this.modalForm.controls['projectCodeInput'];
    };

    get projectName(): AbstractControl {
        return this.modalForm.controls['projectNameInput'];
    };

    get location(): AbstractControl {
        return this.modalForm.controls['locationInput'];
    };

    get center(): AbstractControl {
        return this.modalForm.controls['centerSelect'];
    };

    get management(): AbstractControl {
        return this.modalForm.controls['managementSelect'];
    };

    get area(): AbstractControl {
        return this.modalForm.controls['areaSelect'];
    };

    get movement(): AbstractControl {
        return this.modalForm.controls['movementSelect'];
    };

    constructor(injector: Injector) {
        super(injector);

        this.operationTaskServiceProxy = injector.get(OperationTaskServiceProxy);
        this.integrationServiceProxy = injector.get(IntegrationServiceProxy);
        this.formBuilder = injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            operationTaskCodeInput: [{ value: '', disabled: true }, Validators.compose([Validators.maxLength(32)])],
            operationTaskHasIntegrationSelect: ['false', Validators.compose([Validators.required])],
            projectCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
            projectNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(5000)])],
            locationInput: ['', Validators.compose([Validators.required, Validators.maxLength(5000)])],
            centerSelect: ['-1', Validators.compose([Validators.required])],
            managementSelect: ['-1', Validators.compose([Validators.required])],
            areaSelect: ['-1', Validators.compose([Validators.required])],
            movementSelect: ['-1', Validators.compose([Validators.required])]
        });
    }

    async ngOnInit(): Promise<void> {

        const loading = await this.loader.show();

        try {

            let result = await Promise.all([
                this.getAreas(),
                this.getCenters(),
                this.getManagements(),
                this.getIncomeExpenses(),
                this.getOperationTask(this.id)
            ]);

            this.areas = result[0];
            this.centers = result[1];
            this.managements = result[2];
            this.incomeExpenses = result[3];
            this.item = result[4];

            if (this.item.id) {
                this.operationTaskCode.setValue(this.item.code);
                this.projectCode.setValue(this.item.projectCode);
                this.projectName.setValue(this.item.projectName);
                this.operationTaskHasIntegration.setValue(this.item.hasIntegration ? 'true' : 'false');
                this.location.setValue(this.item.locationName);
                this.center.setValue(this.item.centerCode);
                this.management.setValue(this.item.managementCode);
                this.area.setValue(this.item.areaCode);
                this.movement.setValue(this.item.movementCode);
            }

            if (this.editable) {

            } else {
                this.modalForm.disable();
            }

            this.loaded = true;
        } catch (error) {
            await this.dialog.dismiss();
        }

        await loading.dismiss();
    }

    async save(): Promise<void> {
        this.item.hasIntegration = this.operationTaskHasIntegration.value == 'true';

        if (this.item.hasIntegration) {

            this.item.projectCode = this.projectCode.value;
            this.item.projectName = this.projectName.value;

            if (isNullEmptyOrWhiteSpace(this.item.projectName) || isNullEmptyOrWhiteSpace(this.item.projectName)) {
                this.message.info('Aviso', 'El proyecto es obligatorio.');
                return;
            }

        } else {

            this.item.projectCode = this.projectCode.value;
            this.item.projectName = this.projectName.value;

            if (isNullEmptyOrWhiteSpace(this.item.projectName)) {
                this.message.info('El nombre del proyecto es obligatorio.');
                return;
            }

            if (isNullEmptyOrWhiteSpace(this.item.customerCode) || isNullEmptyOrWhiteSpace(this.item.customerName)) {
                this.message.info('El cliente es obligatorio.');
                return;
            }

            const center = this.centers.find((p) => p.code == this.center.value);

            if (center === null || center === undefined) {
                this.message.info('El centro logístico es obligatorio.');
                return;
            }

            const management = this.managements.find((p) => p.prcCode == this.management.value);

            if (management === null || management === undefined) {
                this.message.info('La gerencia seleccionada es obligatoria.');
                return;
            }

            const area = this.areas.find((p) => p.prcCode == this.area.value);

            if (area === null || area === undefined) {
                this.message.info('El área seleccionada es obligatoria.');
                return;
            }

            const movement = this.incomeExpenses.find((p) => p.prcCode == this.movement.value);

            if (movement === null || movement === undefined) {
                this.message.info('El ingreso/gasto seleccionado es obligatoria.');
                return;
            }

            this.item.centerCode = center.code;
            this.item.centerName = center.name;

            this.item.areaCode = area.prcCode;
            this.item.areaName = area.prcName;

            this.item.managementCode = management.prcCode;
            this.item.managementName = management.prcName;

            this.item.movementCode = movement.prcCode;
            this.item.movementName = movement.prcName;

            this.item.locationName = this.location.value;
        }

        if (this.disabled)
            return;

        this.disabled = true;

        const loading = await this.loader.show();

        if (this.id) {
            this.operationTaskServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado satisfactoriamente.');
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.operationTaskServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado satisfactoriamente.');
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private getAreas(): Promise<IntegrationCostCenter[]> {
        return new Promise<IntegrationCostCenter[]>((resolve, reject) => {
            this.integrationServiceProxy.getAllCostCenters().subscribe({
                next: (response) => resolve(response.items),
                error: () => reject()
            });
        });
    }

    private getCenters(): Promise<IntegrationServiceCallLogisticCenter[]> {
        return new Promise<IntegrationServiceCallLogisticCenter[]>((resolve, reject) => {
            this.integrationServiceProxy.getAllServiceCallLogisticCenters().subscribe({
                next: (response) => resolve(response.items),
                error: () => reject()
            });
        });
    }

    private getManagements(): Promise<IntegrationMasterManagementDto[]> {
        return new Promise<IntegrationMasterManagementDto[]>((resolve, reject) => {
            this.integrationServiceProxy.getAllMasterManagements().subscribe({
                next: (response) => resolve(response.items),
                error: () => reject()
            });
        });
    }

    private getIncomeExpenses(): Promise<IntegrationMasterIncomeExpenseDto[]> {
        return new Promise<IntegrationMasterIncomeExpenseDto[]>((resolve, reject) => {
            this.integrationServiceProxy.getAllMasterIncomeExpenses().subscribe({
                next: (response) => resolve(response.items),
                error: () => reject()
            });
        });
    }

    private getOperationTask(id: number): Promise<OperationTaskDto> {
        return new Promise<OperationTaskDto>((resolve, reject) => {
            if (id === undefined || id === null) {
                resolve(new OperationTaskDto().fromJS({}));
            } else {
                this.operationTaskServiceProxy
                    .get(id)
                    .subscribe({
                        next: (response) => resolve(response),
                        error: () => reject()
                    });
            }
        });
    }
}