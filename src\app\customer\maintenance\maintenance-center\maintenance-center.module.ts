import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceCenterRoutingModule } from './maintenance-center.routing.module';
import { MaintenanceCenterDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceCenterManagementComponent } from './management/management.component';
import { MaintenanceCenterDetailComponent } from './management/detail/detail.component';

@NgModule({
  imports: [
    MaintenanceCenterRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceCenterDashboardComponent,
    MaintenanceCenterManagementComponent,
    MaintenanceCenterDetailComponent
  ]
})
export class MaintenanceCenterModule { }
