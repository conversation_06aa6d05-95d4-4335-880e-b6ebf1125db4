import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { AlarmTreatmentReasonRoutingModule } from './alarm-reason.routing.module';
import { AlarmTreatmentReasonDashboardComponent } from './dashboard/dashboard.component';
import { AlarmTreatmentReasonCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    AlarmTreatmentReasonRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    AlarmTreatmentReasonDashboardComponent,
    AlarmTreatmentReasonCreateEditComponent
  ]
})
export class AlarmTreatmentReasonModule { }
