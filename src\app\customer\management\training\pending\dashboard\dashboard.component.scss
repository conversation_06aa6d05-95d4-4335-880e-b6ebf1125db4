.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.card-title {
    color: #495057;
    font-weight: 600;
}

.p-datatable {
    .p-datatable-header {
        background-color: #fff;
        border: none;
        padding: 1rem;
    }

    .p-datatable-thead > tr > th {
        background-color: #f8f9fa;
        border-color: #dee2e6;
        color: #495057;
        font-weight: 600;
        padding: 0.75rem;
    }

    .p-datatable-tbody > tr > td {
        padding: 0.75rem;
        border-color: #dee2e6;
    }

    .p-datatable-tbody > tr:hover {
        background-color: #f8f9fa;
    }
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.text-muted {
    color: #6c757d !important;
}

.fw-bold {
    font-weight: 600 !important;
}

ion-badge {
    --padding-start: 8px;
    --padding-end: 8px;
    --padding-top: 4px;
    --padding-bottom: 4px;
    font-size: 0.75rem;
}

.p-input-icon-left > input {
    padding-left: 2rem;
}

.p-input-icon-left > .pi {
    left: 0.75rem;
}

.container-fluid {
    padding: 1rem;
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .p-datatable {
        font-size: 0.875rem;
        
        .p-datatable-thead > tr > th,
        .p-datatable-tbody > tr > td {
            padding: 0.5rem;
        }
    }
}
