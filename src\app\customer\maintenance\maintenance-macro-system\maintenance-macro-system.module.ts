import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceMacroSystemRoutingModule } from './maintenance-macro-system.routing.module';
import { MaintenanceMacroSystemDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceMacroSystemCreateEditComponent } from './create-edit/create-edit.component';
import { MaintenanceMacroSystemManagementComponent } from './management/management.component';

@NgModule({
  imports: [
    MaintenanceMacroSystemRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceMacroSystemDashboardComponent,
    MaintenanceMacroSystemCreateEditComponent,
    MaintenanceMacroSystemManagementComponent
  ]
})
export class MaintenanceMacroSystemModule { }
