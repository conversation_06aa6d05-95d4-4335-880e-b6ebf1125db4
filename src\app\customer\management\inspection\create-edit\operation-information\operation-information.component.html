<div *ngIf="loading" class="row">
    <div class="col-12 text-center">
        <ion-spinner name="bubbles"></ion-spinner>
    </div>
</div>

<p-accordion *ngIf="inspectionProgram" [activeIndex]="activeIndex" [multiple]="true">
    <p-accordionTab header="Información general" class="bordered">
        <div class="row mx-0 pt-2">

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene certificado de ITSE?
                        </label>

                        <ion-badge [color]="inspectionProgram.hasCertificate ? 'success' : 'danger'">
                            {{inspectionProgram.hasCertificate ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.hasCertificate" class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Fe<PERSON> de vencimiento
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.certificateExpirationTime | luxonFormat: 'dd/MM/yyyy'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionCertificateResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionProgram.inspectionCertificateResources" [numVisible]="3" [numScroll]="1"
                    [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

            <div class="col-12">
                <hr class="mt-0 mb-2">
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene licencia municipal?
                        </label>

                        <ion-badge [color]="inspectionProgram.hasLicense ? 'success' : 'danger'">
                            {{inspectionProgram.hasLicense ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.hasLicense" class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label">
                            Fecha de vencimiento
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.licenseExpirationTime | luxonFormat: 'dd/MM/yyyy'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionLicenseResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionProgram.inspectionLicenseResources" [numVisible]="3" [numScroll]="1"
                    [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

            <div class="col-12">
                <hr class="mt-0 mb-2">
            </div>

        </div>
    </p-accordionTab>
    <p-accordionTab [header]="'Inspección eléctrica (' + inspectionProgram.inspectionElectrics.length + ')'"
        class="bordered">

        <div class="row mx-0 pt-2">

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label text-center fz-normal d-block">
                            Información de tableros
                        </label>

                    </div>
                </div>
            </div>

        </div>

        <div *ngFor="let inspectionElectric of inspectionProgram.inspectionElectrics; index as i;"
            class="row mx-0 pt-2">

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Tablero
                        </label>

                        <ion-badge color="tertiary">
                            {{i + 1}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Código
                        </label>

                        <span class="d-block">
                            {{inspectionElectric.code}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Temperatura
                        </label>

                        <span class="d-block">
                            {{inspectionElectric.temperature | number: '1.2-2' : 'en-US'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Amperaje
                        </label>

                        <span class="d-block">
                            {{inspectionElectric.amperage | number: '1.2-2' : 'en-US'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Circuitos están identificados?
                        </label>

                        <ion-badge [color]="inspectionElectric.hasIdentificator ? 'success' : 'danger'">
                            {{inspectionElectric.hasIdentificator ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene mandil de protección?
                        </label>

                        <ion-badge [color]="inspectionElectric.hasProtection ? 'success' : 'danger'">
                            {{inspectionElectric.hasProtection ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene conexión a tierra?
                        </label>

                        <ion-badge [color]="inspectionElectric.hasGroundConnection ? 'success' : 'danger'">
                            {{inspectionElectric.hasGroundConnection ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene diagrama unifilar o de circuitos?
                        </label>

                        <ion-badge [color]="inspectionElectric.hasDiagram ? 'success' : 'danger'">
                            {{inspectionElectric.hasDiagram ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene llaves diferencias?
                        </label>

                        <ion-badge [color]="inspectionElectric.hasDiferencial ? 'success' : 'danger'">
                            {{inspectionElectric.hasDiferencial ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra limpio?
                        </label>

                        <ion-badge [color]="inspectionElectric.hasCleanInterface ? 'success' : 'danger'">
                            {{inspectionElectric.hasCleanInterface ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Presenta conexiones irregulares?
                        </label>

                        <ion-badge [color]="inspectionElectric.hasIrregularConnection ? 'success' : 'danger'">
                            {{inspectionElectric.hasIrregularConnection ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionElectric.inspectionElectricResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionElectric.inspectionElectricResources" [numVisible]="3" [numScroll]="1"
                    [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionElectric.observation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="i > 0" class="col-12">
                <hr class="mt-0 mb-2">
            </div>

        </div>

    </p-accordionTab>
    <p-accordionTab header="Inspección estructural" class="bordered">

        <div class="row mx-0 pt-2">

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra visualmente conforme con <b>Gestión BSF</b>?
                        </label>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasFluvialNet ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Red fluvial
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasFluvialNet ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasFluvialNet ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasFluvialNet" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.fluvialNetObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasTallGate ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Portones
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasTallGate ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasTallGate ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasTallGate" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.tallGateObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasWallOrPillar ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Muros/Pilares
                        </label>

                        <ion-badge
                            [color]="inspectionProgram.inspectionStructure.hasWallOrPillar ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasWallOrPillar ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasWallOrPillar" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.wallOrPillarObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasFloor ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Pisos
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasFloor ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasFloor ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasFloor" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.floorObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasCeiling ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Techos/Cerchas
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasCeiling ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasCeiling ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasCeiling" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.ceilingObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

        </div>

        <div class="row mx-0 pt-2">

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra visualmente conforme con <b>Gestión Clientes</b>?
                        </label>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Racks
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasRack == null ? 'warning' : (inspectionProgram.inspectionStructure.hasRack  ? 'success' : 'danger')">
                            {{inspectionProgram.inspectionStructure.hasRack == null ? 'No Aplica' : (inspectionProgram.inspectionStructure.hasRack ? 'Si' : 'No')}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasRack" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.rackObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasBatteryBank ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Banco de baterías
                        </label>

                        <ion-badge
                            [color]="inspectionProgram.inspectionStructure.hasBatteryBank  == null ? 'warning' : (inspectionProgram.inspectionStructure.hasBatteryBank ? 'success' : 'danger')">
                            {{inspectionProgram.inspectionStructure.hasBatteryBank == null ? 'No Aplica' : (inspectionProgram.inspectionStructure.hasBatteryBank ? 'Si' : 'No') }}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasBatteryBank" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.batteryBankObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasSignaling ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Señalización
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasSignaling ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasSignaling ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasSignaling" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.signalingObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionStructure.inspectionBatteryBankResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionProgram.inspectionStructure.inspectionBatteryBankResources"
                    [numVisible]="3" [numScroll]="1" [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasAvailability ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Disponibilidad
                        </label>

                        <ion-badge
                            [color]="inspectionProgram.inspectionStructure.hasAvailability ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasAvailability ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasAvailability" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.availabilityObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasProtection ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Protección
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasProtection ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasProtection ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasProtection" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.protectionObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionStructure.hasAccess ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Acesso
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionStructure.hasAccess ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionStructure.hasAccess ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionStructure.hasAccess" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.accessObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionStructure.inspectionStructureResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionProgram.inspectionStructure.inspectionStructureResources"
                    [numVisible]="3" [numScroll]="1" [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionStructure.observation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

        </div>

    </p-accordionTab>
    <p-accordionTab header="Inspección carga de combustible" class="bordered">

        <div class="row mx-0 pt-2">

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Material almacenado
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionFuel.storageMaterial || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Material de embalaje
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionFuel.packingMaterial || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Material peligroso
                        </label>

                        <ion-badge
                            [color]="inspectionProgram.inspectionFuel.hasDangerousMaterial ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionFuel.hasDangerousMaterial ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionFuel.inspectionFuelResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionProgram.inspectionFuel.inspectionFuelResources" [numVisible]="3"
                    [numScroll]="1" [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

            <div *ngIf="inspectionProgram.inspectionFuel.hasDangerousMaterial" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionFuel.dangerousMaterialObservation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Porcentaje de ocupación del almacén
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionFuel.usePercentage | number : '1.2-2' : 'en-US'}}%
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionFuel.observation || 'Sin observaciones'}}
                        </span>

                    </div>
                </div>
            </div>

        </div>
    </p-accordionTab>
    <p-accordionTab
        [header]="'Gabinetes contra incendio (' + inspectionProgram.inspectionFire.inspectionFireProtections.length + ')'"
        class="bordered">

        <div class="row mx-0 pt-2">

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label text-center fz-normal d-block">
                            Información de gabinetes
                        </label>

                    </div>
                </div>
            </div>

        </div>

        <div *ngFor="let inspectionFireProtection of inspectionProgram.inspectionFire.inspectionFireProtections; index as i;"
            class="row mx-0 pt-2">

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Número de gabinete
                        </label>

                        <ion-badge color="tertiary">
                            {{i + 1}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Código de gabinete
                        </label>

                        <span class="d-block">
                            {{inspectionFireProtection.name}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra en buen estado?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasGoodCondition ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasGoodCondition ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Es accesible?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.isAccessible ? 'success' : 'danger'">
                            {{inspectionFireProtection.isAccessible ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Está señalizado?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.isSignalized ? 'success' : 'danger'">
                            {{inspectionFireProtection.isSignalized ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene guarda de seguridad?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasSecurity ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasSecurity ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Valvula angular 1 1/2 pulgada está operativo?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasValveOneAndOneMiddle ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasValveOneAndOneMiddle ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Valvula angular 2 1/2 pulgada está operativo?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasValveTwoAndOneMiddle ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasValveTwoAndOneMiddle ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Mangera 1 1/2 pulgada está operativa?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasHoseOneAndMiddle ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasHoseOneAndMiddle ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionFireProtection.hasHoseOneAndMiddle" class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Fecha de fabricación de manguera
                        </label>

                        <span class="d-block">
                            {{inspectionFireProtection.hoseOneAndMiddleTime | luxonFormat: 'dd/MM/yyyy'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Piton 1 1/2 pulgadas está operativa?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasPitonOneAndOneMiddle ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasPitonOneAndOneMiddle ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionFireProtection.hasPitonOneAndOneMiddle" class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene empaquetadura?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasPitonPackaging ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasPitonPackaging ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene llave F?
                        </label>

                        <ion-badge [color]="inspectionFireProtection.hasSpanner ? 'success' : 'danger'">
                            {{inspectionFireProtection.hasSpanner ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionFireProtection.observation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionFireProtection.inspectionFireProtectionResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionFireProtection.inspectionFireProtectionResources" [numVisible]="3"
                    [numScroll]="1" [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

            <div class="col-12">
                <hr class="mt-0 mb-2">
            </div>

        </div>

        <div class="row mx-0 pt-2">

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label text-center fz-normal d-block">
                            Información adicional
                        </label>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Extintores operativos?
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionFire.hasExtinguisher ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionFire.hasExtinguisher ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionFire.hasExtinguisher" class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Cantidad de extintores
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionFire.extinguisherQuantity | number: '1.0-0' : 'en-US'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionFire.hasExtinguisher" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionFire.extinguisherObservation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Luces de emergencia operativas?
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionFire.hasEmergencyLight ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionFire.hasEmergencyLight ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionFire.hasEmergencyLight" class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Cantidad de luces de emergencia
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionFire.emergencyLightQuantity | number: '1.0-0' : 'en-US'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionFire.hasEmergencyLight" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionFire.emergencyLightObservation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Señalización operativa?
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionFire.hasSignaling ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionFire.hasSignaling ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionFire.hasSignaling" class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Cantidad de luces de emergencia
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionFire.signalingQuantity | number: '1.0-0' : 'en-US'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionFire.hasSignaling" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionFire.signalingObservation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="!inspectionProgram.inspectionFire.hasSignaling" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionFire.signalingObservation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene brigada?
                        </label>

                        <ion-badge [color]="inspectionProgram.inspectionFire.hasBrigade ? 'success' : 'danger'">
                            {{inspectionProgram.inspectionFire.hasBrigade ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="{{inspectionProgram.inspectionFire.hasAlarm ? 'col-6' : 'col-12'}} mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Tiene pulsadores de alarma?
                        </label>

                        <ion-badge *ngIf="inspectionProgram.inspectionFire.hasAlarm == inspectionAlarmModes.true"
                            color="success">
                            Si
                        </ion-badge>
                        <ion-badge *ngIf="inspectionProgram.inspectionFire.hasAlarm == inspectionAlarmModes.false"
                            color="danger">
                            No
                        </ion-badge>
                        <ion-badge *ngIf="inspectionProgram.inspectionFire.hasAlarm == inspectionAlarmModes.undefined"
                            color="dark">
                            N/A
                        </ion-badge>
                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionFire.hasAlarm == inspectionAlarmModes.false" class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionFire.alarmObservation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

        </div>

    </p-accordionTab>
    <p-accordionTab header="Alarma y detección" class="bordered">
        <div class="row mx-0 pt-2">

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra operativo el detector de humo?
                        </label>

                        <ion-badge *ngIf="inspectionProgram.inspectionAlarm.hasSmokeSensor == inspectionAlarmModes.true"
                            color="success">
                            Si
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasSmokeSensor == inspectionAlarmModes.false"
                            color="danger">
                            No
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasSmokeSensor == inspectionAlarmModes.undefined"
                            color="dark">
                            N/A
                        </ion-badge>
                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra operativo el photobeam?
                        </label>

                        <ion-badge *ngIf="inspectionProgram.inspectionAlarm.hasPhotoBean == inspectionAlarmModes.true"
                            color="success">
                            Si
                        </ion-badge>
                        <ion-badge *ngIf="inspectionProgram.inspectionAlarm.hasPhotoBean == inspectionAlarmModes.false"
                            color="danger">
                            No
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasPhotoBean == inspectionAlarmModes.undefined"
                            color="dark">
                            N/A
                        </ion-badge>
                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra operativos los sensores magnéticos?
                        </label>

                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasMagneticSensor == inspectionAlarmModes.true"
                            color="success">
                            Si
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasMagneticSensor == inspectionAlarmModes.false"
                            color="danger">
                            No
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasMagneticSensor == inspectionAlarmModes.undefined"
                            color="dark">
                            N/A
                        </ion-badge>
                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra operativos los sensores de movimiento?
                        </label>

                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasMovementSensor == inspectionAlarmModes.true"
                            color="success">
                            Si
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasMovementSensor == inspectionAlarmModes.false"
                            color="danger">
                            No
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasMovementSensor == inspectionAlarmModes.undefined"
                            color="dark">
                            N/A
                        </ion-badge>
                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Se encuentra operativos la estación de A&D?
                        </label>

                        <ion-badge *ngIf="inspectionProgram.inspectionAlarm.hasStation == inspectionAlarmModes.true"
                            color="success">
                            Si
                        </ion-badge>
                        <ion-badge *ngIf="inspectionProgram.inspectionAlarm.hasStation == inspectionAlarmModes.false"
                            color="danger">
                            No
                        </ion-badge>
                        <ion-badge
                            *ngIf="inspectionProgram.inspectionAlarm.hasStation == inspectionAlarmModes.undefined"
                            color="dark">
                            N/A
                        </ion-badge>
                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.inspectionAlarm.observation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div *ngIf="inspectionProgram.inspectionAlarm.inspectionAlarmResources.length > 0" class="col-12">

                <p-carousel [value]="inspectionProgram.inspectionAlarm.inspectionAlarmResources" [numVisible]="3"
                    [numScroll]="1" [circular]="false">
                    <ng-template let-resource pTemplate="item">
                        <div class="inspection-resource">
                            <img (click)="showResource(resource.path)" class="inspection-resource__image"
                                [src]="resource.path" [alt]="resource.name" aria-label="Image Resource" />
                        </div>
                    </ng-template>
                </p-carousel>

            </div>

        </div>
    </p-accordionTab>
    <p-accordionTab header="Datos del cliente" class="bordered">
        <div class="row mx-0 pt-2">

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Nombre
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionCustomer.name}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            DNI
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionCustomer.document}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Cargo
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionCustomer.job}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Teléfono
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionCustomer.phoneNumber}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Correo electrónico
                        </label>

                        <span class="d-block">
                            {{inspectionProgram.inspectionCustomer.emailAddress}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Documento del cliente
                        </label>

                        <div class="inspection-signature pt-2">

                            <div class="inspection-resource d-flex flex-column me-2">

                                <img (click)="showResource(inspectionProgram?.inspectionCustomer?.inspectionCustomerFrontDocumentResource?.path)"
                                    class="inspection-resource__image"
                                    [src]="inspectionProgram?.inspectionCustomer?.inspectionCustomerFrontDocumentResource?.path"
                                    [alt]="inspectionProgram?.inspectionCustomer?.inspectionCustomerFrontDocumentResource?.name"
                                    aria-label="Image Resource" />

                                <label class="d-block text-center">
                                    Frontal
                                </label>

                            </div>

                            <div class="inspection-resource d-flex flex-column">

                                <img (click)="showResource(inspectionProgram?.inspectionCustomer?.inspectionCustomerBackDocumentResource?.path)"
                                    class="inspection-resource__image"
                                    [src]="inspectionProgram?.inspectionCustomer?.inspectionCustomerBackDocumentResource?.path"
                                    [alt]="inspectionProgram?.inspectionCustomer?.inspectionCustomerBackDocumentResource?.name"
                                    aria-label="Image Resource" />

                                <label class="d-block text-center">
                                    Posterior
                                </label>

                            </div>

                        </div>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Firma del cliente
                        </label>

                        <div class="inspection-signature inspection-resource">
                            <img (click)="showResource(inspectionProgram?.inspectionCustomer?.inspectionCustomerSignatureResource?.path)"
                                [src]="inspectionProgram?.inspectionCustomer?.inspectionCustomerSignatureResource?.path"
                                class="inspection-resource__image" aria-label="Inspector Signature">
                        </div>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Firma del inspector
                        </label>

                        <div class="inspection-signature inspection-resource">
                            <img (click)="showResource(inspectionProgram?.inspectionCustomer?.inspectionInspectorSignatureResource?.path)"
                                [src]="inspectionProgram?.inspectionCustomer?.inspectionInspectorSignatureResource?.path"
                                class="inspection-resource__image" aria-label="Inspector Signature">
                        </div>

                    </div>
                </div>
            </div>

        </div>
    </p-accordionTab>
    <p-accordionTab header="Análisis" class="bordered">
        <div class="row mx-0 pt-2">

            <div class="col-6 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            ¿Existe posibilidad de riesgo?
                        </label>

                        <ion-badge [color]="inspectionProgram.hasRisk ? 'success' : 'danger'">
                            {{inspectionProgram.hasRisk ? 'Si' : 'No'}}
                        </ion-badge>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.riskObservation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

            <div class="col-12 mb-2">
                <div class="ion-input">
                    <div class="input-control">

                        <label class="form-label d-block">
                            Observaciones generales
                        </label>

                        <span class="col-12">
                            {{inspectionProgram.observation || 'Sin información'}}
                        </span>

                    </div>
                </div>
            </div>

        </div>
    </p-accordionTab>
</p-accordion>