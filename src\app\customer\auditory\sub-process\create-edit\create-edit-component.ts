import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { ProcessDto, ProcessServiceProxy } from '@proxies/auditory/process.proxy';
import { SubProcessServiceProxy, SubProcessDto } from '@proxies/auditory/sub-process.proxy'
import { AppAuditoryFindEmployeePositionComponent } from '@components/auditory/find-position/find-employee-position.component'
import { IntegrationEmployeePositionDto } from '@proxies/integration.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-create-edit-document',
    templateUrl: 'create-edit-component.html',
    styleUrls: [
        'create-edit-component.scss'
    ]
})

export class SubProcessCreateEditComponent extends ViewComponent implements OnInit {

    @Input() id: number;

    private subProcessServiceProxy: SubProcessServiceProxy;
    private processServiceProxy: ProcessServiceProxy;
    private formBuilder: FormBuilder;

    item: SubProcessDto = new SubProcessDto();
    process: ProcessDto[] = [];
    disabled: boolean = false;
    loaded: boolean = false;

    modalForm!: FormGroup;

    get subProcessCode(): AbstractControl {
        return this.modalForm.controls['subProcessCodeInput'];
    };

    get subProcessName(): AbstractControl {
        return this.modalForm.controls['subProcessNameInput'];
    };

    get subProcessProcess(): AbstractControl {
        return this.modalForm.controls['subProcessProcessSelect'];
    };

    get subProcessCreator(): AbstractControl {
        return this.modalForm.controls['subProcessCreatorInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.processServiceProxy = _injector.get(ProcessServiceProxy);
        this.subProcessServiceProxy = _injector.get(SubProcessServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            subProcessCodeInput: ['', Validators.compose([Validators.required])],
            subProcessNameInput: ['', Validators.compose([Validators.required])],
            subProcessProcessSelect: ['-1', Validators.compose([Validators.required])],
            subProcessCreatorInput: [''],
        });
    }

    async ngOnInit(): Promise<void> {
        this.disabled = true;
        const loading = await this.loader.show();
        await this.loadInputs();

        if (this.id) {
            this.subProcessServiceProxy
                .get(this.id)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: (response) => {
                        this.item = response;
                        this.subProcessName.setValue(this.item.name);
                        this.subProcessCode.setValue(this.item.code);
                        this.subProcessProcess.setValue(this.item.processid);
                        this.subProcessCreator.setValue(this.item.creator);

                        this.loaded = true;
                    },
                    error: async () => await this.dialog.dismiss()
                });
        }
        else {
            await loading.dismiss();
            this.disabled = false;
        }
    }

    async save(): Promise<void> {
        this.item.code = this.subProcessCode.value;
        this.item.name = this.subProcessName.value;
        this.item.processid = this.subProcessProcess.value;
        this.item.creator = this.subProcessCreator.value;

        if (!this.item.code) {
            this.message.info('El código es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.name) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (!this.item.processid) {
            this.message.info('El proceso es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.subProcessServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Subproceso actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.subProcessServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Subproceso creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private loadInputs(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.processServiceProxy
                .getAllFilter()
                .subscribe({
                    next: (response) => {
                        this.process = response.items
                        resolve();

                    }, error: () => reject()
                });
        });
    }

    clearCreator(): void {
        this.subProcessCreator.setValue('');
    }

    showFindPosition(): void {
            this.dialog.showWithData<IntegrationEmployeePositionDto>({
                component: AppAuditoryFindEmployeePositionComponent
            }).then((response) => {
                if (response.data.result) {
                    let obj = new IntegrationEmployeePositionDto().fromJS(response.data.result);
                    this.subProcessCreator.setValue(obj.name);
                }
            });
        }
}