<!-- Loading indicator -->
<div *ngIf="isLoading" class="col-12 text-center mb-4">
  <ion-spinner name="crescent"></ion-spinner>
  <p class="mt-2">Cargando resumen de asistencias...</p>
</div>

<div class="row" [style.opacity]="isLoading ? '0.5' : '1'">
  <!-- Estadísticas generales -->
  <div class="col-12 mb-3">
    <div class="d-flex justify-content-between align-items-center">
      <label class="fz-normal fw-bold text-dark mb-0">
        Resumen de Asistencias
      </label>
      <div class="d-flex gap-2">
        <ion-button
          (click)="SendEmailAsistantAll()"
          color="secondary"
          fill="outline"
          size="small"
        >
          <ion-icon slot="start" name="mail" ></ion-icon>
          Enviar recordatorio Asistencia
        </ion-button>
        <ion-button
          (click)="SendEmailEvaluacionAll()"
          color="secondary"
          fill="outline"
          size="small"
        >
          <ion-icon slot="start" name="mail" ></ion-icon>
          Enviar recordatorio Evaluación
        </ion-button>
        <ion-button
          (click)="refreshData()"
          color="primary"
          fill="outline"
          size="small"
        >
          <ion-icon slot="start" name="refresh"></ion-icon>
          Actualizar
        </ion-button>
      </div>
    </div>
    <hr />
  </div>

  <!-- Cards de estadísticas -->
  <div class="col-lg-3 col-md-6 col-12 mb-3">
    <div class="stats-card">
      <div class="stats-icon total">
        <ion-icon name="people"></ion-icon>
      </div>
      <div class="stats-content">
        <h3>{{ totalAttendees }}</h3>
        <p>Total Inscritos</p>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6 col-12 mb-3">
    <div class="stats-card">
      <div class="stats-icon attendance">
        <ion-icon name="checkmark-circle"></ion-icon>
      </div>
      <div class="stats-content">
        <h3>{{ attendedCount }}</h3>
        <p>Asistieron ({{ getAttendancePercentage() }}%)</p>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6 col-12 mb-3">
    <div class="stats-card">
      <div class="stats-icon video">
        <ion-icon name="play-circle"></ion-icon>
      </div>
      <div class="stats-content">
        <h3>{{ watchedVideoCount }}</h3>
        <p>Vieron Video ({{ getVideoWatchedPercentage() }}%)</p>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6 col-12 mb-3">
    <div class="stats-card">
      <div class="stats-icon evaluation">
        <ion-icon name="document-text"></ion-icon>
      </div>
      <div class="stats-content">
        <h3>{{ completedEvaluationCount }}</h3>
        <p>Evaluaciones ({{ getEvaluationCompletedPercentage() }}%)</p>
      </div>
    </div>
  </div>

 

  <!-- Lista de asistentes -->
  <div class="col-12 mb-3">
    <label class="fz-normal fw-bold text-dark mb-0">
      Detalle de Asistentes
    </label>
    <hr />
  </div>

  <div class="col-12" *ngIf="attendees.length > 0">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>Nombre</th>
            <th>Email</th>
            <th>Tipo Asistencia</th>
            <th>Asistió</th>
            <th>Vio Video</th>
            <th>Evaluación</th>
            <th>Calificación</th>
            <th>Recordatorio</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let attendee of attendees">
            <td>
              <strong>{{ attendee.name }}</strong>
            </td>
            <td>
              <small class="text-muted">{{ attendee.emailAddress }}</small>
            </td>
            <td>
              <span class="badge badge-info">{{
                attendee.attendanceType
              }}</span>
            </td>
            <td>
              <span
                class="badge"
                [ngClass]="getAttendanceStatusClass(attendee.attended)"
              >
                {{ attendee.attended}}
              </span>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <span
                  class="badge"
                  [ngClass]="getAttendanceStatusClass(attendee.watchedVideo)"
                >
                  {{ attendee.watchedVideo }}
                </span>
                <small
                  *ngIf="attendee.watchedVideoTime"
                  class="ms-2 text-muted"
                >
                  {{ attendee.watchedVideoTime.toFormat("dd/MM/yyyy HH:mm") }}
                </small>
              </div>
            </td>
            <td>
              <span
                class="badge"
                [ngClass]="
                  getEvaluationStatusClass(attendee.completedEvaluation)
                "
              >
                {{ attendee.completedEvaluation }}
              </span>
            </td>
            <td>
              <span
                 
                class="score-badge"
                [ngClass]="getScoreClass(attendee.evaluationScore)"
              >
                {{ attendee.evaluationScore }}/20
              </span>
               
            </td>
            <td>
              <ion-button
                *ngIf="
                  !(
                    attendee.attendanceType === 'No Aplica' ||
                    (attendee.watchedVideo === 'Sí' &&
                      attendee.completedEvaluation === 'Sí')
                  )
                "
                (click)="!(attendee.attended === 'Sí') ? SendEmailAsistant(attendee) : SendEmailEvaluacion(attendee)"
                color="secondary"
                fill="outline"
                size="small"
              >
                <ion-icon slot="start" name="mail"></ion-icon>
                Enviar
              </ion-button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Mensaje cuando no hay datos -->
  <div class="col-12 text-center" *ngIf="!isLoading && attendees.length === 0">
    <div class="empty-state">
      <ion-icon name="people-outline" class="empty-icon"></ion-icon>
      <h4>No hay asistentes registrados</h4>
      <p class="text-muted">
        Aún no se han registrado asistentes para esta capacitación.
      </p>
    </div>
  </div>
</div>
