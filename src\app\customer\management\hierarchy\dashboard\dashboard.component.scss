.node-label {
    font-size: 14px;
}

.node-statistics {
    font-size: 12px;
    opacity: 0.5;
}

.node-status {
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 5px;
    margin-left: 5px;
    margin-right: 5px;

    &.enabled {
        background: var(--ion-color-secondary);
        color: var(--ion-color-secondary-contrast);
    }

    &.disabled {
        background: var(--ion-color-danger);
        color: var(--ion-color-danger-contrast);
    }
}

.hierarchy-content {
    min-width: 1080px;
}