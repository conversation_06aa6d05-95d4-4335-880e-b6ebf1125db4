import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { InspectionRoutingModule } from './inspection.routing.module';
import { InspectionDashboardComponent } from './dashboard/dashboard.component';
import { InspectionCreateEditComponent } from './create-edit/create-edit.component';
import { ComponentModule } from '@components/component.module';
import { InspectionGeneralInformationComponent } from './create-edit/general-information/general-information.component';
import { InspectionModifiedInformationComponent } from './create-edit/modified-information/modified-information.component';
import { InspectionShowModifiedInformationComponent } from './create-edit/modified-information/show-modified-information/show-modified-information.component';
import { InspectionOperationInformationComponent } from './create-edit/operation-information/operation-information.component';
import { InspectionImportComponent } from './import/import.component';
import { InspectionHistoryInformationComponent } from './create-edit/history-information/history-information.component';
import { InspectionChangeInspectionTimeComponent } from './change-inspection-time/change-inspection-time.component';
import { InspectionRejectComponent } from './reject-inspection/reject-inspection.component';
import { InspectionCancelComponent } from './cancel-inspection/cancel-inspection.component';
import { InspectionServiceCallInformationComponent } from './create-edit/general-information/service-call-information/service-call-information.component';

@NgModule({
  imports: [
    InspectionRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule,
    ComponentModule
  ],
  declarations: [
    InspectionDashboardComponent,
    InspectionCreateEditComponent,
    InspectionGeneralInformationComponent,
    InspectionModifiedInformationComponent,
    InspectionShowModifiedInformationComponent,
    InspectionChangeInspectionTimeComponent,
    InspectionHistoryInformationComponent,
    InspectionOperationInformationComponent,
    InspectionServiceCallInformationComponent,
    InspectionRejectComponent,
    InspectionImportComponent,
    InspectionCancelComponent
  ]
})
export class InspectionModule { }