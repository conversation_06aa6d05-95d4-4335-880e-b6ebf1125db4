<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Plantillas
        </ion-title>

        <ion-buttons slot="end">
        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Administration.Template.Modify'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th style="min-width: 350px; width: 350px; max-width: 350px;">
                                    Descripción
                                </th>
                                <th style="min-width: 200px; width: 200px; max-width: 200px;">
                                    Llave
                                </th>
                                <th style="min-width: 350px;">
                                    Valor
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px"
                                    [hidden]="
                                !(
                                    [
                                        'Pages.Administration.Template.Modify'
                                    ] | permissionAny
                                )
                                ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 350px; width: 350px; max-width: 350px;">
                                    {{record.name}}
                                </td>
                                <td style="min-width: 200px; width: 200px; max-width: 200px;">
                                    {{record.id}}
                                </td>
                                <td style="min-width: 350px;">
                                    {{record.value}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>