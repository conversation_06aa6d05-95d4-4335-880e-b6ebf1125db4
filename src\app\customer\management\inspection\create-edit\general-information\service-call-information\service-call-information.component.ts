import { Component, Input, OnInit } from '@angular/core';
import { PrimengTable } from '@core/models/primeng-table';
import { InspectionDto } from '@proxies/inspection.proxy';
import { DateTime } from 'luxon';

interface IServiceCallItemDto {
    type: string;
    hasCall: boolean;
    executed: boolean;
    executionTime: DateTime;
    observation: string;
}

@Component({
    selector: 'app-inspection-service-call-information',
    templateUrl: 'service-call-information.component.html',
    styleUrls: [
        'service-call-information.component.scss'
    ]
})
export class InspectionServiceCallInformationComponent implements OnInit {

    @Input() inspection!: InspectionDto;
    @Input() completed!: boolean;

    table: PrimengTable;

    constructor() {
        this.table = new PrimengTable();
        this.table.records = [];
        this.table.totalRecordsCount = 0;
    }

    ngOnInit(): void {
        this.table.records.push(<IServiceCallItemDto>{
            type: 'INS. ELÉCTRICA',
            hasCall: this.inspection.hasElectricInspection,
            executed: this.inspection.electricInspectionSended,
            executionTime: this.inspection.electricInspectionSendedTime,
            observation: this.inspection.electricInspectionDescription
        });

        this.table.records.push(<IServiceCallItemDto>{
            type: 'INS. CARGA DE COMBUSTIBLE',
            hasCall: this.inspection.hasFuelInspection,
            executed: this.inspection.fuelInspectionSended,
            executionTime: this.inspection.fuelInspectionSendedTime,
            observation: this.inspection.fuelInspectionDescription
        });

        this.table.records.push(<IServiceCallItemDto>{
            type: 'INS. ALARMAS Y DETECCIÓN',
            hasCall: this.inspection.hasAlarmInspection,
            executed: this.inspection.alarmInspectionSended,
            executionTime: this.inspection.alarmInspectionSendedTime,
            observation: this.inspection.alarmInspectionDescription
        });

        this.table.records.push(<IServiceCallItemDto>{
            type: 'INS. GABINETES CONTRA INCENDIO',
            hasCall: this.inspection.hasFireInspection,
            executed: this.inspection.fireInspectionSended,
            executionTime: this.inspection.fireInspectionSendedTime,
            observation: this.inspection.fireInspectionDescription
        });

        this.table.totalRecordsCount = this.table.records.length;
    }
}