import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { TrainingModeDto, TrainingModeServiceProxy } from '@proxies/training-mode.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-training-mode-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class TrainingModeCreateEditComponent extends ViewComponent implements OnInit {

    private trainingModeServiceProxy: TrainingModeServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: TrainingModeDto = new TrainingModeDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get trainingModeName(): AbstractControl {
        return this.modalForm.controls['trainingModeNameInput'];
    };

    get trainingModeEnabled(): AbstractControl {
        return this.modalForm.controls['trainingModeEnabledSelect'];
    };

    get trainingModeCapacityRequired(): AbstractControl {
        return this.modalForm.controls['trainingModeCapacityRequiredSelect'];
    };

    get trainingModeGenerateCertificate(): AbstractControl {
        return this.modalForm.controls['trainingModeGenerateCertificateSelect'];
    };

    get trainingModeCode(): AbstractControl {
        return this.modalForm.controls['trainingModeCodeInput'];
    };
    
    constructor(_injector: Injector) {
        super(_injector);

        this.trainingModeServiceProxy = _injector.get(TrainingModeServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            trainingModeNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            trainingModeEnabledSelect: ['false', [Validators.required]],
            trainingModeCapacityRequiredSelect: ['false', [Validators.required]],
            trainingModeGenerateCertificateSelect: ['false', [Validators.required]],
            trainingModeCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.trainingModeServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.trainingModeName.setValue(this.item.name);
                        this.trainingModeEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.trainingModeCapacityRequired.setValue(this.item.capacityRequired ? 'true' : 'false');
                        this.trainingModeGenerateCertificate.setValue(this.item.generateCertificate ? 'true' : 'false');
                        this.trainingModeCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new TrainingModeDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.trainingModeName.value;
        this.item.enabled = this.trainingModeEnabled.value == 'true';
        this.item.capacityRequired = this.trainingModeCapacityRequired.value == 'true';
        this.item.generateCertificate = this.trainingModeGenerateCertificate.value == 'true';
        this.item.code = this.trainingModeCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de capacitación dirigida a es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.trainingModeServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Capacitación dirigida a actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.trainingModeServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Capacitación dirigida a creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}