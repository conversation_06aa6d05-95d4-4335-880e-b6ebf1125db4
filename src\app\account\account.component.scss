.account__container {
    height: 100%;
    width: 100%;
    position: relative;
    background: var(--ion-color-light);

    .account__content {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-bottom: -10%;
        position: relative;

        .account__waves {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 90%;
            background-image: url('/assets/icons/waves.svg');
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            transform: scaleX(-1);
            z-index: 1;
        }

        .account__icon {
            padding-bottom: 2rem;
            z-index: 2;
            display: flex;
            justify-content: center;
        }

        .account__form {
            padding: 2rem;
            border-radius: 5px;
            background: #ffffff;
            position: relative;
            box-shadow: rgba(0, 0, 0, 0.12) 0px 4px 16px;
            z-index: 2;
        }
    }
}

@media (min-width: 992px) {
    .account__form {
        min-width: 460px;
        max-width: 460px;
    }
}

@media (max-width: 991px) {
    .account__form {
        width: 90%;
        max-width: 460px;
    }
}
