import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MacroProcessRoutingModule } from './macro-process.routing.module';
import { MacroProcessDashboardComponent } from './dashboard/dashboard.component';
import { MacroProcessCreateEditComponent } from './create-edit/create-edit-component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';

@NgModule({
  imports: [
    MacroProcessRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    MacroProcessDashboardComponent,
    MacroProcessCreateEditComponent
  ]
})
export class MacroProcessModule { }