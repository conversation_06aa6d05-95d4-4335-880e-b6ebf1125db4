<ion-header mode="md">
    <ion-toolbar>

        <ion-buttons slot="start">
            <ion-button (click)="onBackButtonPressed()">
                <ion-icon slot="icon-only" name="chevron-back"></ion-icon>
            </ion-button>
        </ion-buttons>

        <ion-title class="fz-normal fw-bold">
            Importación de inspecciones
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="downloadTemplate()" class="ion-option me-2" color="success" fill="solid">
                <ion-icon class="me-1" name="cloud-download"></ion-icon>
                <ion-label class="d-none d-md-inline-block">
                    Descargar plantilla
                </ion-label>
            </ion-button>

            <ion-button (click)="save()" [disabled]="saving || totalRecordsCount == 0" class="ion-option"
                color="primary" fill="solid">
                <ion-label class="d-none d-md-inline-block">
                    Guardar cambios
                </ion-label>
                <ion-icon class="ms-1" name="save"></ion-icon>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <div class="row">

                <div class="col-12">
                    <ion-label class="fz-small" color="primary">
                        Paso 1. Seleccionar un archivo para importar
                    </ion-label>

                    <hr class="ion-divider-primary">
                </div>

                <div class="col-12 mt-3 mb-4">
                    <app-file-uploader (onUploadItem)="onFileSelected($event)" [images]="false" [videos]="false"
                        [files]="true" />

                    <span class="d-block form-text text-muted fz-note">
                        Ud. puede seleccionar un archivo excel (xlsx) con un tamaño máximo de 5MB.
                    </span>
                </div>

                <div *ngIf="resource" class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Recurso pendiente de publicación
                    </label>
                    <hr>
                </div>

                <div *ngIf="resource" class="col-12">
                    <app-file-preview [resource]="resource" [type]="resource.type" (onRemove)="onRemoveResource()">
                    </app-file-preview>
                </div>

                <div class="col-12 text-end">
                    <ion-button (click)="import()" [disabled]="!resource" size="normal" color="secondary" fill="solid">
                        <ion-icon class="me-1" name="cloud-upload"></ion-icon>
                        <ion-label class="py-1">
                            Cargar datos
                        </ion-label>
                    </ion-button>
                </div>

                <div class="col-12">
                    <ion-label class="fz-small" color="primary">
                        Paso 2. Revisa los datos identificados antes de guardar los cambios
                    </ion-label>

                    <hr class="ion-divider-primary">
                </div>

                <div class="col-12 mt-3">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 200px">
                                    Almacén
                                </th>
                                <th style="min-width: 200px">
                                    Fecha y hora
                                </th>
                                <th style="min-width: 200px">
                                    Tipo de inspección
                                </th>
                                <th style="min-width: 150px">
                                    Tipo Documento
                                </th>
                                <th style="min-width: 150px">
                                    Nº Documento
                                </th>
                                <th style="min-width: 150px">
                                    Nombres
                                </th>
                                <th style="min-width: 150px">
                                    Apellido Paterno
                                </th>
                                <th style="min-width: 150px">
                                    Apellido Materno
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr [hidden]="record.isHidden">
                                <td style="min-width: 200px">
                                    {{record.property ? record.property.code : ''}}
                                </td>
                                <td style="min-width: 200px">
                                    {{record.inspectionTime | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                                <td style="min-width: 200px">
                                    {{record.inspectionType ? record.inspectionType.name : ''}}
                                </td>
                                <td style="min-width: 150px">
                                    {{record.documentType ? record.documentType.name : ''}}
                                </td>
                                <td style="min-width: 150px">
                                    {{record.document}}
                                </td>
                                <td style="min-width: 150px">
                                    {{record.assignedUser ? record.assignedUser.name : ''}}
                                </td>
                                <td style="min-width: 150px">
                                    {{record.assignedUser ? record.assignedUser.surname : ''}}
                                </td>
                                <td style="min-width: 150px">
                                    {{record.assignedUser ? record.assignedUser.secondSurname : ''}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="totalRecordsCount == 0">
                        Sin información cargada
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Total: {{totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </div>
            </div>
        </ion-card-content>
    </ion-card>
</ion-content>