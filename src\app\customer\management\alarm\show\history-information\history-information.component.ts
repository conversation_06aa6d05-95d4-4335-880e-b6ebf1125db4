import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AlarmEventDto, AlarmTreatmentHistoryType } from '@proxies/alarm-proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';

@Component({
    selector: 'app-alarm-history-information',
    templateUrl: 'history-information.component.html',
    styleUrls: [
        'history-information.component.scss'
    ]
})
export class AlarmHistoryInformationComponent extends ViewComponent implements OnInit {

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    @Input() alarm: AlarmEventDto;

    alarmTreatmentHistoryTypes = {
        create: AlarmTreatmentHistoryType.Create,
        change: AlarmTreatmentHistoryType.Change,
        complete: AlarmTreatmentHistoryType.Complete,
        canceled: AlarmTreatmentHistoryType.Canceled,
        manual: AlarmTreatmentHistoryType.Manual
    }

    private skipCount: number;
    private maxResultCount: number;

    constructor(_injector: Injector) {
        super(_injector);

        this.skipCount = 0;
        this.table.defaultRecordsCountPerPage = 5;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;
    }

    ngOnInit(): void {
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        for (let item of this.alarm.treatment.alarmTreatmentHistories) {
            item.isHidden = true;
            if (index >= skipCount && result < maxResultCount) {
                item.isHidden = false;
                result++;
            }

            index++;
        }

        this.table.totalRecordsCount = index;
    }
}