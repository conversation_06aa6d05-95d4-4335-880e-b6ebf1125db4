import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AppFileUploadProgressComponent } from '@components/file-upload-progress/file-upload-progress.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IUploadProgressRespose } from '@core/models/app-config';
import { UploadResource } from '@core/utils/core.request';
import { MonitoringAlarmStatus, MonitoringAlarmTreatmentReasonDto, MonitoringAlarmTreatmentReasonType, MonitoringDto, MonitoringServiceProxy, MonitoringUpdateDto } from '@proxies/monitoring.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-monitoring-edit',
    templateUrl: 'edit.component.html',
    styleUrls: [
        'edit.component.scss'
    ]
})
export class MonitoringEditComponent extends ViewComponent implements OnInit {

    private readonly monitoringServiceProxy: MonitoringServiceProxy;
    private readonly formBuilder: FormBuilder;

    @Input() id!: number;

    loaded!: boolean;
    disabled!: boolean;
    status!: MonitoringAlarmStatus;
    modalForm!: FormGroup;
    size: number = 15_728_640;

    item: MonitoringDto;
    uploadResources: UploadResource[];

    monitoringAlarmStatuses = {
        pending: MonitoringAlarmStatus.Pending,
        process: MonitoringAlarmStatus.Process,
        completed: MonitoringAlarmStatus.Completed
    };

    alarmTreatmentReasons!: MonitoringAlarmTreatmentReasonDto[];

    get monitoringAlarmId(): AbstractControl {
        return this.modalForm.controls['monitoringAlarmIdInput'];
    };

    get monitoringAlarmType(): AbstractControl {
        return this.modalForm.controls['monitoringAlarmTypeInput'];
    };

    get monitoringAlarmIsReal(): AbstractControl {
        return this.modalForm.controls['monitoringAlarmIsRealSelect'];
    };

    get monitoringAlarmTreatmentReason(): AbstractControl {
        return this.modalForm.controls['monitoringAlarmTreatmentReasonSelect'];
    };

    get monitoringDescription(): AbstractControl {
        return this.modalForm.controls['monitoringDescriptionInput'];
    };

    constructor(injector: Injector) {
        super(injector);

        this.monitoringServiceProxy = injector.get(MonitoringServiceProxy);
        this.formBuilder = injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            monitoringAlarmIdInput: [''],
            monitoringAlarmTypeInput: [''],
            monitoringAlarmIsRealSelect: [true],
            monitoringDescriptionInput: ['', Validators.compose([Validators.maxLength(25000)])],
            monitoringAlarmTreatmentReasonSelect: ['-1']
        });
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.monitoringServiceProxy
            .get(this.id)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (response) => {
                    this.item = response;

                    this.monitoringAlarmId.setValue(`${this.item.alarmId}`);
                    this.monitoringAlarmType.setValue(this.item.alarmTreatmentType ? this.item.alarmTreatmentType.name : '');

                    if (this.item.status == this.monitoringAlarmStatuses.process) {
                        this.monitoringAlarmIsReal.setValue(this.item.isReal ? true : false);
                        this.monitoringAlarmTreatmentReason.setValue(this.item.alarmTreatmentReason ? this.item.alarmTreatmentReason.id : '-1');
                        this.monitoringDescription.setValue(this.item.description);

                        this.alarmTreatmentReasons = this.item.alarmTreatmentReasons.filter(p =>
                            p.type == MonitoringAlarmTreatmentReasonType.All ||
                            p.type == (
                                this.item.isReal ?
                                    MonitoringAlarmTreatmentReasonType.True :
                                    MonitoringAlarmTreatmentReasonType.False
                            )
                        );
                    } else {
                        this.alarmTreatmentReasons = this.item.alarmTreatmentReasons.filter(p =>
                            p.type == MonitoringAlarmTreatmentReasonType.All ||
                            p.type == MonitoringAlarmTreatmentReasonType.True
                        );
                    }

                    this.loaded = true;
                },
                error: () => this.dialog.dismiss()
            });
    }

    onMonitoringIsRealChange(event: any): void {
        const checked: boolean = event.detail.checked;

        this.alarmTreatmentReasons = this.item.alarmTreatmentReasons.filter(p =>
            p.type == MonitoringAlarmTreatmentReasonType.All ||
            p.type == (
                checked ?
                    MonitoringAlarmTreatmentReasonType.True :
                    MonitoringAlarmTreatmentReasonType.False
            )
        );

        this.monitoringAlarmTreatmentReason.setValue('-1');
    }

    onUploadItem(event: UploadResource): void {
        this.uploadResources ??= [];
        this.uploadResources.push(event);
    }

    onRemoveResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.item.alarmTreatmentResources[index].remove = true;
            }
        });
    }

    onRemoveUploadResource(index: number): void {
        this.message.confirm('¿Está seguro de eliminar el recurso seleccionado?', 'Aviso', (confirmation) => {
            if (confirmation) {
                this.uploadResources ??= [];
                this.uploadResources.splice(index, 1);
            }
        });
    }

    async save(): Promise<void> {
        if (this.status == MonitoringAlarmStatus.Completed) {
            this.message.confirm('¿Está seguro de finalizar el tratamiento de la alarma actual?', 'Aviso', async (confirmation) => {
                if (confirmation) 
                    await this.complete();                
            });
        } else {
            await this.complete();
        }
    }

    private async complete(): Promise<void> {
        const hasReason: boolean = this.item.alarmTreatmentType.hasReal && this.item.alarmTreatmentType.hasReason;

        const alarmTreatmentReason: MonitoringAlarmTreatmentReasonDto = hasReason ?
            this.item.alarmTreatmentReasons.find(p => p.id == this.monitoringAlarmTreatmentReason.value) :
            undefined;

        if (hasReason && !alarmTreatmentReason) {
            this.message.info('La razón el obligatoria. Por favor verifique la información antes de continuar.', 'Aviso');
            return;
        }

        this.uploads(async () => {
            const loading = await this.loader.show();

            let data: MonitoringUpdateDto = new MonitoringUpdateDto();

            data.id = this.item.id;
            data.isReal = this.monitoringAlarmIsReal.value;
            data.alarmTreatmentReason = alarmTreatmentReason;
            data.description = this.monitoringDescription.value;
            data.alarmTreatmentResources = this.item.alarmTreatmentResources;
            data.uploadResources = this.uploadResources;
            data.action = this.status;

            this.monitoringServiceProxy
                .update(data)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss()
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado satisfactoriamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        });
    }

    private uploads(callback: () => void) {
        if (this.uploadResources && this.uploadResources.length > 0) {
            this.dialog.show({
                component: AppFileUploadProgressComponent,
                componentProps: {
                    files: this.uploadResources.map(p => p.file),
                    processed: (data: IUploadProgressRespose) => {
                        if (data.completed) {
                            let index: number = 0;

                            for (let token of data.tokens) {
                                this.uploadResources[index].token = token;
                                index++;
                            }

                            callback();
                        } else {
                            this.disabled = false;
                        }
                    }
                }
            });
        } else {
            callback();
        }
    }
}