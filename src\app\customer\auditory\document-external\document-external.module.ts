import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DocumentExternalRoutingModule } from './document-external.routing.module';
import { DocumentExternalDashboardComponent } from './dashboard/dashboard.component';
import { DocumentExternalCreateEditComponent } from './create-edit/create-edit-component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';
import { TagInputModule } from 'ngx-chips'

@NgModule({
  imports: [
    DocumentExternalRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule,
    TagInputModule
  ],
  declarations: [
    DocumentExternalDashboardComponent,
    DocumentExternalCreateEditComponent
  ]
})
export class DocumentExternalModule { }