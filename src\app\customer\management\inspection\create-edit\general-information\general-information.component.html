<form [formGroup]="modalForm" class="row">

    <div class="col-12 mb-2">
        <div class="ion-input">
            <div class="input-control">

                <label class="form-label fz-normal d-block">
                    Información general
                </label>

                <hr>

            </div>
        </div>
    </div>

    <div class="col-6 mb-4">
        <app-date-input name="InspectionTime" label="Fecha (*)" [(value)]="inspectionDate"
            [disabled]="inspection.id ? true : false" />
    </div>

    <div class="col-6 mb-4">
        <app-time-input name="InspectionTime" label="Hora (*)" [(value)]="inspectionTime"
            [disabled]="inspection.id ? true : false" />
    </div>

    <div class="col-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="InspectionType">
                    Tipo de inspección (*)
                </label>
                <select class="form-control" id="InspectionType" name="InspectionType"
                    formControlName="inspectionTypeSelect">
                    <option value="-1">
                        Seleccione
                    </option>
                    <option *ngFor="let inspectionType of inspectionTypes" [value]="inspectionType.id">
                        {{inspectionType.name}}
                    </option>
                </select>
            </div>
        </div>
    </div>

    <div class="col-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="InspectionMode">
                    Estado
                </label>
                <select [disabled]="true" class="form-control" id="InspectionMode" name="InspectionMode"
                    formControlName="inspectionModeSelect">
                    <option [value]="inspectionModes.none">
                        Seleccione
                    </option>
                    <option [value]="inspectionModes.programed">
                        Programada
                    </option>
                    <option [value]="inspectionModes.modified">
                        Reprogramada
                    </option>
                    <option [value]="inspectionModes.modifiedWithoutDate">
                        Reprogramada sin fecha
                    </option>
                    <option [value]="inspectionModes.process">
                        En proceso
                    </option>
                    <option [value]="inspectionModes.executed">
                        Pendiente de revisión
                    </option>
                    <option [value]="inspectionModes.canceled">
                        Cancelada
                    </option>
                    <option [value]="inspectionModes.contract">
                        Cancelada por contrato
                    </option>
                    <option [value]="inspectionModes.rejected">
                        Rechazada
                    </option>
                    <option [value]="inspectionModes.completed">
                        Finalizada
                    </option>
                </select>
            </div>
        </div>
    </div>

    <div *ngIf="inspection.id" class="col-6 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="InspectionCode">
                    Código
                </label>
                <input type="text" [disabled]="true" class="form-control" id="InspectionCode" name="InspectionCode"
                    value="{{inspection.code}}" />
            </div>
        </div>
    </div>

    <div class="col-12 mb-2">
        <div class="ion-input">
            <div class="input-control">

                <label class="form-label fz-normal d-block">
                    Información del usuario asignado
                </label>

                <hr>

            </div>
        </div>
    </div>

    <div *ngIf="!completed" class="col-12 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="InspectionUser">
                    Buscar usuario (*)
                </label>
                <div class="input-group action">
                    <input (click)="showFindUser()" class="form-control rounded" id="InspectionUser"
                        name="InspectionUser" value="Presiona para buscar un usuario" readonly />
                    <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                </div>
            </div>
        </div>
    </div>

    <ng-container *ngIf="inspection.assignedUser">

        <div class="col-6 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionAssignedUserDocumentType">
                        Tipo documento
                    </label>
                    <input class="form-control" id="InspectionAssignedUserDocumentType"
                        name="InspectionAssignedUserDocumentType"
                        value="{{inspection?.assignedUser?.documentType?.name}}" readonly />
                </div>
            </div>
        </div>

        <div class="col-6 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionAssignedUserDocument">
                        Nº Documento
                    </label>
                    <input class="form-control" id="InspectionAssignedUserDocument"
                        name="InspectionAssignedUserDocument" value="{{inspection?.assignedUser?.document}}" readonly />
                </div>
            </div>
        </div>

        <div class="col-12 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionAssignedUserDocument">
                        Nombres y apellidos
                    </label>
                    <textarea class="form-control" id="InspectionAssignedUserDocument"
                        name="InspectionAssignedUserDocument" rows="2"
                        value="{{inspection?.assignedUser?.name}} {{inspection?.assignedUser?.surname}} {{inspection?.assignedUser?.secondSurname}}"
                        readonly></textarea>
                </div>
            </div>
        </div>

    </ng-container>

    <div class="col-12 mb-2">
        <div class="ion-input">
            <div class="input-control">

                <label class="form-label fz-normal d-block">
                    Información del cliente
                </label>

                <hr>

            </div>
        </div>
    </div>

    <div *ngIf="!completed" class="col-12 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="InspectionCustomer">
                    Buscar cliente (*)
                </label>
                <div class="input-group action">
                    <input (click)="showFindCustomer()" class="form-control rounded" id="InspectionCustomer"
                        name="InspectionCustomer" value="Presiona para buscar un cliente" readonly />
                    <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                </div>
            </div>
        </div>
    </div>

    <ng-container *ngIf="inspection.customer">

        <div class="col-6 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionCustomerCode">
                        Código
                    </label>
                    <input class="form-control" id="InspectionCustomerCode" name="InspectionCustomerCode"
                        value="{{inspection?.customer?.cardCode}}" readonly />
                </div>
            </div>
        </div>

        <div class="col-12 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionCustomerName">
                        Nombre
                    </label>
                    <textarea class="form-control" id="InspectionCustomerName" name="InspectionCustomerName" rows="2"
                        value="{{inspection?.customer?.cardName}}" readonly></textarea>
                </div>
            </div>
        </div>

    </ng-container>

    <div class="col-12 mb-2">
        <div class="ion-input">
            <div class="input-control">

                <label class="form-label fz-normal d-block">
                    Información de bodega
                </label>

                <hr>

            </div>
        </div>
    </div>

    <div *ngIf="!completed" class="col-12 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="InspectionProperty">
                    Buscar bodega (*)
                </label>
                <div class="input-group action">
                    <input (click)="showFindProperty()" class="form-control rounded" id="InspectionProperty"
                        name="InspectionProperty" value="Presiona para buscar una bodega" readonly />
                    <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                </div>
            </div>
        </div>
    </div>

    <ng-container *ngIf="inspection.property">

        <div class="col-6 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionPropertyCode">
                        Código
                    </label>
                    <input class="form-control" id="InspectionPropertyCode" name="InspectionPropertyCode"
                        value="{{inspection?.property?.code}}" readonly />
                </div>
            </div>
        </div>

        <div class="col-6 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionPropertyCenter">
                        Centro logístico
                    </label>
                    <input class="form-control" id="InspectionPropertyCenter" name="InspectionPropertyCenter"
                        value="{{inspection?.center?.name}}" readonly />
                </div>
            </div>
        </div>

        <div class="col-12 mb-4">
            <div class="ion-input">
                <div class="input-control">
                    <label class="form-label" for="InspectionPropertyName">
                        Nombre
                    </label>
                    <textarea class="form-control" id="InspectionPropertyName" name="InspectionPropertyName" rows="2"
                        value="{{inspection?.property?.name}}" readonly></textarea>
                </div>
            </div>
        </div>

    </ng-container>

    <ng-container *ngIf="inspection.mode == inspectionModes.rejected">

        <div class="col-12 mb-2">
            <div class="ion-input">
                <div class="input-control">

                    <label class="form-label fz-normal d-block">
                        Información de rechazo
                    </label>

                    <hr>

                </div>
            </div>
        </div>

        <div class="col-12 mb-4">

            <div class="ion-input">
                <div class="input-control">

                    <label for="InspectionRejectDescription" class="form-label">
                        Motivo
                    </label>

                    <textarea type="text" class="form-control" id="InspectionRejectDescription"
                        name="InspectionRejectDescription" formControlName="inspectionRejectDescriptionInput"
                        maxlength="25000" rows="4" readonly></textarea>

                </div>
            </div>

        </div>

        <div class="col-12 mb-4">

            <div class="ion-input">
                <div class="input-control">

                    <label for="InspectionRejectTime" class="form-label">
                        Fecha de rechazo
                    </label>

                    <input type="text" class="form-control" id="InspectionRejectTime" name="InspectionRejectTime"
                        value="{{inspection.rejectedTime | luxonFormat: 'dd/MM/yyyy HH:mm'}}" readonly>

                </div>
            </div>

        </div>

    </ng-container>

    <ng-container *ngIf="inspection.mode == inspectionModes.canceled">


        <div class="col-12 mb-2">
            <div class="ion-input">
                <div class="input-control">

                    <label class="form-label fz-normal d-block">
                        Información de cancelación
                    </label>

                    <hr>

                </div>
            </div>
        </div>

        <div class="col-12 mb-4">

            <div class="ion-input">
                <div class="input-control">

                    <label for="InspectionCancelDescription" class="form-label">
                        Motivo
                    </label>

                    <textarea type="text" class="form-control" id="InspectionCancelDescription"
                        name="InspectionCancelDescription" formControlName="inspectionCancelDescriptionInput" rows="4"
                        value =  "{{inspection.cancelDescription}}" readonly></textarea>

                </div>
            </div>

        </div>

        <div class="col-12 mb-4">

            <div class="ion-input">
                <div class="input-control">

                    <label for="InspectionCancelTime" class="form-label">
                        Fecha de rechazo
                    </label>

                    <input type="text" class="form-control" id="InspectionCancelTime" name="InspectionCancelTime"
                        value="{{inspection.cancelTime | luxonFormat: 'dd/MM/yyyy HH:mm'}}" readonly>

                </div>
            </div>

        </div>

    </ng-container>

    <ng-container *ngIf="inspection.mode == inspectionModes.completed">

        <div class="col-12 mb-2">
            <div class="ion-input">
                <div class="input-control">

                    <label class="form-label fz-normal d-block">
                        Información de llamadas de servicio
                    </label>

                    <hr>

                </div>
            </div>
        </div>

        <div class="col-12">
            <app-inspection-service-call-information
                [inspection]="inspection"
                [completed]="completed"/>
        </div>

    </ng-container>
</form>