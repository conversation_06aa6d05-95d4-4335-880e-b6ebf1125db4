<div class="h-100 w-100">
    <app-modal [title]="loaded ? (editable ? (id ? 'Editar Proyecto - Personas' : 'Crear Proyecto - Personas') : 'Visualizar Proyecto - Personas') : 'Cargando...'" [disabled]="disabled" size="extra">
        <app-modal-body>
            <p-tabView *ngIf="loaded" [(activeIndex)]="activeIndex" [scrollable]="true">
                <p-tabPanel header="Información general">
                    <app-operation-task-general-information
                        [operationTask]="item"
                        [editable]="editable"
                        [modalForm]="modalForm"
                        [areas]="areas" 
                        [centers]="centers" 
                        [managements]="managements"
                        [incomeExpenses]="incomeExpenses" />
                </p-tabPanel>
                <p-tabPanel header="Información de personas">
                    <app-operation-task-person-information 
                        [operationTask]="item"
                        [editable]="editable" />
                </p-tabPanel>                       
            </p-tabView>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" [showSaveButton]="editable" (onSave)="save()" />
    </app-modal>
</div>