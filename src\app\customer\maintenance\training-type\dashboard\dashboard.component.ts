import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { TrainingTypeDto, TrainingTypeServiceProxy } from '@proxies/training-type.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { TrainingTypeCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-training-type-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class TrainingTypeDashboardComponent extends ViewComponent {

    private trainingTypeServiceProxy: TrainingTypeServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    staticStates: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    defaultStates: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.trainingTypeServiceProxy = _injector.get(TrainingTypeServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: TrainingTypeCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: TrainingTypeDto) {
        this.dialog.showWithData<boolean>({
            component: TrainingTypeCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: TrainingTypeDto) {
        this.message.confirm(`¿Estas seguro de eliminar el tipo de capacitación "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.trainingTypeServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el tipo de capacitación satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.trainingTypeServiceProxy
            .getAll(
                event?.filters?.['name']?.['value'],
                event?.filters?.['enabled']?.['value'],
                event?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: TrainingTypeDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.TrainingType.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.TrainingType.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.trainingTypeServiceProxy
            .export(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['enabled']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}