import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'alarm-accounts',
    data: { permission: 'Pages.Maintenance.AlarmAccount' },
    loadChildren: () => import('./alarm-account/alarm-account.module').then(p => p.AlarmAccountModule)
  },
  {
    path: 'alarm-treatment-reasons',
    data: { permission: 'Pages.Maintenance.AlarmTreatmentReason' },
    loadChildren: () => import('./alarm-treatment-reason/alarm-reason.module').then(p => p.AlarmTreatmentReasonModule)
  },
  {
    path: 'alarm-types',
    data: { permission: 'Pages.Maintenance.AlarmType' },
    loadChildren: () => import('./alarm-type/alarm-type.module').then(p => p.AlarmTypeModule)
  },
  {
    path: 'basic-protection-equipments',
    data: { permission: 'Pages.Maintenance.BasicProtectionEquipment' },
    loadChildren: () => import('./basic-protection-equipment/basic-protection-equipment.module').then(p => p.BasicProtectionEquipmentModule)
  },
  {
    path: 'inspection-types',
    data: { permission: 'Pages.Maintenance.TrainingType' },
    loadChildren: () => import('./inspection-type/inspection-type.module').then(p => p.InspectionTypeModule)
  },
  {
    path: 'maintenance-activity-problems',
    data: { permission: 'Pages.Maintenance.MaintenanceActivityProblem' },
    loadChildren: () => import('./maintenance-activity-problem/maintenance-activity-problem.module').then(p => p.MaintenanceActivityProblemModule)
  },
  {
    path: 'maintenance-centers',
    data: { permission: 'Pages.Maintenance.MaintenanceCenter' },
    loadChildren: () => import('./maintenance-center/maintenance-center.module').then(p => p.MaintenanceCenterModule)
  },
  {
    path: 'maintenance-families',
    data: { permission: 'Pages.Maintenance.MaintenanceFamily' },
    loadChildren: () => import('./maintenance-family/maintenance-family.module').then(p => p.MaintenanceFamilyModule)
  },
  {
    path: 'maintenance-macro-systems',
    data: { permission: 'Pages.Maintenance.MaintenanceMacroSystem' },
    loadChildren: () => import('./maintenance-macro-system/maintenance-macro-system.module').then(p => p.MaintenanceMacroSystemModule)
  },
  {
    path: 'maintenance-micro-systems',
    data: { permission: 'Pages.Maintenance.MaintenanceMicroSystem' },
    loadChildren: () => import('./maintenance-micro-system/maintenance-micro-system.module').then(p => p.MaintenanceMicroSystemModule)
  },
  {
    path: 'maintenance-nano-systems',
    data: { permission: 'Pages.Maintenance.MaintenanceNanoSystem' },
    loadChildren: () => import('./maintenance-nano-system/maintenance-nano-system.module').then(p => p.MaintenanceNanoSystemModule)
  },
  {
    path: 'job-types',
    data: { permission: 'Pages.Maintenance.JobType' },
    loadChildren: () => import('./job-type/job-type.module').then(p => p.JobTypeModule)
  },
  {
    path: 'maintenance-program-parameters',
    data: { permission: 'Pages.Maintenance.MaintenanceProgramParameter' },
    loadChildren: () => import('./maintenance-program-parameter/maintenance-program-parameter.module').then(p => p.MaintenanceProgramParameterModule)
  },
  {
    path: 'maintenance-service-request-ensurances',
    data: { permission: 'Pages.Maintenance.MaintenanceServiceRequestEnsurance' },
    loadChildren: () => import('./maintenance-service-request-ensurance/maintenance-service-request-ensurance.module').then(p => p.MaintenanceServiceRequestEnsuranceModule)
  },
  {
    path: 'person-areas',
    data: { permission: 'Pages.Maintenance.PersonArea' },
    loadChildren: () => import('./person-area/person-area.module').then(p => p.PersonAreaModule)
  },
  {
    path: 'specific-protection-equipments',
    data: { permission: 'Pages.Maintenance.SpecificProtectionEquipment' },
    loadChildren: () => import('./specific-protection-equipment/specific-protection-equipment.module').then(p => p.SpecificProtectionEquipmentModule)
  },
  {
    path: 'training-modes',
    data: { permission: 'Pages.Maintenance.TrainingMode' },
    loadChildren: () => import('./training-mode/training-mode.module').then(p => p.TrainingModeModule)
  },
  {
    path: 'training-types',
    data: { permission: 'Pages.Maintenance.TrainingType' },
    loadChildren: () => import('./training-type/training-type.module').then(p => p.TrainingTypeModule)
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule]
})
export class MaintenanceRoutingModule { }
