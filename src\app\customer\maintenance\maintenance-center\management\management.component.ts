import { Component, Injector, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { GoogleMapServiceProxy } from '@proxies/google-map.proxy';
import { MaintenanceCenterCoordinateDto, MaintenanceCenterDto, MaintenanceCenterServiceProxy } from '@proxies/maintenance-center.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-center-management',
    templateUrl: 'management.component.html',
    styleUrls: [
        'management.component.scss'
    ]
})
export class MaintenanceCenterManagementComponent extends ViewComponent implements OnInit {

    private readonly maintenanceCenterServiceProxy: MaintenanceCenterServiceProxy;
    private readonly googleMapServiceProxy: GoogleMapServiceProxy;
    private readonly activatedRoute: ActivatedRoute;

    apiKey!: string;
    item!: MaintenanceCenterDto;
    loaded: boolean = false;
    selectedShape!: any;
    polygons: google.maps.Polygon[] = [];

    private disabled: boolean = false;

    constructor(injector: Injector) {
        super(injector);

        this.maintenanceCenterServiceProxy = injector.get(MaintenanceCenterServiceProxy);
        this.googleMapServiceProxy = injector.get(GoogleMapServiceProxy);
        this.activatedRoute = injector.get(ActivatedRoute);
    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        try {
            const code: string = await this.getParameterCode();

            this.item = await this.getMaintenanceCenter(code);
            this.apiKey = await this.getGoogleMapsKey();

            this.loaded = true;
        } catch {
            this.onBackButtonPressed();
        }

        await loading.dismiss();
    }

    clearPolygons(): void {
        for (let polygon of this.polygons)
            polygon.setMap(null);

        this.polygons = [];
    }

    async save(): Promise<void> {
        if (this.polygons.length == 0) {
            this.message.info('El área de la ubicación del centro es obligatoria.');
            return;
        }
        if (this.polygons.length > 1) {
            this.message.info('Solo debe existir un área para la ubicación del centro.');
            return;
        }

        const coordinates: MaintenanceCenterCoordinateDto[] = this.polygons[0]
            .getPath()
            .getArray()
            .map((p: any) => new MaintenanceCenterCoordinateDto({ lat: p.lat(), lng: p.lng() }));

        this.item.path = JSON.stringify(coordinates);

        if (this.disabled)
            return;

        this.disabled = true;

        const loading = await this.loader.show();

        this.maintenanceCenterServiceProxy
            .update(this.item)
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: () => {
                    this.notify.success('Área asignada satisfactoriamente.');
                    this.onBackButtonPressed();
                }
            });
    }

    onBackButtonPressed(): void {
        this.navigation.back('/customer/maintenance/maintenance-centers/dashboard');
    }

    private getMaintenanceCenter(code: string): Promise<MaintenanceCenterDto> {
        return new Promise<MaintenanceCenterDto>((resolve, reject) => {
            this.maintenanceCenterServiceProxy.get(code).subscribe({
                next: (response) => resolve(response),
                error: () => reject()
            });
        });
    }

    private getParameterCode(): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            this.activatedRoute.params.subscribe({
                next: (params) => {
                    const code: string = params['center'];

                    if (isNullEmptyOrWhiteSpace(code)) {
                        reject();
                    } else {
                        resolve(code);
                    }
                },
                error: () => reject()
            });
        });
    }

    private getGoogleMapsKey(): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            this.googleMapServiceProxy.get().subscribe({
                next: (response) => {
                    if (isNullEmptyOrWhiteSpace(response.apiKey)) {
                        this.notify.error('No se puede realizar la operación debido a que no existe un API_KEY configurado actualmente.', 10_000, 'bottom');
                        reject();
                    } else {
                        resolve(response.apiKey);
                    }
                },
                error: () => reject()
            })
        });
    }
}