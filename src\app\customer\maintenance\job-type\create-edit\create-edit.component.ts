import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { JobTypeDto, JobTypeServiceProxy } from '@proxies/job-type.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-job-type-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class JobTypeCreateEditComponent extends ViewComponent implements OnInit {

    private jobTypeServiceProxy: JobTypeServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: JobTypeDto = new JobTypeDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get jobTypeName(): AbstractControl {
        return this.modalForm.controls['jobTypeNameInput'];
    };

    get jobTypeEnabled(): AbstractControl {
        return this.modalForm.controls['jobTypeEnabledSelect'];
    };

    get jobTypeShowExtra(): AbstractControl {
        return this.modalForm.controls['jobTypeShowExtraSelect'];
    };

    get jobTypeCode(): AbstractControl {
        return this.modalForm.controls['jobTypeCodeInput'];
    };
    
    constructor(_injector: Injector) {
        super(_injector);

        this.jobTypeServiceProxy = _injector.get(JobTypeServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            jobTypeNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            jobTypeEnabledSelect: ['true', [Validators.required]],
            jobTypeShowExtraSelect: ['false', [Validators.required]],
            jobTypeCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.jobTypeServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.jobTypeName.setValue(this.item.name);
                        this.jobTypeEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.jobTypeShowExtra.setValue(this.item.showExtra ? 'true' : 'false');
                        this.jobTypeCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new JobTypeDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.jobTypeName.value;
        this.item.enabled = this.jobTypeEnabled.value == 'true';
        this.item.showExtra = this.jobTypeShowExtra.value == 'true';
        this.item.code = this.jobTypeCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre de tipo de trabajo es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.jobTypeServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de trabajo actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.jobTypeServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de trabajo creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}