<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? (onlyview ? 'Visualizar Documento Externo' : 'Editar Documento Externo') : 'Crear Documento Externo'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General"> 
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentName.invalid && (documentName.touched || documentName.dirty))}">
                                        Nombre (*)
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control" id="DocumentName" name="DocumentName"
                                        formControlName="documentNameInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentMacroProcess" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentMacroProcess.invalid && (documentMacroProcess.touched || documentMacroProcess.dirty))}">
                                        Macro Proceso
                                    </label>
                                    <select class="form-control" style="z-index: 1;" id="DocumentMacroProcess"
                                        name="DocumentMacroProcess"
                                        formControlName="documentMacroProcessSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let macroprocess of macroProcesses"
                                            [value]="macroprocess.id">
                                            {{macroprocess.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            Seleccione un Macro Proceso
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="DocumentProcess" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentProcess.invalid && (documentProcess.touched || documentProcess.dirty))}">
                                        Proceso
                                    </label>
                                    <select class="form-control" id="DocumentProcess" name="DocumentProcess"
                                        formControlName="documentProcessSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let process of processSelect" [value]="process.id">
                                            {{process.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            Seleccione un Proceso
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentLocationType" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentLocationType.invalid && (documentLocationType.touched || documentLocationType.dirty))}">
                                        Tipo de Ubicación (*)
                                    </label>

                                    <select class="form-control" id="DocumentLocationType" name="DocumentLocationType"
                                        formControlName="documentLocationTypeSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let locationType of locationTypes" [value]="locationType">
                                            {{locationType}}
                                        </option>
                                    </select>

                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentLocation" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentLocation.invalid && (documentLocation.touched || documentLocation.dirty))}">
                                        Ubicación
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control" id="DocumentLocation" name="DocumentLocation"
                                        formControlName="documentLocationInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentRetentionTime" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentRetentionTime.invalid && (documentRetentionTime.touched || documentRetentionTime.dirty))}">
                                        Tiempo de Retención (Años) (*)
                                    </label>

                                    <input type="number" style="text-transform: uppercase;" class="form-control" id="DocumentRetentionTime" name="DocumentRetentionTime"
                                        formControlName="documentRetentionTimeInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El tiempo de retención del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentProvision" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentProvision.invalid && (documentProvision.touched || documentProvision.dirty))}">
                                        Disposición Final (*)
                                    </label>

                                    <select class="form-control" id="DocumentProvision" name="DocumentProvision"
                                        formControlName="documentProvisionSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let provision of provisionTypes" [value]="provision">
                                            {{provision}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">

                                    <label for="DocumentEdition" class="form-label"
                                        [ngClass]="{'ng-invalid' : (documentEdition.invalid && (documentEdition.touched || documentEdition.dirty))}">
                                        Edición, Año u Otro (*)
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control" id="DocumentEdition" name="DocumentEdition"
                                        formControlName="documentEditionInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            La edición del documento es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <ion-row class="ion-align-items-center">
                                        <ion-col size="6" size-md="12">
                                          <ion-item lines="none">
                                            <ion-checkbox slot="start" formControlName="documentLdrCheck">
                                            </ion-checkbox>
                                            <ion-label>¿Visualizar en LDR?</ion-label>
                                          </ion-item>
                                        </ion-col>
                                      </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [showSaveButton]="!onlyview" [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>