import { Component, Injector, Input, OnInit } from '@angular/core';
import { AppResourceFullScreenComponent } from '@components/resource-full-screen/resource-full-screen.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppConsts } from '@core/inheritance/app-core-consts';
import { InspectionAlarmMode, InspectionProgramDto, InspectionProgramServiceProxy } from '@proxies/inspection-program.proxy';
import { InspectionDto } from '@proxies/inspection.proxy';

@Component({
    selector: 'app-inspection-operation-information',
    templateUrl: 'operation-information.component.html',
    styleUrls: [
        'operation-information.component.scss'
    ]
})
export class InspectionOperationInformationComponent extends ViewComponent implements OnInit {

    private inspectionProgramServiceProxy: InspectionProgramServiceProxy;

    @Input() inspection!: InspectionDto;
    @Input() completed!: boolean;

    activeIndex: number[] = [];
    loading: boolean = true;
    inspectionProgram!: InspectionProgramDto;

    inspectionAlarmModes = {
        true: InspectionAlarmMode.True,
        false: InspectionAlarmMode.False,
        undefined: InspectionAlarmMode.Undefined
    }

    private isOnPreview: boolean = false;

    constructor(injector: Injector) {
        super(injector);

        this.inspectionProgramServiceProxy = injector.get(InspectionProgramServiceProxy);
    }

    ngOnInit(): void {
        this.inspectionProgramServiceProxy
            .get(this.inspection.lastInspectionProgramId)
            .subscribe({
                next: (response) => {
                    this.preferences.get(AppConsts.authorization.encrptedAuthTokenName).subscribe({
                        next: (token) => {
                            this.inspectionProgram = response;

                            for (let resource of response.inspectionLicenseResources)
                                resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                            for (let resource of response.inspectionCertificateResources)
                                resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                            for (let resource of response.inspectionStructure.inspectionStructureResources)
                                resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                            for (let resource of response.inspectionStructure.inspectionBatteryBankResources)
                                resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                            for (let resource of response.inspectionFuel.inspectionFuelResources)
                                resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                            for (let resource of response.inspectionFuel.inspectionDangerousMaterialResources)
                                resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                            for (let resource of response.inspectionAlarm.inspectionAlarmResources)
                                resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;

                            for (let inspectionElectric of response.inspectionElectrics)
                                for (let resource of inspectionElectric.inspectionElectricResources)
                                    resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;

                            for (let inspectionFireProtection of response.inspectionFire.inspectionFireProtections)
                                for (let resource of inspectionFireProtection.inspectionFireProtectionResources)
                                    resource.path = `${resource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;

                            if (response.inspectionCustomer !== null && response.inspectionCustomer !== undefined) {
                                if (response.inspectionCustomer.inspectionCustomerSignatureResource !== null && response.inspectionCustomer.inspectionCustomerSignatureResource !== undefined)
                                    response.inspectionCustomer.inspectionCustomerSignatureResource.path = `${response.inspectionCustomer.inspectionCustomerSignatureResource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                                if (response.inspectionCustomer.inspectionInspectorSignatureResource !== null && response.inspectionCustomer.inspectionInspectorSignatureResource !== undefined)
                                    response.inspectionCustomer.inspectionInspectorSignatureResource.path = `${response.inspectionCustomer.inspectionInspectorSignatureResource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;

                                if (response.inspectionCustomer.inspectionCustomerFrontDocumentResource !== null && response.inspectionCustomer.inspectionCustomerFrontDocumentResource !== undefined)
                                    response.inspectionCustomer.inspectionCustomerFrontDocumentResource.path = `${response.inspectionCustomer.inspectionCustomerFrontDocumentResource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                                if (response.inspectionCustomer.inspectionCustomerBackDocumentResource !== null && response.inspectionCustomer.inspectionCustomerBackDocumentResource !== undefined)
                                    response.inspectionCustomer.inspectionCustomerBackDocumentResource.path = `${response.inspectionCustomer.inspectionCustomerBackDocumentResource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;

                                if (response.inspectionCustomer.inspectionInspectorFrontDocumentResource !== null && response.inspectionCustomer.inspectionInspectorFrontDocumentResource !== undefined)
                                    response.inspectionCustomer.inspectionInspectorFrontDocumentResource.path = `${response.inspectionCustomer.inspectionInspectorFrontDocumentResource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                                if (response.inspectionCustomer.inspectionInspectorBackDocumentResource !== null && response.inspectionCustomer.inspectionInspectorBackDocumentResource !== undefined)
                                    response.inspectionCustomer.inspectionInspectorBackDocumentResource.path = `${response.inspectionCustomer.inspectionInspectorBackDocumentResource.path}&${encodeURIComponent('enc_auth_token')}=${encodeURIComponent(token)}`;
                            }

                            this.loading = false;
                        }
                    });
                }
            });
    }

    showResource(path: string): void {
        if (this.isOnPreview)
            return;

        this.isOnPreview = true;
        this.dialog.show({
            component: AppResourceFullScreenComponent,
            cssClass: 'transparent',
            componentProps: {
                resource: path
            }
        })
            .then(() => this.isOnPreview = false)
            .catch(() => this.isOnPreview = false);
    }
}