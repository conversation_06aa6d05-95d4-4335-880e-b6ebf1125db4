import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AccountRoutingModule } from './account.rounting.module';
import { LoginComponent } from './login/login.component';
import { AccountComponent } from './account.component';
import { CoreModule } from '@core/core.module';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { ForgotPasswordCodeComponent } from './forgot-password-code/forgot-password-code.component';
import { AccountGuard } from './account.guard';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    CoreModule,
    AccountRoutingModule
  ],
  declarations: [
    AccountComponent,
    LoginComponent,
    ForgotPasswordComponent,
    ForgotPasswordCodeComponent
  ],
  providers: [
    AccountGuard
  ]
})
export class AccountModule { }
