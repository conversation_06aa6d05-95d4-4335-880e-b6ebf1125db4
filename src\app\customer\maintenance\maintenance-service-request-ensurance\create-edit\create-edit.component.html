<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar tipo de póliza, seguro o similar' : '<PERSON>rear tipo de póliza, seguro o similar'"
        [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceServiceRequestEnsuranceName" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceServiceRequestEnsuranceName.invalid && (maintenanceServiceRequestEnsuranceName.touched || maintenanceServiceRequestEnsuranceName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceServiceRequestEnsuranceName"
                                name="MaintenanceServiceRequestEnsuranceName"
                                formControlName="maintenanceServiceRequestEnsuranceNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="MaintenanceServiceRequestEnsuranceDescriptionRequired">
                                ¿Requiere descripción? (*)
                            </label>
                            <select class="form-control" id="MaintenanceServiceRequestEnsuranceDescriptionRequired"
                                name="MaintenanceServiceRequestEnsuranceDescriptionRequired"
                                formControlName="maintenanceServiceRequestEnsuranceDescriptionRequiredSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="MaintenanceServiceRequestEnsuranceEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="MaintenanceServiceRequestEnsuranceEnabled"
                                name="MaintenanceServiceRequestEnsuranceEnabled"
                                formControlName="maintenanceServiceRequestEnsuranceEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceServiceRequestEnsuranceIndex" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceServiceRequestEnsuranceName.invalid && (maintenanceServiceRequestEnsuranceName.touched || maintenanceServiceRequestEnsuranceName.dirty))}">
                                Índice
                            </label>

                            <input inputNumber type="text" class="form-control"
                                id="MaintenanceServiceRequestEnsuranceIndex"
                                name="MaintenanceServiceRequestEnsuranceIndex"
                                formControlName="maintenanceServiceRequestEnsuranceIndexInput" maxlength="5">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El índice es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceServiceRequestEnsuranceCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="MaintenanceServiceRequestEnsuranceCode"
                                name="MaintenanceServiceRequestEnsuranceCode"
                                formControlName="maintenanceServiceRequestEnsuranceCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>