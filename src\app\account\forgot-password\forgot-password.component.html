<form [formGroup]="forgotPasswordForm" class="d-flex flex-column">

    <ion-label class="d-block text-center h4 mb-2">
        Recuperar acceso
    </ion-label>

    <small class="text-muted mb-3">
        Enviaremos un correo electrónico con un código de 6 dígitos para que puedas recuperar el acceso a tu cuenta. Si el correo electrónico no llega en 5 minutos, por favor repite el proceso nuevamente.
    </small>

    <div class="form-group mb-3">
        <label class="form-label" for="EmailAddress">
            Correo electrónico
        </label>
        <div class="form-control-group">
            <input type="email" id="EmailAddress" class="form-control" formControlName="emailAdressInput">
            <div class="form-control-validation">
                {{ emailAddress.value ? 'El correo electrónico no es válido.' : 'El correo electrónico es obligatorio.' }}
            </div>
        </div>
    </div>
    
    <ion-button routerLink="/account/login" routerDirection="root" class="me-auto ion-no-padding mb-3" size="small" fill="clear">
        <ion-icon name="chevron-back"></ion-icon>
        <ion-label>
            Iniciar sesión
        </ion-label>
    </ion-button>

    <ion-button (click)="continue()" [disabled]="forgotPasswordForm.invalid" size="normal" expand="block">
        <ion-label>
            Enviar código
        </ion-label>
    </ion-button>

</form>