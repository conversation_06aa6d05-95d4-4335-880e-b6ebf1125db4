<form [formGroup]="modalForm">

  <div class="row">
    <div class="col-6">
      <div class="ion-input mb-4">


        <div class="input-control">
          <label class="form-label" for="AreaPositionTierCreate">
              Centro Logístico
          </label>
          <div class="input-group action">
            <input (click)="showFindPosition('logisticCenter')" type="text" class="form-control rounded"
              id="AreaPositionTierCreate" name="AreaPositionTierCreate" formControlName="logisticCenterName"
              readonly />
            <ion-icon class="input-group-icon-right" name="search"></ion-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="col-6">
      <div class="ion-input mb-4">
        <div class="input-control">
          <label for="enviadoA" class="form-label">
            Enviado a
          </label>
          <input type="text" class="form-control" id="enviadoA" formControlName="enviadoA" maxlength="32">
          <ion-row class="input-validation">
            <ion-col size="12">
              El documento es obligatorio.
            </ion-col>
          </ion-row>
        </div>
      </div>
    </div>
  </div>


  <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">

    <div class="ion-input">
      <div class="input-control">

        <label for="descripcionPortador" class="form-label">
          Remitimos con el portador
        </label>

        <textarea type="text" class="form-control" id="descripcionPortador" formControlName="descripcionPortador"
          maxlength="25000" rows="2"></textarea>

        <ion-row class="input-validation">
          <ion-col size="8">
            La descripción es obligatoria.
          </ion-col>
        </ion-row>
      </div>
    </div>

  </div>

  <div class="row mt-4">

    <!-- Emisor -->
    <div class="col-md-6">
      <div class="border p-3 mb-3 position-relative">
        <span class="position-absolute top-0 start-50 translate-middle badge bg-primary px-3 py-2">
          EMISOR
        </span>
        <div class="mb-2">
          <label for="tipoDocumentoEmisor" class="form-label"><strong>Tipo Documento:</strong></label>
          <select id="tipoDocumentoEmisor" class="form-control" formControlName="tipoDocumentoEmisor">
            <option *ngFor="let tipo of documentTypes" [value]="tipo.id">{{ tipo.name }}</option>
          </select>
        </div>

        <div class="mb-2">
          <label for="numeroDocumentoEmisor" class="form-label"><strong>Nro Documento:</strong></label>
          <div class="input-group">
            <input id="numeroDocumentoEmisor" type="text" class="form-control"
              formControlName="numeroDocumentoEmisor" />
            <button class="btn btn-outline-secondary" type="button" (click)="buscarEmisor()" [disabled]="isDisabled">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>

        <div class="mb-2">
          <label for="nombreEmisor" class="form-label"><strong>Nombre:</strong></label>
          <input id="nombreEmisor" type="text" class="form-control" formControlName="nombreEmisor" readonly>
        </div>

        <div class="mb-2">
          <label for="correoEmisor" class="form-label"><strong>Correo:</strong></label>
          <input id="correoEmisor" type="email" class="form-control" formControlName="correoEmisor" readonly>
        </div>
      </div>

      <!-- Firma Emisor -->
      <div class="border p-2">
        <img [src]="getResource (registerCharge.signatureEmisor.resource)" *ngIf="getExisteSignatureEmisor()" />
        <app-signature-pad #emisorPad *ngIf="!getExisteSignatureEmisor()" [options]="signaturePadOptions"
          (onBeginEvent)="onSignatureStart()" (onEndEvent)="onSignatureEnd()"></app-signature-pad>
        <div class="text-end mt-2" *ngIf="!getExisteSignatureEmisor()">
          <button class="btn btn-sm btn-outline-danger" (click)="borrarFirma('emisor')" title="Borrar firma">
            <i class="fas fa-eraser"></i>
          </button>
        </div>
      </div>
    </div>



    <div class="col-md-6">
      <div class="border p-3 mb-3 position-relative">
        <span class="position-absolute top-0 start-50 translate-middle badge bg-primary px-3 py-2">
          RECEPTOR
        </span>

        <div class="mb-2">
          <label for="tipoDocumentoReceptor" class="form-label"><strong>Tipo Documento:</strong></label>
          <select id="tipoDocumentoReceptor" class="form-control" formControlName="tipoDocumentoReceptor">
            <option *ngFor="let tipo of documentTypes" [value]="tipo.id">{{ tipo.name }}</option>
          </select>
        </div>

        <div class="mb-2">
          <label for="numeroDocumentoReceptor" class="form-label"><strong>Nro Documento:</strong></label>
          <div class="input-group">
            <input id="numeroDocumentoReceptor" type="text" class="form-control"
              formControlName="numeroDocumentoReceptor" />
            <button class="btn btn-outline-secondary" type="button" (click)="buscarReceptor()" [disabled]="isDisabled">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>

        <div class="mb-2">
          <label for="nombreReceptor" class="form-label"><strong>Nombre:</strong></label>
          <input id="nombreReceptor" type="text" class="form-control" formControlName="nombreReceptor" readonly />
        </div>

        <div class="mb-2">
          <label for="correoReceptor" class="form-label"><strong>Correo:</strong></label>
          <input id="correoReceptor" type="email" class="form-control" formControlName="correoReceptor" readonly />
        </div>
      </div>

      <div class="border p-2">
        <img [src]="getResource(registerCharge.signatureReceptor.resource)" *ngIf="getExisteSignatureReceptor()" />
        <app-signature-pad #receptorPad *ngIf="!getExisteSignatureReceptor()" [options]="signaturePadOptions"
          (onBeginEvent)="onSignatureStart()" (onEndEvent)="onSignatureEnd()"></app-signature-pad>
        <div class="text-end mt-2" *ngIf="!getExisteSignatureReceptor()">
          <button class="btn btn-sm btn-outline-danger" (click)="borrarFirma('receptor')" title="Borrar firma">
            <i class="fas fa-eraser"></i>
          </button>
        </div>
      </div>
    </div>


  </div>
</form>

<p-dialog header="Aviso" [(visible)]="mostrarDialogoRegistro" [draggable]="false">
  <p>La persona no se encuentra registrada.</p>
  <div class="center-content">
    <p-button type="button" label="AGREGAR PERSONA" icon="pi pi-plus" iconPos="left" (onClick)="registrarPersona()"
      styleClass="custom-red-button"></p-button>
  </div>
</p-dialog>