<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar Norma' : 'Crear Norma'" [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="PolicyName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (policyName.invalid && (policyName.touched || policyName.dirty))}">
                                        Nombre (*)
                                    </label>
                                    <input type="text" class="form-control" id="PolicyName" name="PolicyName"
                                        formControlName="policyNameInput">
                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El nombre es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <app-auditory-requeriment-dashboard [policy]="item" *ngIf="item?.id">
                    </app-auditory-requeriment-dashboard>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>