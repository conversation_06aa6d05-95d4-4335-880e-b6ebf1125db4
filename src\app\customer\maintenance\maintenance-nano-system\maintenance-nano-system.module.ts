import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceNanoSystemRoutingModule } from './maintenance-nano-system.routing.module';
import { MaintenanceNanoSystemDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceNanoSystemCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    MaintenanceNanoSystemRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceNanoSystemDashboardComponent,
    MaintenanceNanoSystemCreateEditComponent
  ]
})
export class MaintenanceNanoSystemModule { }
