<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Configuración
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="reset()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button busyIf [busy]="busy" (click)="save()" class="ion-option" color="primary" fill="solid">
                <ion-icon name="save"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block ms-2">
                    Guardar
                </ion-label>
            </ion-button>

        </ion-buttons>
    </ion-toolbar>
</ion-header>
<ion-content *ngIf="settingsData" color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Configuración de correo electrónico (SMTP)
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="EmailAddressSenderInput">
                                Nombre de remitente
                            </label>
                            <input type="text" class="form-control" id="EmailAddressSenderInput"
                                name="EmailAddressSenderInput" [(ngModel)]="settingsData.email.displayName">
                        </div>
                    </div>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="EmailAddressServerInput">
                                Servidor SMTP
                            </label>
                            <input type="text" class="form-control" id="EmailAddressServerInput"
                                name="EmailAddressServerInput" [(ngModel)]="settingsData.email.smtpHost">
                        </div>
                    </div>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="EmailAddressServerPortInput">
                                Puerto SMTP
                            </label>
                            <input inputNumber type="text" class="form-control" id="EmailAddressServerPortInput"
                                name="EmailAddressServerPortInput" [(ngModel)]="settingsData.email.smtpPort"
                                maxlength="4">
                        </div>
                    </div>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start" [(ngModel)]="settingsData.email.smtpEnableSsl">
                        <span class="fz-small">Usar SSL</span>
                    </ion-checkbox>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="EmailAddressUserInput">
                                Usuario
                            </label>
                            <input type="text" class="form-control" id="EmailAddressUserInput"
                                name="EmailAddressUserInput" [(ngModel)]="settingsData.email.smtpUserName">
                        </div>
                    </div>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="EmailAddressPasswordInput">
                                Contraseña
                            </label>
                            <input type="password" class="form-control" id="EmailAddressPasswordInput"
                                name="EmailAddressPasswordInput" [(ngModel)]="settingsData.email.smtpPassword">
                        </div>
                    </div>
                </ion-col>

            </ion-row>

            <ion-row>
                <ion-col class="mt-4" size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Envío de correo electrónico de pruebas
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col sizeXs="12" sizeSm="12" sizeMd="8" sizeLg="6" sizeXl="4">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label mb-0" for="TestEmailAddress">
                                Correo electrónico
                            </label>
                            <ion-note class="fz-note d-block">
                                Verifica que la configuración de correo electrónico funcione correctamente.
                            </ion-note>
                            <input type="text" class="form-control" id="TestEmailAddress"
                                placeholder="Ingrese el correo electrónico de pruebas.." name="TestEmailAddress"
                                [(ngModel)]="testEmailAddress">
                        </div>
                    </div>
                </ion-col>

                <ion-col class="d-flex align-items-end" sizeXs="12" sizeSm="12" sizeMd="4" sizeLg="6" sizeXl="8">
                    <ion-button (click)="sendTestEmailAddress()" class="mt-auto mb-0">
                        <ion-label>
                            Enviar
                        </ion-label>
                    </ion-button>
                </ion-col>
            </ion-row>

            <ion-row>
                <ion-col class="mt-4" size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Complejidad de la contraseña
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start"
                        [(ngModel)]="settingsData.passwordComplexity.requireDigit">
                        <span class="fz-small">Requiere dígitos</span>
                    </ion-checkbox>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start"
                        [(ngModel)]="settingsData.passwordComplexity.requireLowercase">
                        <span class="fz-small">Requiere minúsculas</span>
                    </ion-checkbox>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start"
                        [(ngModel)]="settingsData.passwordComplexity.requireNonAlphanumeric">
                        <span class="fz-small">Requiere caracter no alfanumérico</span>
                    </ion-checkbox>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start"
                        [(ngModel)]="settingsData.passwordComplexity.requireUppercase">
                        <span class="fz-small">Requiere mayúsculas</span>
                    </ion-checkbox>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="PasswordSettingLength">
                                Longitud mínima
                            </label>
                            <input inputNumber type="text" class="form-control" id="PasswordSettingLength"
                                name="PasswordSettingLength"
                                [(ngModel)]="settingsData.passwordComplexity.requiredLength" maxlength="2">
                        </div>
                    </div>
                </ion-col>

            </ion-row>

            <ion-row>

                <ion-col size="12">

                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Configuración de inicio de sesión con Microsoft
                    </ion-label>

                    <hr>

                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start"
                        [(ngModel)]="settingsData.authentication.microsoft.isEnabled">
                        <span class="fz-small">
                            ¿Habilitar inicio de sesión con Microsoft
                        </span>
                    </ion-checkbox>
                </ion-col>

                <ion-col *ngIf="settingsData.authentication.microsoft.isEnabled" size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="AutenticationMicrosoftAuthority">
                                Tenant
                            </label>
                            <input type="text" class="form-control" id="AutenticationMicrosoftAuthority"
                                name="AutenticationMicrosoftAuthority"
                                [(ngModel)]="settingsData.authentication.microsoft.authority">
                        </div>
                    </div>
                </ion-col>

                <ion-col *ngIf="settingsData.authentication.microsoft.isEnabled" size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="AutenticationMicrosoftClientId">
                                Cliente
                            </label>
                            <input type="text" class="form-control" id="AutenticationMicrosoftClientId"
                                name="AutenticationMicrosoftClientId"
                                [(ngModel)]="settingsData.authentication.microsoft.clientId">
                        </div>
                    </div>
                </ion-col>

            </ion-row>

            <ion-row>
                <ion-col class="mt-4" size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Bloqueo de usuarios
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start" [(ngModel)]="settingsData.userLockOut.isEnabled">
                        <span class="fz-small">
                            Habilitar bloqueo de cuenta por intentos fallidos al iniciar sesión.
                        </span>
                    </ion-checkbox>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="WrongLoginCount">
                                Número máximo de intentos fallidos antes de bloquear la cuenta
                            </label>
                            <input inputNumber type="text" class="form-control" id="WrongLoginCount"
                                name="WrongLoginCount"
                                [(ngModel)]="settingsData.userLockOut.maxFailedAccessAttemptsBeforeLockout"
                                maxlength="5">
                        </div>
                    </div>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="AccountLockerTimeOut">
                                Tiempo (segundos) de bloqueo antes de poder iniciar sesión nuevamente
                            </label>
                            <input inputNumber type="text" class="form-control" id="AccountLockerTimeOut"
                                name="AccountLockerTimeOut"
                                [(ngModel)]="settingsData.userLockOut.defaultAccountLockoutSeconds" maxlength="5">
                        </div>
                    </div>
                </ion-col>

                <ion-col size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Configuración de reporte (Jaspersoft)
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start" [(ngModel)]="settingsData.report.isEnabled">
                        <span class="fz-small">
                            ¿Habilitado?
                        </span>
                    </ion-checkbox>
                </ion-col>

                <ion-col *ngIf="settingsData.report.isEnabled" size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="ReportServerInput">
                                Servidor
                            </label>
                            <input type="text" class="form-control" id="ReportServerInput" name="ReportServerInput"
                                [(ngModel)]="settingsData.report.url">
                        </div>
                    </div>
                </ion-col>

            </ion-row>

            <ion-row *ngIf="settingsData.integration">

                <ion-col size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Configuración de integración (SAP)
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col size="12">
                    <ion-checkbox labelPlacement="end" justify="start" [(ngModel)]="settingsData.integration.isEnabled">
                        <span class="fz-small">
                            ¿Habilitado?
                        </span>
                    </ion-checkbox>
                </ion-col>

                <ion-col *ngIf="settingsData.report.isEnabled" size="12">

                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="IntegrationService">
                                Servidor
                            </label>
                            <input type="text" class="form-control" id="IntegrationService" name="IntegrationService"
                                [(ngModel)]="settingsData.integration.service">
                        </div>
                    </div>

                    <ion-col size="12">
                        <div class="ion-input mb-2">
                            <div class="input-control">
                                <label class="form-label" for="IntegrationServiceUsername">
                                    Usuario
                                </label>
                                <input type="text" class="form-control" id="IntegrationServiceUsername"
                                    name="IntegrationServiceUsername" [(ngModel)]="settingsData.integration.username">
                            </div>
                        </div>
                    </ion-col>

                    <ion-col size="12">
                        <div class="ion-input mb-2">
                            <div class="input-control">
                                <label class="form-label" for="IntegrationServicePassword">
                                    Contraseña
                                </label>
                                <input type="password" class="form-control" id="IntegrationServicePassword"
                                    name="IntegrationServicePassword" [(ngModel)]="settingsData.integration.password">
                            </div>
                        </div>
                    </ion-col>

                </ion-col>

            </ion-row>

            <ion-row *ngIf="settingsData.integration">

                <ion-col size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Configuración de inspecciones
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col size="12">
                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="InspectionCustomer">
                                Código de cliente (Global)
                            </label>
                            <input type="text" class="form-control" id="InspectionCustomer" name="InspectionCustomer"
                                [(ngModel)]="settingsData.inspection.customer">
                        </div>
                    </div>
                </ion-col>

            </ion-row>

            <ion-row *ngIf="settingsData.google">

                <ion-col size="12">
                    <ion-label class="d-block fz-normal fw-bold" color="dark">
                        Google maps
                    </ion-label>
                    <hr>
                </ion-col>

                <ion-col *ngIf="settingsData.google.map" size="12">

                    <div class="ion-input mb-2">
                        <div class="input-control">
                            <label class="form-label" for="GoogleMapApiKey">
                                Api Key
                            </label>
                            <input type="password" class="form-control" id="GoogleMapApiKey" name="GoogleMapApiKey"
                                [(ngModel)]="settingsData.google.map.apiKey">
                        </div>
                    </div>

                </ion-col>

            </ion-row>

        </ion-card-content>
    </ion-card>
</ion-content>