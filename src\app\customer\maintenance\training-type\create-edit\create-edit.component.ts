import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { TrainingTypeDto, TrainingTypeServiceProxy } from '@proxies/training-type.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-training-type-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class TrainingTypeCreateEditComponent extends ViewComponent implements OnInit {

    private trainingTypeServiceProxy: TrainingTypeServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: TrainingTypeDto = new TrainingTypeDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get trainingTypeName(): AbstractControl {
        return this.modalForm.controls['trainingTypeNameInput'];
    };

    get trainingTypeEnabled(): AbstractControl {
        return this.modalForm.controls['trainingTypeEnabledSelect'];
    };

    get trainingTypeCode(): AbstractControl {
        return this.modalForm.controls['trainingTypeCodeInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.trainingTypeServiceProxy = _injector.get(TrainingTypeServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            trainingTypeNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            trainingTypeEnabledSelect: ['false', [Validators.required]],
            trainingTypeCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.trainingTypeServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.trainingTypeName.setValue(this.item.name);
                        this.trainingTypeEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.trainingTypeCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new TrainingTypeDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.trainingTypeName.value;
        this.item.enabled = this.trainingTypeEnabled.value == 'true';
        this.item.code = this.trainingTypeCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre del tipo de capacitación es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.trainingTypeServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de capacitación actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.trainingTypeServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de capacitación creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}