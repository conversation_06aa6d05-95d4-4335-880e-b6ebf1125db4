import { Component, Injector, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, AbstractControl, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { IPasswordInputConfig } from '@core/models/password-input-config';
import { AccountPasswordComplexitySetting, AccountServiceProxy } from '@proxies/account-proxy';
import { finalize } from 'rxjs';

@Component({
    templateUrl: 'forgot-password-code.component.html',
    styleUrls: [
        'forgot-password-code.component.scss'
    ]
})
export class ForgotPasswordCodeComponent extends ViewComponent implements OnInit {

    private accountServiceProxy!: AccountServiceProxy;
    private activatedRoute!: ActivatedRoute;
    private formBuilder!: FormBuilder;

    forgotPasswordForm!: FormGroup;
    passwordSettings!: AccountPasswordComplexitySetting;
    emailAddress!: string;
    loaded!: boolean;

    get code(): AbstractControl {
        return this.forgotPasswordForm.controls['codeInput'];
    };

    get newPassword(): AbstractControl {
        return this.forgotPasswordForm.controls['newPasswordInput'];
    };

    get newPasswordRepeat(): AbstractControl {
        return this.forgotPasswordForm.controls['newPasswordRepeatInput'];
    };

    inputNewPassword: IPasswordInputConfig = {
        type: 'password',
        icon: 'eye-outline',
        toggle: () => {
            switch (this.inputNewPassword.type) {
                case 'text': {
                    this.inputNewPassword.type = 'password';
                    this.inputNewPassword.icon = 'eye-outline';
                    break;
                }
                case 'password': {
                    this.inputNewPassword.type = 'text';
                    this.inputNewPassword.icon = 'eye-off-outline';
                    break;
                }
            }
        }
    }

    inputRepeatPassword: IPasswordInputConfig = {
        type: 'password',
        icon: 'eye-outline',
        toggle: () => {
            switch (this.inputRepeatPassword.type) {
                case 'text': {
                    this.inputRepeatPassword.type = 'password';
                    this.inputRepeatPassword.icon = 'eye-outline';
                    break;
                }
                case 'password': {
                    this.inputRepeatPassword.type = 'text';
                    this.inputRepeatPassword.icon = 'eye-off-outline';
                    break;
                }
            }
        }
    }

    private disabled: boolean;

    constructor(_injector: Injector) {
        super(_injector);

        this.accountServiceProxy = _injector.get(AccountServiceProxy);
        this.activatedRoute = _injector.get(ActivatedRoute);
        this.formBuilder = _injector.get(FormBuilder);
    }

    ngOnInit(): void {
        this.activatedRoute.params.subscribe(async (params) => {

            const emailAddress: string = params["emailAddress"];

            if (isNullEmptyOrWhiteSpace(emailAddress)) {
                this.navigation.root('/account/login', 'back');
                return;
            }

            this.emailAddress = emailAddress;
            const loading = await this.loader.show();

            this.accountServiceProxy
                .getAccountPasswordComplexitySettings()
                .pipe(finalize(async () => loading.dismiss())).subscribe({
                    next: (response) => {
                        this.passwordSettings = response;

                        this.forgotPasswordForm = this.formBuilder.group({
                            'codeInput': ['', Validators.compose([Validators.required, Validators.minLength(6), Validators.maxLength(6)])],
                            'newPasswordInput': ['', Validators.compose([Validators.required, Validators.maxLength(64)])],
                            'newPasswordRepeatInput': ['', Validators.compose([Validators.required, Validators.maxLength(64)])],
                        }, {
                            validator: this.mustMatch('newPasswordInput', 'newPasswordRepeatInput')
                        });

                        this.loaded = true;
                    }
                });
        });
    }

    async continue(): Promise<void> {
        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        this.accountServiceProxy
            .resetPassword(this.emailAddress, this.code.value, this.newPassword.value)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            }))
            .subscribe({
                next: () => {
                    this.message.info('La contraseña de acceso se restableció de manera satisfactoria. Por favor inicie sesión nuevamente.', 'Aviso');
                    this.navigation.back('/account/login');
                }
            });
    }

    private mustMatch(controlName: string, matchingControlName: string) {
        return (formGroup: FormGroup) => {
            const control = formGroup.controls[controlName];
            const matchingControl = formGroup.controls[matchingControlName];
            const givenPassword = control.value;

            let errors = control.errors;
            let repeatErros = matchingControl.errors;

            if (control.value !== matchingControl.value) {
                if (!repeatErros)
                    repeatErros = {};
                repeatErros["mustMatch"] = true;
            }

            if (this.passwordSettings.requireDigit && givenPassword && !(/[0-9]/).test(givenPassword)) {
                if (!errors)
                    errors = {};
                errors["requireDigit"] = true;
            }

            if (this.passwordSettings.requireUppercase && givenPassword && !(/[A-Z]/).test(givenPassword)) {
                if (!errors)
                    errors = {};
                errors["requireUppercase"] = true;
            }

            if (this.passwordSettings.requireLowercase && givenPassword && !(/[a-z]/).test(givenPassword)) {
                if (!errors)
                    errors = {};
                errors["requireLowercase"] = true;
            }

            if (this.passwordSettings.requiredLength && givenPassword && givenPassword.length < this.passwordSettings.requiredLength) {
                if (!errors)
                    errors = {};
                errors["requiredLength"] = true;
            }

            // use upperCaseLetters
            if (this.passwordSettings.requireNonAlphanumeric && givenPassword && (/^[0-9a-zA-Z]+$/).test(givenPassword)) {
                if (!errors)
                    errors = {};
                errors["requireNonAlphanumeric"] = true;
            }

            control.setErrors(errors);
            matchingControl.setErrors(repeatErros);
        }
    }
}
