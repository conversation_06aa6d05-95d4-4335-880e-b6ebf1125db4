.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 1rem;
}

.card-title {
    color: #495057;
    font-weight: 600;
    font-size: 1rem;
}

.card-body {
    padding: 1.5rem;
}

.resource-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid #e9ecef;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
}

.form-label {
    color: #495057;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.fw-bold {
    font-weight: 600 !important;
}

.text-muted {
    color: #6c757d !important;
}

ion-badge {
    --padding-start: 8px;
    --padding-end: 8px;
    --padding-top: 4px;
    --padding-bottom: 4px;
    font-size: 0.75rem;
}

ion-icon {
    --ionicon-stroke-width: 32px;
}

.p-tabview {
    .p-tabview-nav {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        
        .p-tabview-nav-link {
            background: transparent;
            border: none;
            color: #495057;
            font-weight: 500;
            padding: 1rem 1.5rem;
            
            &:hover {
                background: rgba(0, 0, 0, 0.05);
            }
        }
        
        .p-highlight .p-tabview-nav-link {
            background: #fff;
            color: #007bff;
            border-bottom: 2px solid #007bff;
        }
    }
    
    .p-tabview-panels {
        background: #fff;
        padding: 1.5rem;
    }
}

ion-spinner {
    width: 32px;
    height: 32px;
}

// Estilos para las secciones de recursos
.text-primary {
    color: #007bff !important;
}

.text-secondary {
    color: #6c757d !important;
}

// Separación entre secciones
.mb-4 {
    margin-bottom: 1.5rem !important;
}

// Línea divisoria sutil entre videos y otros adjuntos
.mb-4::after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #dee2e6, transparent);
    margin-top: 1rem;
}

// Estilos para el wrapper de video
.video-wrapper {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease-in-out;

    &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
}

// Botón de marcar como visto
ion-button {
    --border-radius: 20px;

    &[disabled] {
        --opacity: 0.7;
    }
}

// Estilos para la evaluación
.evaluation-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
}

.questions-container {
    .question-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        border-left: 4px solid #007bff;

        .question-header {
            .question-text {
                font-size: 1.1rem;
                line-height: 1.5;
                color: #495057;
            }
        }

        .options-container {
            .form-check {
                padding: 0.5rem 0;

                .form-check-input {
                    margin-top: 0.25rem;
                }

                .form-check-label {
                    font-size: 1rem;
                    line-height: 1.4;
                    cursor: pointer;
                    padding-left: 0.5rem;

                    &:hover {
                        color: #007bff;
                    }
                }
            }
        }
    }
}

// Estilos para el mensaje de evaluación completada
.alert-success {
    background-color: #d1edff;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 1.5rem;

    .text-success {
        color: #155724 !important;
    }

    ion-icon {
        font-size: 3rem;
    }
}

// Estilos para el tab de satisfacción
.satisfaction-rating {
    .stars-container {
        .stars-wrapper {
            display: inline-flex;
            gap: 0.5rem;

            ion-icon {
                font-size: 2.5rem;
                cursor: pointer;
                transition: all 0.2s ease-in-out;

                &.star-filled {
                    color: #ffc107;
                    transform: scale(1.1);
                }

                &.star-empty {
                    color: #dee2e6;

                    &:hover {
                        color: #ffc107;
                        transform: scale(1.05);
                    }
                }
            }
        }
    }
}

.satisfaction-comments {
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        font-size: 1rem;
        line-height: 1.5;
        resize: vertical;

        &:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        &::placeholder {
            color: #6c757d;
            font-style: italic;
        }
    }

    .form-text {
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .p-tabview .p-tabview-panels {
        padding: 1rem;
    }
    
    .resource-card {
        margin-bottom: 1rem;
    }
}
