import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { MaintenanceFamilyDto, MaintenanceFamilyServiceProxy } from '@proxies/maintenance-family.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-family-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MaintenanceFamilyCreateEditComponent extends ViewComponent implements OnInit {

    private maintenanceFamilyServiceProxy: MaintenanceFamilyServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: MaintenanceFamilyDto = new MaintenanceFamilyDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get maintenanceFamilyName(): AbstractControl {
        return this.modalForm.controls['maintenanceFamilyNameInput'];
    };

    get maintenanceFamilyCode(): AbstractControl {
        return this.modalForm.controls['maintenanceFamilyCodeInput'];
    };
    
    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceFamilyServiceProxy = _injector.get(MaintenanceFamilyServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            maintenanceFamilyNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            maintenanceFamilyCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.maintenanceFamilyServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.maintenanceFamilyName.setValue(this.item.name);
                        this.maintenanceFamilyCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new MaintenanceFamilyDto();
        }
    }

    async save(): Promise<void> {

        this.item.name = this.maintenanceFamilyName.value;
        this.item.code = this.maintenanceFamilyCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre es obligatorio', 'Aviso');
            return;
        }

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.maintenanceFamilyServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.maintenanceFamilyServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}