import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { IntegrationServiceProxy, IntegrationAreaSigDto } from '@proxies/integration.proxy'
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class AreaDashboardComponent extends ViewComponent implements OnInit {

    private integrationServiceProxy: IntegrationServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    managementArray: IFilterOption<string>[];
    areas: IntegrationAreaSigDto[] = [];
    areasTable: IntegrationAreaSigDto[] = [];

    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
    }

    ngOnInit() {

    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilter();
            await this.loadData();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        let name = this.dataTable?.filters?.['AreaName']?.['value'];
        let managementcode = this.dataTable?.filters?.['ManagementCode']?.['value'];
        let take = this.table.getMaxResultCount(this.paginator, event);
        let skip = this.table.getSkipCount(this.paginator, event);

        this.areasTable = [...this.areas];
        if (name) this.areasTable = this.areasTable.filter(x => x.name.toLocaleLowerCase().includes(name.toLocaleLowerCase()));
        if (managementcode) this.areasTable = this.areasTable.filter(x => x.managementSigQuery?.code == managementcode);

        this.table.totalRecordsCount = this.areasTable.length;
        this.table.records = this.areasTable.splice(skip, take + skip);
        await loading.dismiss()
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    private async loadFilter(): Promise<void> {
        return await new Promise((resolve, reject) => {
            this.integrationServiceProxy
                .getAllManagementSig()
                .subscribe({
                    next: (response) => {
                        this.managementArray = response.items.map(p => {
                            return {
                                label: p.name,
                                value: p.code
                            };
                        });
                        resolve();
                    }, error: () => reject()
                });
        })
    }

    private async loadData(): Promise<void> {
        return await new Promise((resolve, reject) => {
            this.integrationServiceProxy
            .getAllAreaSig()
            .subscribe({
                next: (response) => {
                    this.areas = response.items
                    console.log(this.areas)
                    resolve();
                }, error: () => reject()
            });
        })
    }
}
