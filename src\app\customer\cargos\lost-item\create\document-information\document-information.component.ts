import { AfterContentInit, Component, Injector, Input, OnInit } from '@angular/core';
import { RegisterCharge, RegisterChargeView } from '@core/models/modelsSCA/register-charge';
import { ResourceScaService } from '@core/services/servicesSCA/resource-sca.service';
import { UploadResource } from '@core/utils/core.request';

@Component({
  selector: 'app-cargo-document-information',
  templateUrl: './document-information.component.html',
  styleUrls: ['./document-information.component.scss'],
})
export class CargoDocumentInformationComponent implements OnInit, AfterContentInit {

  constructor(_injector: Injector) {
    this.resourceService = _injector.get(ResourceScaService)
  }
  ngAfterContentInit(): void {
    console.log("DOCUMENT INFO", this.registerCharge)
  }

  public documentUploadResource: UploadResource
  public imageUploadResource: UploadResource

  private resourceService: ResourceScaService
  @Input() registerCharge: RegisterChargeView
  ngOnInit() {
    console.log("DOCUMENT INFO", this.registerCharge)
  }
  getResource(path: string): string {
    let resultado = this.resourceService.get(path);
    // console.log(resultado)
    return resultado;
  }
  onUploadImage(event: UploadResource): void {

    this.imageUploadResource = event;
  }

  onUploadDocument(event: UploadResource): void {
    this.documentUploadResource = event;
    //  fileName: request.file.name,
    //         size: request.size.toString(),
    //         extension: request.file.name.split(".").pop(),
    //         className: "",
    //         name: `${request.token}.${request.file.name.split(".").pop()}`,
    //         token: request.token,
    this.registerCharge.documents = {
      extension: event.file.name.split(".").pop(),
      className: "", fileName: event.file.name,
      name: event.file.name, size: event.size.toString(), resource: "", resourceFolder: "", sectionFolder: ""
    };
  }

  onRemoveUploadImage(): void {
    this.imageUploadResource = null;
  }

  onRemoveUploadDocument(): void {
    this.documentUploadResource = null;
  }
  onRemoveDocument(): void {
    this.registerCharge.documents = null;
  }
  showResource(path: string): void {
    // if (this.isOnPreview)
    //     return;

    // this.isOnPreview = true;
    // this.dialog.show({
    //     component: AppResourceFullScreenComponent,
    //     cssClass: 'transparent',
    //     componentProps: {
    //         resource: path
    //     }
    // })
    //     .then(() => this.isOnPreview = false)
    //     .catch(() => this.isOnPreview = false);
  }

}
