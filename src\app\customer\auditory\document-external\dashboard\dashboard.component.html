<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Documentos Externos
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button *ngIf="processControl" (click)="showList()" class="ion-option"
                color="primary" fill="solid">
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Reporte LMR
                </ion-label>
            </ion-button>

            <ion-button *ngIf="processControl" (click)="showReport()" class="ion-option"
                color="primary" fill="solid">
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Reporte LMDE
                </ion-label>
            </ion-button>

            <ion-button *ngIf="createItemButton" (click)="createItem()" class="ion-option"
                color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar Documento
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 100px; max-width: 100px; width: 100px">
                                    Acciones
                                </th>
                                <th style="min-width: 20px">
                                    ID
                                </th>
                                <th style="min-width: 100px">
                                    Nombre
                                </th>
                                <th style="min-width: 100px">
                                    Macro Proceso
                                </th>
                                <th style="min-width: 100px">
                                    Proceso
                                </th>
                                <th style="min-width: 60px">
                                    Edición
                                </th>
                                <th style="min-width: 50px">
                                    LMR
                                </th>
                                <th style="min-width: 100px">
                                    Fecha de Creación
                                </th>
                                <th style="min-width: 100px">
                                    Fecha de Actualización
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 80px; max-width: 80px; width: 80px">
                                </th>
                                <th style="min-width: 20px; width: 20px; max-width: 20px;">
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Name"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="MacroProcessId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="documentMacroProcessArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter field="ProcessId" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown [ngModel]="value" [options]="documentProcessArray"
                                                (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                                appendTo="body">
                                                <ng-template let-option pTemplate="item">
                                                    <span class="ml-1 mt-1">{{ option.label }}</span>
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                    <p-columnFilter type="text" field="Edition"></p-columnFilter>
                                </th>
                                <th style="min-width: 50px; width: 50px; max-width: 50px;">
                                    <p-columnFilter type="boolean" field="Ldr"></p-columnFilter>
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                </th>
                                <th style="min-width: 100px; width: 100px; max-width: 100px;">
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 80px; max-width: 80px; width: 80px">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        ID
                                    </span> {{record?.id}}
                                </td>
                                <td style="min-width: 200px">
                                    <span class="p-column-title">
                                        Name
                                    </span> {{record?.name?.toUpperCase()}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Macro Process
                                    </span> {{ getMacroProcess(record?.process?.macroprocessid) }}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Process
                                    </span> {{ getProcess(record?.processId) }}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        Edition
                                    </span> {{record?.edition?.toUpperCase()}}
                                </td>
                                <td style="min-width: 50px">
                                    <ion-badge color="success" *ngIf="record?.ldr">
                                        Si
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="!record?.ldr">
                                        No
                                    </ion-badge>
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        CreatedAt
                                    </span> {{record?.createdAt | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                                <td>
                                    <span class="p-column-title">
                                        LastModified
                                    </span> {{record?.lastModified | luxonFormat: 'dd/MM/yyyy HH:mm'}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>