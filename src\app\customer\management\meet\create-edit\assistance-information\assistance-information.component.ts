import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace, trim } from '@core/utils/tools';
import { MeetDto, MeetMeetAssistanceDto } from '@proxies/meet.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { MeetCreateEditAssistanceComponent } from './create-edit-assistance/create-edit-assistance.component';
import { DocumentTypeServiceProxy } from '@proxies/document-type.proxy';
import { IFilterOption } from '@core/models/filters';
import { SearchServiceProxy } from '@proxies/search.proxy';

@Component({
    selector: 'app-meet-assistance-information',
    templateUrl: 'assistance-information.component.html',
    styleUrls: [
        'assistance-information.component.scss'
    ]
})
export class MeetAssistanceInformationComponent extends ViewComponent implements OnInit {

    private documentTypeServiceProxy: DocumentTypeServiceProxy;
    private searchServiceProxy: SearchServiceProxy;

    @Input() meet!: MeetDto;
    @Input() completed!: boolean;
    
    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    documentTypeArray: IFilterOption<number>[];

    findCode: string = '';
    finding: boolean = false;

    private skipCount: number;
    private maxResultCount: number;

    constructor(_injector: Injector) {
        super(_injector);

        this.skipCount = 0;
        this.table.defaultRecordsCountPerPage = 5;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;

        this.documentTypeServiceProxy = _injector.get(DocumentTypeServiceProxy);
        this.searchServiceProxy = _injector.get(SearchServiceProxy);
    }

    ngOnInit(): void {
        this.documentTypeServiceProxy.getAll({ maxResultCount: 1000, skipCount: 0 }).subscribe({
            next: (response) => {
                this.documentTypeArray = response.items.map(p => {
                    return {
                        label: p.name,
                        value: p.id
                    };
                });
            }
        });

        this.meet.meetAssistances ??= [];
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    createItem(): void {
        this.dialog.showWithData<MeetMeetAssistanceDto>({
            component: MeetCreateEditAssistanceComponent,
            componentProps: {
                meet: this.meet,
                meetAssistance: new MeetMeetAssistanceDto()
            }
        }).then((response) => {
            if (response.data.result) {
                this.meet.meetAssistances ??= [];
                this.meet.meetAssistances.push(response.data.result);
                this.formatPagination(this.skipCount, this.maxResultCount);
            }
        });
    }

    editItem(item: MeetMeetAssistanceDto, index: number): void {
        this.dialog.showWithData<MeetMeetAssistanceDto>({
            component: MeetCreateEditAssistanceComponent,
            componentProps: {
                meet: this.meet,
                meetAssistance: new MeetMeetAssistanceDto().fromJS(item),
                index: index
            }
        }).then((response) => {
            if (response.data.result) {
                this.meet.meetAssistances[index] = response.data.result;
            }
        });
    }

    deleteItem(item: MeetMeetAssistanceDto) {
        this.message.confirm(`¿Estas seguro de eliminar la asistencia de "${(item.name || '').trim()}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                item.remove = true;
                this.formatPagination(this.skipCount, this.maxResultCount);
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {

        if (this.table.shouldResetPaging(event)) 
            this.paginator.changePage(0);        

        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    showActions(event: any, item: MeetMeetAssistanceDto, index: number) {
        
        this.popover.show(event, [
            {
                label: 'Editar',
                callback: () => this.editItem(item, index)
            },
            {
                label: 'Eliminar',
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async find(): Promise<void> {
        if (isNullEmptyOrWhiteSpace(this.findCode))
            return;
        if (this.finding)
            return;

        this.finding = true;

        const loading = await this.loader.show();

        this.searchServiceProxy
            .find(trim(this.findCode))
            .pipe(finalize(async () => {
                this.finding = false;
                await loading.dismiss();

                setTimeout(() => document.getElementById('MeetAssistanceCode')?.focus(), 250);
            })).subscribe({
                next: (response) => {

                    this.findCode = '';

                    if (response !== undefined && response !== null) {
                        this.meet.meetAssistances ??= [];

                        const index: number = this.meet.meetAssistances.findIndex(p => 
                            p.document == response.document && 
                            response.documentType?.id == p.documentType?.id
                        );

                        if (index == -1) {
                            const meetAssistance = new MeetMeetAssistanceDto().fromJS(response);

                            this.meet.meetAssistances.push(meetAssistance);
                            this.formatPagination(this.skipCount, this.maxResultCount);

                            this.notify.success('El usuario ha sido agregado exitosamente');

                        } else {
                            this.notify.warn('El usuario ya se encuentra agregado');
                        }
                    } else {
                        this.notify.error('El usuario ingresado no existe');
                    }
                }
            });
    }

    resetFilters(): void {
        this.dataTable?.clear();
        this.getData();
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        const documentType: IFilterOption<number>[] = this.dataTable?.filters?.['documentType']?.['value'];
        const isNullDocumentType: boolean = documentType === null || documentType === undefined || documentType.length == 0;
        const document: string = this.dataTable?.filters?.['document']?.['value'];
        const isNullDocument: boolean = isNullEmptyOrWhiteSpace(document);
        const name: string = this.dataTable?.filters?.['name']?.['value'];
        const isNullName: boolean = isNullEmptyOrWhiteSpace(name);
        const emailAddress: string = this.dataTable?.filters?.['emailAddress']?.['value'];
        const isNullEmailAddress: boolean = isNullEmptyOrWhiteSpace(emailAddress);
        const company: string = this.dataTable?.filters?.['company']?.['value'];
        const isNullCompany: boolean = isNullEmptyOrWhiteSpace(company);

        let values = this.meet.meetAssistances.filter(p => {
            return (isNullDocumentType || documentType.findIndex(d => d.value == p.documentType?.id) !== -1) &&
                (isNullDocument || (p.document || '').trim().toUpperCase().indexOf((document || '').toUpperCase().trim()) != -1) &&
                (isNullName || (p.name || '').toUpperCase().trim().indexOf((name || '').toUpperCase().trim()) != -1) &&
                (isNullEmailAddress || (p.emailAddress || '').toUpperCase().trim().indexOf((emailAddress || '').toUpperCase().trim()) != -1) &&
                (isNullCompany || (p.company || '').toUpperCase().trim().indexOf((company || '').toUpperCase().trim()) != -1);
        });

        this.table.records = [];

        for (let item of values) {
            item.isHidden = true;

            if (!item.remove) {

                if (index >= skipCount && result < maxResultCount) {
                    this.table.records.push(item);
                    result++;
                }

                index++;
            }
        }

        this.table.totalRecordsCount = index;
    }
}