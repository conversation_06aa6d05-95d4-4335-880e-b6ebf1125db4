import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceFamilyRoutingModule } from './maintenance-family.routing.module';
import { MaintenanceFamilyDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceFamilyCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    MaintenanceFamilyRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceFamilyDashboardComponent,
    MaintenanceFamilyCreateEditComponent
  ]
})
export class MaintenanceFamilyModule { }
