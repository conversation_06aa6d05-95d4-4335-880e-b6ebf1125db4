<div class="row">

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de proceso
        </label>
        <hr>
    </div>

    <div class="col-12 col-sm-12 col-md-6 col-lg-4 col-xl-3 col-xxl-3 mb-4">

        <div class="ion-input">
            <div class="input-control">

                <label for="OperationTaskProgramCode" class="form-label">
                    Código interno Tareo
                </label>

                <input type="text" class="form-control" id="OperationTaskProgramCode" name="OperationTaskProgramCode"
                    value="{{item?.code}}" [disabled]="true">

            </div>
        </div>

    </div>

    <div class="col-12 col-sm-12 col-md-6 col-lg-4 col-xl-3 col-xxl-3 mb-4">
        <app-date-input name="ProcessTime" label="Fecha (*)" [(value)]="operationTime" [disabled]="!editable" />
    </div>

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información del proyecto
        </label>
        <hr>
    </div>

    <div *ngIf="editable" class="col-12 mb-4">
        <div class="ion-input">
            <div class="input-control">
                <label class="form-label" for="OperationTaskProject">
                    Buscar proyecto (*)
                </label>
                <div class="input-group action">
                    <input (click)="showFindProject()" class="form-control rounded" id="OperationTaskProject"
                        name="OperationTaskProject" value="Presiona para buscar un proyecto" readonly />
                    <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                </div>
            </div>
        </div>
    </div>

    <ng-container *ngIf="item.operationTask">

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskCode" class="form-label">
                        Código interno proyecto
                    </label>

                    <input type="text" class="form-control" id="OperationTaskCode" name="OperationTaskCode"
                        value="{{item?.operationTask?.code}}" [disabled]="true">

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskProjectCode" class="form-label">
                        Código proyecto
                    </label>

                    <input type="text" class="form-control" id="OperationTaskProjectCode"
                        name="OperationTaskProjectCode" value="{{item?.operationTask?.projectCode}}" [disabled]="true">

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskProjectName" class="form-label">
                        Nombre
                    </label>

                    <textarea type="text" class="form-control" id="OperationTaskProjectName"
                        name="OperationTaskProjectName" value="{{item?.operationTask?.projectName}}" [disabled]="true"
                        rows="2"></textarea>

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskCustomer" class="form-label">
                        Cliente
                    </label>

                    <textarea type="text" class="form-control" id="OperationTaskCustomer" name="OperationTaskCustomer"
                        value="{{item?.operationTask?.customerCode}} {{item?.operationTask?.customerName}}"
                        [disabled]="true" rows="2"></textarea>

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskLocation" class="form-label">
                        Ubicación
                    </label>

                    <textarea type="text" class="form-control" id="OperationTaskLocation" name="OperationTaskLocation"
                        value="{{item?.operationTask?.locationName}}" [disabled]="true" rows="2"></textarea>

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskCenterName" class="form-label">
                        Centro logístico
                    </label>

                    <input type="text" class="form-control" id="OperationTaskCenterName" name="OperationTaskCenterName"
                        value="{{item?.operationTask?.centerName}}" [disabled]="true">

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskManagementName" class="form-label">
                        Gerencia
                    </label>

                    <input type="text" class="form-control" id="OperationTaskManagementName"
                        name="OperationTaskManagementName" value="{{item?.operationTask?.managementName}}"
                        [disabled]="true">

                </div>
            </div>
        </div>

        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-4">
            <div class="ion-input">
                <div class="input-control">

                    <label for="OperationTaskAreaName" class="form-label">
                        Área
                    </label>

                    <input type="text" class="form-control" id="OperationTaskAreaName" name="OperationTaskAreaName"
                        value="{{item?.operationTask?.areaName}}" [disabled]="true">

                </div>
            </div>
        </div>

    </ng-container>

</div>