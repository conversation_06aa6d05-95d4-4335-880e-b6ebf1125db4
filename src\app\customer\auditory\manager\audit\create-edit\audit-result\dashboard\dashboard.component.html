<div class="row">

    <div class="col-12 mb-3">
        <ion-toolbar>
            <ion-title class="fz-normal fw-bold">
                Hallazgos
            </ion-title>
            <ion-buttons slot="end">
                <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                    <ion-icon name="refresh"></ion-icon>
                </ion-button>
                <ion-button (click)="createItem()" class="ion-option" color="primary" fill="solid">
                    <ion-icon name="add"></ion-icon>
                    <ion-label class="fz-small d-none d-lg-inline-block">
                        Agregar Hallazgo
                    </ion-label>
                </ion-button>
            </ion-buttons>
        </ion-toolbar>
    </div>

    <div class="col-12">
        <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
            [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
            ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width: 100px; max-width: 100px; width: 100px">
                        Acciones
                    </th>
                    <th style="min-width: 20px">
                        ID
                    </th>
                    <th style="min-width: 100px">
                        Tipo
                    </th>
                    <th style="min-width: 100px">
                        Centros Logísticos
                    </th>
                    <th style="min-width: 100px">
                        Proceso
                    </th>
                    <th style="min-width: 100px">
                        Estado
                    </th>
                </tr>
                <tr>
                    <th style="min-width: 80px; max-width: 80px; width: 80px">
                    </th>
                    <th style="min-width: 20px; width: 20px; max-width: 20px;">
                    </th>
                    <th style="min-width: 100px; width: 100px; max-width: 100px;">
                        <p-columnFilter field="AuditoryResultTypeId" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown [ngModel]="value" [options]="auditoryResultTypeArray"
                                    (onChange)="filter($event.value)" placeholder="Todos" [showClear]="true"
                                    appendTo="body">
                                    <ng-template let-option pTemplate="item">
                                        <span class="ml-1 mt-1">{{ option.label }}</span>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 100px; width: 100px; max-width: 100px;">
                        <p-columnFilter field="LogisticCenterId" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-multiSelect [ngModel]="value" (onClear)="filter()" (onChange)="filter($event.value)"
                                    [options]="logisticCenterArray" [showHeader]="false" [showClear]="true"
                                    optionLabel="label" appendTo="body"></p-multiSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 100px; width: 100px; max-width: 100px;">
                        <p-columnFilter field="ProcessId" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown [ngModel]="value" [options]="processArray" (onChange)="filter($event.value)"
                                    placeholder="Todos" [showClear]="true" appendTo="body">
                                    <ng-template let-option pTemplate="item">
                                        <span class="ml-1 mt-1">{{ option.label }}</span>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th style="min-width: 100px; width: 100px; max-width: 100px;">
                        <p-columnFilter field="StatusId" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown [ngModel]="value" [options]="statusArray" (onChange)="filter($event.value)"
                                    placeholder="Todos" [showClear]="true" appendTo="body">
                                    <ng-template let-option pTemplate="item">
                                        <span class="ml-1 mt-1">{{ option.label }}</span>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr>
                    <td class="action-column" style="min-width: 80px; max-width: 80px; width: 80px">
                        <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                            fill="solid" size="small">
                            <ion-label class="fz-small">
                                Acciones
                            </ion-label>
                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                        </ion-button>
                    </td>
                    <td>
                        <span class="p-column-title">
                            ID
                        </span> {{record?.id}}
                    </td>
                    <td>
                        <span class="p-column-title">
                            Type
                        </span> {{ getType(record?.typeId) }}
                    </td>
                    <td style="min-width: 200px">
                        <span class="p-column-title">
                            LogisticCenters
                        </span> {{ getCenter(record?.logisticCenters) }}
                    </td>
                    <td>
                        <span class="p-column-title">
                            Process
                        </span> {{ getProcess(record?.subProcessId) }}
                    </td>
                    <td>
                        <span class="p-column-title">
                            Status
                        </span> {{ getStatus(record?.statusId) }}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
        <div class="primeng-paging-container">
            <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator (onPageChange)="getData($event)"
                [totalRecords]="table.totalRecordsCount" [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                [showCurrentPageReport]="true" dropdownAppendTo="body"
                currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
            </p-paginator>
        </div>
    </div>
</div>