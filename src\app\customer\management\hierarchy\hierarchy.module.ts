import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { ComponentModule } from '@components/component.module';
import { HierarchyRoutingModule } from './hierarchy.routing.module';
import { HierarchyDashboardComponent } from './dashboard/dashboard.component';
import { HierarchyCreateEditComponent } from './create-edit/create-edit.component';
import { HierarchyEmployeeHierarchyComponent } from './employee-hierarchy/employee-hierarchy.component';

@NgModule({
  imports: [
    HierarchyRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule,
    ComponentModule
  ],
  declarations: [
    HierarchyDashboardComponent,
    HierarchyCreateEditComponent,
    HierarchyEmployeeHierarchyComponent
  ]
})
export class HierarchyModule { }