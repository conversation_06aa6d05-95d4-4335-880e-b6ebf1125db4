import { Component, Injector, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AccountServiceProxy } from '@proxies/account-proxy';
import { finalize } from 'rxjs';

@Component({
    templateUrl: 'forgot-password.component.html',
    styleUrls: [
        'forgot-password.component.scss'
    ]
})
export class ForgotPasswordComponent extends ViewComponent {

    private accountServiceProxy!: AccountServiceProxy;
    private formBuilder!: FormBuilder;

    forgotPasswordForm!: FormGroup;

    get emailAddress(): AbstractControl {
        return this.forgotPasswordForm.controls['emailAdressInput'];
    };

    private disabled: boolean;

    constructor(_injector: Injector) {
        super(_injector);

        this.accountServiceProxy = _injector.get(AccountServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.forgotPasswordForm = this.formBuilder.group({
            'emailAdressInput': ['', Validators.compose([Validators.required, Validators.email, Validators.maxLength(64)])]
        });
    }

    async continue(): Promise<void> {
        if (this.disabled)
            return;

        this.disabled = true;

        const loading = await this.loader.show();
        const emailAddress: string = this.emailAddress.value;

        this.accountServiceProxy
            .sendResetPassword(emailAddress)
            .pipe(finalize(async () => {
                this.disabled = false;
                await loading.dismiss();
            })).subscribe({
                next: () => {
                    this.navigation.root(`/account/forgot-password/${emailAddress}`, 'forward');
                }
            });
    }
}