import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AuditoryDto, AuditoryServiceProxy } from '@proxies/auditory/manager/audit.proxy';
import { AuditCreateEditComponent } from '../create-edit/create-edit-component';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { IFilterOption } from '@core/models/filters';
import { IntegrationServiceProxy } from '@proxies/integration.proxy';

@Component({
    selector: 'app-audit-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class AuditDashboardComponent extends ViewComponent implements OnInit {

    private auditoryServiceProxy: AuditoryServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    auditoryTypeArray: IFilterOption<number>[];
    logisticCenterArray: IFilterOption<string>[];
    policyArray: IFilterOption<string>[];
    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.auditoryServiceProxy = _injector.get(AuditoryServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
    }

    ngOnInit() {
        
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: AuditCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: AuditoryDto): void {
        this.dialog.showWithData<boolean>({
            component: AuditCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    previewItem(role: AuditoryDto) {
        this.dialog.showWithData<boolean>({
            component: AuditCreateEditComponent,
            componentProps: {
                id: role.id,
                onlyview: true
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(document: AuditoryDto) {
        this.message.confirm(`¿Estas seguro de eliminar la auditoría "${document.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.auditoryServiceProxy
                    .delete(document.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado la auditoría satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.auditoryServiceProxy
            .getAll(
                this.dataTable?.filters?.['Code']?.['value'],
                this.dataTable?.filters?.['AuditoryTypeId']?.['value'],
                (<IFilterOption<number>[]>this.dataTable?.filters?.['LogisticCenterId']?.['value'])?.map(x => x.value) || [],
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['PolicyId']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: AuditoryDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                hide: false,
                callback: () => this.editItem(role)
            },
            {
                label: 'Visualizar',
                hide: false,
                callback: () => this.previewItem(role)
            },
            {
                label: 'Eliminar',
                hide: false,
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    private getFiltersAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.auditoryServiceProxy.getFilters().subscribe({
                next: (response) => {
                    this.policyArray = this.parseFilter(response.policies);
                    resolve()
                },
                error: (err) => reject(err)
            });
        });
    }

    private getCenters(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.integrationServiceProxy
                .getAllLogiticCenters()
                .subscribe({
                    next: (response) => {
                        this.logisticCenterArray = this.parseFilter(response.items);
                        resolve();
                    },
                    error: () => reject()
                })
        });
    }

    private async loadFilters(): Promise<void> {
        await Promise.all([
            this.getFiltersAsync(),
            this.getCenters()
        ]);
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }
    
    private parseFilterCode(data: any[]): any {
        return data.map(p => {
            return {
                label: p.code,
                value: p.id ?? p.code
            };
        });
    }

    getCenter(code: string): string {
        return this.logisticCenterArray.find(item => item.value === code)?.label || 'Todas';
    }

    getDates(item: AuditoryDto): string {
        return item?.dates?.map(x => x.date.toLocaleDateString()).join();
    }
}
