import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { BasicProtectionEquipmentDto, BasicProtectionEquipmentServiceProxy } from '@proxies/basic-protection-equipment.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { BasicProtectionEquipmentCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-basic-protection-equipment-dashboard.component',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class BasicProtectionEquipmentDashboardComponent extends ViewComponent {

    private basicProtectionEquipmentServiceProxy: BasicProtectionEquipmentServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    staticStates: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    constructor(_injector: Injector) {
        super(_injector);

        this.basicProtectionEquipmentServiceProxy = _injector.get(BasicProtectionEquipmentServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: BasicProtectionEquipmentCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: BasicProtectionEquipmentDto) {
        this.dialog.showWithData<boolean>({
            component: BasicProtectionEquipmentCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: BasicProtectionEquipmentDto) {
        this.message.confirm(`¿Estas seguro de eliminar el equipo de protección personal básico "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.basicProtectionEquipmentServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el equipo de protección personal básico satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.basicProtectionEquipmentServiceProxy
            .getAll(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['enabled']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: BasicProtectionEquipmentDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.BasicProtectionEquipment.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.BasicProtectionEquipment.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.basicProtectionEquipmentServiceProxy
            .export(
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['enabled']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}