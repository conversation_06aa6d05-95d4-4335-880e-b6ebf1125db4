<ion-header mode="md">
    <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Charlas
        </ion-title>

        <ion-buttons slot="end">

            <ion-button *ngIf="'Pages.Management.Meet' | permission" (click)="export()" class="ion-option me-2" color="success" fill="solid">
                <ion-icon name="cloud-download"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.Meet' | permission" (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.Meet' | permission" (click)="getData()" class="ion-option me-2" color="tertiary" fill="solid">
                <ion-icon name="search"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Management.Meet.Modify' | permission" (click)="createItem()" class="ion-option"
                color="primary" fill="solid">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar charla
                </ion-label>
            </ion-button>

        </ion-buttons>

    </ion-toolbar>
</ion-header>
<ion-content color="light">
    <ion-card>
        <ion-card-content>
            <ion-row>

                <ion-col size="12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Management.Meet.Modify',
                                        'Pages.Management.Meet.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    Acciones
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="Code">
                                    Código
                                    <p-sortIcon field="Code"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 200px; max-width: 200px; width: 200px"
                                    pSortableColumn="Center">
                                    Centro logístico
                                    <p-sortIcon field="Center"></p-sortIcon>
                                </th>
                                <th style="min-width: 250px;" pSortableColumn="Title">
                                    Tema
                                    <p-sortIcon field="Title"></p-sortIcon>
                                </th>
                                <th style="min-width: 250px;" pSortableColumn="Leader.Name,Leader.Surname,Leader.SecondSurname">
                                    Expositor
                                    <p-sortIcon field="Leader.Name,Leader.Surname,Leader.SecondSurname"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="Type">
                                    Tipo
                                    <p-sortIcon field="Type"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="Status">
                                    Estado
                                    <p-sortIcon field="Status"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="Turn">
                                    Turno
                                    <p-sortIcon field="Turn"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 180px; max-width: 180px; width: 180px" pSortableColumn="StartTime">
                                    Fecha
                                    <p-sortIcon field="StartTime"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px" pSortableColumn="StartTime">
                                    H. Inicio
                                    <p-sortIcon field="StartTime"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px" pSortableColumn="EndTime">
                                    H. Fin
                                    <p-sortIcon field="EndTime"></p-sortIcon>
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                                !(
                                    [
                                        'Pages.Management.Meet.Modify',
                                        'Pages.Management.Meet.Delete'
                                    ] | permissionAny
                                )
                                ">
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter type="text" field="code"></p-columnFilter>
                                </th>
                                <th style="min-width: 200px; max-width: 200px; width: 200px">
                                    <p-columnFilter field="centers" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="profitCenters"
                                                [showHeader]="false" [showClear]="true" placeholder="Todos"
                                                optionLabel="label" appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 250px">
                                    <p-columnFilter type="text" field="title"></p-columnFilter>
                                </th>
                                <th style="min-width: 250px">
                                    <p-columnFilter type="text" field="leader"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter field="types" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="meetTypeArray"
                                                [showHeader]="false" [showClear]="true" placeholder="Todos"
                                                optionLabel="label" appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter field="statuses" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="meetStatusArray"
                                                [showHeader]="false" [showClear]="true" placeholder="Todos"
                                                optionLabel="label" appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter field="turns" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-multiSelect [ngModel]="value" (onClear)="filter()"
                                                (onChange)="filter($event.value)" [options]="meetTurnArray"
                                                [showHeader]="false" [showClear]="true" placeholder="Todos"
                                                optionLabel="label" appendTo="body"></p-multiSelect>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 180px; max-width: 180px; width: 180px">
                                    <p-columnFilter field="startTime" [showMenu]="false">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <app-date-input name="startTime" [value]="value"
                                                [showLabel]="false" (onDateChange)="filter($event)" />
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px"></th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px"></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px"
                                    [hidden]="
                                !(
                                    [
                                        'Pages.Management.Meet.Modify',
                                        'Pages.Management.Meet.Delete'
                                    ] | permissionAny
                                )
                                ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.code}}
                                </td>
                                <td class="text-center" style="min-width: 200px; max-width: 200px; width: 200px">
                                    {{record?.center?.name}}
                                </td>
                                <td style="min-width: 250px;">
                                    {{record.title}}
                                </td>
                                <td style="min-width: 250px;">
                                    {{record.leader?.name}} {{record.leader?.surname}} {{record.leader?.secondSurname}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ion-badge color="primary" *ngIf="record.type == meetTypes.recurrent">
                                        De rutina
                                    </ion-badge>
                                    <ion-badge color="secondary" *ngIf="record.type == meetTypes.onDemand">
                                        A demanda
                                    </ion-badge>
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ion-badge color="tertiary" *ngIf="record.status == meetStatuses.programmed">
                                        Programado
                                    </ion-badge>
                                    <ion-badge color="success" *ngIf="record.status == meetStatuses.executed">
                                        Ejecutado
                                    </ion-badge>
                                    <ion-badge color="danger" *ngIf="record.status == meetStatuses.canceled">
                                        Cancelado
                                    </ion-badge>
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    <ion-badge color="warning" *ngIf="record.turn == meetTurns.diurnal">
                                        Diurno
                                    </ion-badge>
                                    <ion-badge color="dark" *ngIf="record.turn == meetTurns.nocturnal">
                                        Nocturno
                                    </ion-badge>
                                </td>
                                <td class="text-center" style="min-width: 180px; max-width: 180px; width: 180px">
                                    {{record?.startTime | luxonFormat: 'yyyy/MM/dd'}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record?.startTime | luxonFormat: 'HH:mm'}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record?.endTime | luxonFormat: 'HH:mm'}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </ion-col>

            </ion-row>
        </ion-card-content>
    </ion-card>
</ion-content>