import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { SubProcessCreateEditComponent } from '../create-edit/create-edit-component';
import { IFilterOption } from '@core/models/filters';
import { ProcessServiceProxy } from '@proxies/auditory/process.proxy';
import { SubProcessDto, SubProcessServiceProxy } from '@proxies/auditory/sub-process.proxy';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class SubProcessDashboardComponent extends ViewComponent implements OnInit {

    private subProcessServiceProxy: SubProcessServiceProxy;
    private processServiceProxy: ProcessServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    processArray: IFilterOption<string>[];

    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.processServiceProxy = _injector.get(ProcessServiceProxy);
        this.subProcessServiceProxy = _injector.get(SubProcessServiceProxy);
    }

    ngOnInit() {

    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: SubProcessCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: SubProcessDto): void {
        this.dialog.showWithData<boolean>({
            component: SubProcessCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(obj: SubProcessDto) {
        this.message.confirm(`¿Estas seguro eliminar el subproceso "${obj.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.subProcessServiceProxy
                    .delete(obj.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: (response) => {
                            if(response.IsSuccess) {
                                this.notify.success('Se ha eliminado satisfactoriamente', 5000);
                                this.getData();   
                            }
                            else {
                                this.notify.error('Ocurrio un error en la eliminación.', 5000);
                            }
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.subProcessServiceProxy
            .getAll(
                this.dataTable?.filters?.['Code']?.['value'],
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['ProcessId']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: SubProcessDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Auditory.SubProcess'
                ],
                callback: () => this.editItem(role)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Auditory.SubProcess'
                ],
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    private loadFilters(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.processServiceProxy.getAllFilter().subscribe({
                next: (response) => {
                    this.processArray = this.parseFilter(response.items);
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }
}
