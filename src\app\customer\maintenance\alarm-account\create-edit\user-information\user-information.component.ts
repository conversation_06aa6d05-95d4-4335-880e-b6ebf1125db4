import { Component, Injector, Input, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AlarmAccountDto } from '@proxies/alarm-account.proxy';
import { AlarmUserDto, AlarmUserServiceProxy } from '@proxies/alarm-user.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { AlarmUserCreateEditComponent } from './create-edit/create-edit.component';

@Component({
    selector: 'app-alarm-user-dashboard',
    templateUrl: 'user-information.component.html',
    styleUrls: [
        'user-information.component.scss'
    ]
})
export class AlarmUserInformationComponent extends ViewComponent {

    private alarmUserServiceProxy: AlarmUserServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    @Input() alarmAccount: AlarmAccountDto;

    constructor(_injector: Injector) {
        super(_injector);

        this.alarmUserServiceProxy = _injector.get(AlarmUserServiceProxy);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: AlarmUserCreateEditComponent,
            componentProps: {
                alarmAccount: this.alarmAccount
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: AlarmUserDto) {
        this.dialog.showWithData<boolean>({
            component: AlarmUserCreateEditComponent,
            componentProps: {
                alarmAccount: this.alarmAccount,
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: AlarmUserDto) {
        this.message.confirm(`¿Estas seguro de eliminar el usuario de alarma "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.alarmUserServiceProxy
                    .delete(this.alarmAccount.id, item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el usuario de alarma satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.alarmUserServiceProxy
            .getAll(
                this.alarmAccount.id,
                this.dataTable?.filters?.['name']?.['value'],
                this.dataTable?.filters?.['partition']?.['value'],
                this.dataTable?.filters?.['code']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: AlarmUserDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.AlarmUser.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.AlarmUser.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}