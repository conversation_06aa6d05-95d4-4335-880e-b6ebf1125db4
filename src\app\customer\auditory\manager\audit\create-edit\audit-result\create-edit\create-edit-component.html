<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? (onlyview ? 'Visualizar Hallazgo' : 'Editar Hallazgo') : 'Crear Hallazgo'"
        [disabled]="disabled">
        <app-modal-tabs>
            <p-tabView [scrollable]="true">
                <p-tabPanel header="Información General">
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <app-date-input [(value)]="_date" name="AuditoryResultDate" label="Fecha Real" />
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryResultCreatedAt" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryResultCreatedAt.invalid && (auditoryResultCreatedAt.touched || auditoryResultCreatedAt.dirty))}">
                                        Fecha de Creación
                                    </label>

                                    <input type="text" class="form-control" id="AuditoryResultCreatedAt"
                                        name="AuditoryResultCreatedAt" formControlName="auditoryResultCreatedAtInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryMacroProcess" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryMacroProcess.invalid && (auditoryMacroProcess.touched || auditoryMacroProcess.dirty))}">
                                        Macro Proceso
                                    </label>
                                    <select class="form-control" style="z-index: 1;" id="AuditoryMacroProcess"
                                        name="AuditoryMacroProcess" formControlName="auditoryMacroProcessSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let macroprocess of macroprocesses" [value]="macroprocess.id">
                                            {{macroprocess.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            Seleccione un Macro Proceso
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryProcess" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryProcess.invalid && (auditoryProcess.touched || auditoryProcess.dirty))}">
                                        Proceso
                                    </label>
                                    <select class="form-control" id="AuditoryProcess" name="AuditoryProcess"
                                        formControlName="auditoryProcessSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let process of processSelect" [value]="process.id">
                                            {{process.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            Seleccione un Proceso
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditorySubProcess" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditorySubProcess.invalid && (auditorySubProcess.touched || auditorySubProcess.dirty))}">
                                        SubProceso
                                    </label>
                                    <select class="form-control" id="AuditorySubProcess" name="AuditorySubProcess"
                                        formControlName="auditorySubProcessSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let process of subProcessSelect" [value]="process.id">
                                            {{process.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            Seleccione un SubProceso
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryResultLogisticCenter">
                                        Centro logístico (*)
                                    </label>
                                    <select class="form-control" id="AuditoryResultLogisticCenter"
                                        name="AuditoryResultLogisticCenter"
                                        formControlName="auditoryResultLogisticCenterSelect">
                                        <option value="-1">
                                            Todos
                                        </option>
                                        <option *ngFor="let center of centers" [value]="center.code">
                                            {{center.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryResultType"
                                        [ngClass]="{'ng-invalid' : (auditoryResultType.invalid && (auditoryResultType.touched || auditoryResultType.dirty))}">
                                        Tipo de Hallazgo (*)
                                    </label>

                                    <select class="form-control" id="AuditoryResultType" name="AuditoryResultType"
                                        formControlName="auditoryResultTypeSelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let type of types" [value]="type.id">
                                            {{type.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            El tipo de hallazgo es obligatorio.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryResultAction"
                                        [ngClass]="{'ng-invalid' : (auditoryResultAction.invalid && (auditoryResultAction.touched || auditoryResultAction.dirty))}">
                                        Tomar Acción (*)
                                    </label>

                                    <select class="form-control" id="AuditoryResultAction" name="AuditoryResultAction"
                                        formControlName="auditoryResultActionSelect">
                                        <option [value]="0">No</option>
                                        <option [value]="1">Sí</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" *ngIf="auditoryResultAction.value == '1'">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryResultPartners">
                                        Responsables del Proceso de auditoría
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployeePartner()" type="text"
                                            class="form-control rounded" id="AuditoryResultPartners"
                                            name="AuditoryResultPartners" formControlName="auditoryResultPartnersInput"
                                            readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryResultName" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryResultName.invalid && (auditoryResultName.touched || auditoryResultName.dirty))}">
                                        Descripción (*)
                                    </label>

                                    <input type="text" style="text-transform: uppercase;" class="form-control"
                                        id="AuditoryResultName" name="AuditoryResultName"
                                        formControlName="auditoryResultNameInput">

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            La descripción del hallazgo es obligatoria.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryResultPolicy"
                                        [ngClass]="{'ng-invalid' : (auditoryResultPolicy.invalid && (auditoryResultPolicy.touched || auditoryResultPolicy.dirty))}">
                                        Norma (*)
                                    </label>

                                    <select class="form-control" id="AuditoryResultPolicy" name="AuditoryResultPolicy"
                                        formControlName="auditoryResultPolicySelect">
                                        <option value="-1">
                                            Seleccione
                                        </option>
                                        <option *ngFor="let policy of policies" [value]="policy.id">
                                            {{policy.name}}
                                        </option>
                                    </select>

                                    <ion-row class="input-validation">
                                        <ion-col size="12">
                                            La norma de la auditoría es obligatoria.
                                        </ion-col>
                                    </ion-row>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <label class="form-label" for="AuditoryVerifier">
                                Requisitos
                            </label>
                            <ion-item>
                                <ion-select formControlName="auditoryRequerimentPolicySelect" multiple>
                                    <ion-select-option *ngFor="let option of requerimentsSelect" [value]="option.id">{{
                                        option.name }}</ion-select-option>
                                </ion-select>
                            </ion-item>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryVerifier">
                                        Auditor
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployeeVerifier()" type="text"
                                            class="form-control rounded" id="AuditoryVerifier" name="AuditoryVerifier"
                                            formControlName="auditoryResultVerifierInput" readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label for="AuditoryResultStatus" class="form-label"
                                        [ngClass]="{'ng-invalid' : (auditoryResultStatus.invalid && (auditoryResultStatus.touched || auditoryResultStatus.dirty))}">
                                        Estado
                                    </label>

                                    <input type="text" class="form-control" id="AuditoryResultStatus"
                                        name="AuditoryResultStatus" formControlName="auditoryResultStatusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryTrainingVerifier">
                                        Auditor en formación
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployeeTrainingVerifier()" type="text"
                                            class="form-control rounded" id="AuditoryTrainingVerifier"
                                            name="AuditoryTrainingVerifier"
                                            formControlName="auditoryResultTrainingVerifierInput" readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="ion-input mb-4">
                                <div class="input-control">
                                    <label class="form-label" for="AuditoryWatcher">
                                        Observador
                                    </label>
                                    <div class="input-group action">
                                        <input (click)="showFindEmployeeWatcher()" type="text"
                                            class="form-control rounded" id="AuditoryWatcher" name="AuditoryWatcher"
                                            formControlName="auditoryResultWatcherInput" readonly />
                                        <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
                <p-tabPanel header="Documentos Anexos">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <app-resource-preview *ngFor="let resource of item.attachments; index as i;"
                                [resource]="resource.path" [type]="resource.type" [icon]="resource.icon"
                                [name]="resource.fileName" [showRemove]="true" (onRemove)="onRemoveResource(i)">
                            </app-resource-preview>
                        </div>
                        <div class="col-12 mb-3">
                            <app-file-uploader [size]="size" [files]="true" [documents]="true"
                                (onUploadItem)="onUploadItem($event)">
                            </app-file-uploader>
                        </div>

                        <div *ngIf="uploadResources.length > 0" class="col-12 mb-3">
                            <label class="fz-normal fw-bold text-dark mb-0">
                                Recursos pendientes de publicación
                            </label>
                            <hr>
                        </div>

                        <div *ngFor="let resource of uploadResources; index as i;" class="col-12">
                            <app-file-preview [resource]="resource" [type]="resource.type"
                                (onRemove)="onRemoveUploadResource(i)">
                            </app-file-preview>
                        </div>
                    </div>
                </p-tabPanel>
            </p-tabView>
        </app-modal-tabs>
        <app-modal-footer [showSaveButton]="!onlyview" [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>