import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { InspectionTypeServiceProxy } from '@proxies/inspection-type.proxy';
import { InspectionDto, InspectionInspectionTypeDto, InspectionMode, InspectionServiceProxy } from '@proxies/inspection.proxy';
import { IntegrationCenterLogisticDto, IntegrationServiceProxy } from '@proxies/integration.proxy';
import { DateTime } from 'luxon';
import { finalize } from 'rxjs';

const enum InspectionIndexes {
    GeneralInformation,
    ModifiedInformation,
    HistoryInformation,
    OperationInformation
}

@Component({
    selector: 'app-inspection-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class InspectionCreateEditComponent extends ViewComponent implements OnInit {

    private inspectionServiceProxy: InspectionServiceProxy;
    private inspectionTypeServiceProxy: InspectionTypeServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id!: number;

    item!: InspectionDto;
    completed: boolean = false;

    inspectionDate!: Date;
    inspectionTime!: Date;
    modalForm!: FormGroup;
    disabled!: boolean;
    loaded!: boolean;
    activeIndex: InspectionIndexes = InspectionIndexes.GeneralInformation;

    centers!: IntegrationCenterLogisticDto[];
    inspectionTypes!: InspectionInspectionTypeDto[];

    inspectionModes = {
        none: InspectionMode.None,
        programed: InspectionMode.Programed,
        modifiedWithoutDate: InspectionMode.ModifiedWithoutDate,
        modified: InspectionMode.Modified,
        process: InspectionMode.Process,
        executed: InspectionMode.Executed,
        canceled: InspectionMode.Canceled,
        contract: InspectionMode.Contract,
        rejected: InspectionMode.Rejected,
        completed: InspectionMode.Completed
    };

    get inspectionType(): AbstractControl {
        return this.modalForm.controls['inspectionTypeSelect'];
    };

    get inspectionMode(): AbstractControl {
        return this.modalForm.controls['inspectionModeSelect'];
    };

    get inspectionRejectDescription(): AbstractControl {
        return this.modalForm.controls['inspectionRejectDescriptionInput'];
    };

    get inspectionCancelDescription(): AbstractControl {
        return this.modalForm.controls['inspectionCancelDescriptionInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.formBuilder = _injector.get(FormBuilder);
        this.inspectionServiceProxy = _injector.get(InspectionServiceProxy);
        this.inspectionTypeServiceProxy = _injector.get(InspectionTypeServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);

        this.modalForm = this.formBuilder.group({
            inspectionRejectDescriptionInput: [''],
            inspectionCancelDescriptionInput: [''],
            inspectionModeSelect: [{ value: this.inspectionModes.none, disabled: true }],
            inspectionTypeSelect: ['-1'],
        });

    }

    async ngOnInit(): Promise<void> {
        const loading = await this.loader.show();

        this.loadLogisticCenters(async () => {
            if (this.id) {
                this.inspectionServiceProxy
                    .get(this.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: (response) => {
                            this.item = response;
                            this.completed =
                                !(this.item.mode == InspectionMode.Programed ||
                                this.item.mode == InspectionMode.Modified ||
                                this.item.mode == InspectionMode.ModifiedWithoutDate);
                                
                            this.loadFormFields();
                            this.loaded = true;
                        },
                        error: () => this.dialog.dismiss()
                    });
            } else {
                this.item = new InspectionDto();
                this.item.mode = InspectionMode.Programed;

                this.loadFormFields();
                this.loaded = true;

                await loading.dismiss();
            }
        }, async () => await loading.dismiss());
    }

    async save(): Promise<void> {
        const inspectionDate: DateTime = DateTime.fromJSDate(this.inspectionDate);
        const inspectionTime: DateTime = DateTime.fromJSDate(this.inspectionTime);

        if (!this.item.id) {

            if (!inspectionDate.isValid) {
                this.message.info('La fecha de inspección es inválida', 'Aviso');
                return;
            }

            if (!inspectionTime.isValid) {
                this.message.info('La hora de inspección es inválida', 'Aviso');
                return;
            }

            this.item.inspectionTime = DateTime.fromObject({
                year: inspectionDate.year,
                month: inspectionDate.month,
                day: inspectionDate.day,
                hour: inspectionTime.hour,
                minute: inspectionTime.minute,
                second: 0,
                millisecond: 0
            });
        }

        const inspectionType: InspectionInspectionTypeDto = this.inspectionTypes.find(p => p.id == this.inspectionType.value);

        if (!inspectionType) {
            this.message.info('La bodega es obligatoria', 'Aviso');
            return;
        }
        if (!this.item.customer) {
            this.message.info('El cliente es obligatorio', 'Aviso');
            return;
        }
        if (!this.item.property) {
            this.message.info('La bodega es obligatoria', 'Aviso');
            return;
        }
        if (this.inspectionMode.value == this.inspectionModes.none) {
            this.message.info('El estado de la inspección es obligatorio', 'Aviso');
            return;
        }

        this.item.inspectionType = inspectionType;
        this.item.mode = this.inspectionMode.value;

        if (this.disabled)
            return;

        this.disabled = true;

        const loading = await this.loader.show();

        if (this.id) {
            this.inspectionServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss()
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro actualizado satisfactoriamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.inspectionServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss()
                })).subscribe({
                    next: () => {
                        this.notify.success('Registro creado satisfactoriamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }

    private loadFormFields(): void {
        if (this.item.inspectionTime?.isValid) {
            this.inspectionDate = this.item.inspectionTime.toJSDate();
            this.inspectionTime = this.item.inspectionTime.toJSDate();
        }

        if (this.item.inspectionType)
            this.inspectionType.setValue(this.item.inspectionType.id);

        this.inspectionMode.setValue(this.item.mode);
        this.inspectionRejectDescription.setValue(this.item.rejectedDescription);
        this.inspectionRejectDescription.disable();
        this.inspectionCancelDescription.setValue(this.item.cancelDescription);
        this.inspectionCancelDescription.disable();

        if (this.completed) {
            this.inspectionType.disable();
            this.inspectionMode.disable();
        }
    }

    private loadLogisticCenters(callback: () => void, exception: () => void): void {
        this.integrationServiceProxy
            .getAllLogiticCenters()
            .subscribe({
                next: (response) => {
                    this.centers = response.items;
                    this.loadInspectionTypes(callback, exception);
                },
                error: () => this.dialog.dismiss(false) && exception()
            });
    }

    private loadInspectionTypes(callback: () => void, exception: () => void): void {
        this.inspectionTypeServiceProxy
            .getAll(undefined, true, undefined, 'Name DESC', 1000, 0)
            .subscribe({
                next: (response) => {

                    this.inspectionTypes = response.items.map((p) => new InspectionInspectionTypeDto().fromJS(p));

                    callback();
                },
                error: () => this.dialog.dismiss(false) && exception()
            });
    }
}