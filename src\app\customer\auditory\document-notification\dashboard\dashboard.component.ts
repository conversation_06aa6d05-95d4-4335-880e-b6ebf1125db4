import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { DocumentNotificationServiceProxy, DocumentNotificationDto } from '@proxies/auditory/document-notification.proxy'
import { DocumentServiceProxy, DocumentFilterDto } from '@proxies/auditory/document.proxy'
import { IFilterOption } from '@core/models/filters';
import { DateTime } from 'luxon';
import { IntegrationServiceProxy, IntegrationEmployeeDto, IntegrationAreaSigDto, IntegrationManagementSigDto } from '@proxies/integration.proxy';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class DocumentNotificationDashboardComponent extends ViewComponent implements OnInit {

    private documentServiceProxy: DocumentServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private documentNotificationServiceProxy: DocumentNotificationServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    areaArray: IFilterOption<string>[];
    managementArray: IFilterOption<string>[];
    statusArray: IFilterOption<number>[];
    typeArray: IFilterOption<number>[];

    employees: IntegrationEmployeeDto[] = [];

    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.documentServiceProxy = _injector.get(DocumentServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.documentNotificationServiceProxy = _injector.get(DocumentNotificationServiceProxy);
    }

    ngOnInit() {

    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();


        let employeeNameFilter = this.dataTable?.filters?.['EmployeeName']?.['value']?.toLowerCase() || null;

        let filteredEmployee: string[] = this.employees
            .filter(ref => (ref.firstName + ' ' + ref.lastName).toLowerCase().includes(employeeNameFilter))
            .map(ref => ref.email);

        this.subscription?.unsubscribe();
        this.subscription = this.documentNotificationServiceProxy
            .getAll(
                this.dataTable?.filters?.['DocumentTypeId']?.['value'],
                this.dataTable?.filters?.['Code']?.['value'],
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['DocumentManagementId']?.['value'],
                this.dataTable?.filters?.['DocumentAreaId']?.['value'],
                this.dataTable?.filters?.['Version']?.['value'],
                filteredEmployee,
                this.dataTable?.filters?.['Email']?.['value'],
                DateTime.fromJSDate(this.dataTable?.filters?.['sendStartTime']?.['value']),
                DateTime.fromJSDate(this.dataTable?.filters?.['sendEndTime']?.['value']),
                DateTime.fromJSDate(this.dataTable?.filters?.['readStartTime']?.['value']),
                DateTime.fromJSDate(this.dataTable?.filters?.['readEndTime']?.['value']),
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    getName(record: DocumentNotificationDto): string {
        let employee = this.employees.find(source => source.email === record.email);
        return employee ? (employee.firstName + ' ' + employee.lastName) : 'Sin Nombre'
    }
    
    getArea(code: string): string {
        return this.areaArray.find(item => item.value === code)?.label || 'Desconocido';
    }
    
    getManagement(code: string): string {
        return this.managementArray.find(item => item.value === code)?.label || 'Desconocido';
    }

    private getFiltersAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.documentServiceProxy.getFilters().subscribe({
                next: (response) => {
                    this.statusArray = this.parseFilter(response.statuses);
                    this.typeArray = this.parseFilter(response.documentTypes);
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private getUsersAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllEmployees(null, null, 32687, 0).subscribe({
                next: (response) => {
                    this.employees = response.items;
                    resolve()
                },
                error: (err) => reject(err)
            });
        });
    }

    private getAreasAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllAreaSig().subscribe({
                next: (response) => {
                    this.areaArray = this.parseFilter(response.items);
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private getManagementAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllManagementSig().subscribe({
                next: (response) => {
                    this.managementArray = this.parseFilter(response.items);
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private async loadFilters(): Promise<void> {

        await Promise.all([
            this.getFiltersAsync(),
            this.getUsersAsync(),
            this.getAreasAsync(),
            this.getManagementAsync()
        ]);
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }
}
