<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar Sistema 1' : 'Crear Sistema 1'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceMacroSystemName" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceMacroSystemName.invalid && (maintenanceMacroSystemName.touched || maintenanceMacroSystemName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceMacroSystemName"
                                name="MaintenanceMacroSystemName" formControlName="maintenanceMacroSystemNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceMacroSystemCode" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceMacroSystemCode.invalid && (maintenanceMacroSystemCode.touched || maintenanceMacroSystemCode.dirty))}">
                                Código (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceMacroSystemCode"
                                name="MaintenanceMacroSystemCode" formControlName="maintenanceMacroSystemCodeInput"
                                maxlength="3">
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>