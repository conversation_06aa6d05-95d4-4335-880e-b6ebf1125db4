import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IntegrationServiceProxy } from '@proxies/integration.proxy';
import { AreaPositionDto, ProcessTierDto, Tier } from '@proxies/auditory/area-position.proxy'
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { IFilterOption } from '@core/models/filters';
import { UserServiceProxy, UserDto } from '@proxies/user.proxy';
import { DocumentExternalDto, DocumentExternalServiceProxy } from '@proxies/auditory/document-external.proxy';
import { DocumentExternalCreateEditComponent } from '../create-edit/create-edit-component';
import { AppDocumentPreviewComponent } from '@components/file-preview-pdf/file-preview-pdf.component';



@Component({
    selector: 'app-document-external-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class DocumentExternalDashboardComponent extends ViewComponent implements OnInit {

    private documentExternalServiceProxy: DocumentExternalServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    documentMacroProcessArray: IFilterOption<number>[];
    documentProcessArray: IFilterOption<number>[];

    processControl: boolean
    processTier: ProcessTierDto[] = [];
    createItemButton: boolean;
    filterLoaded: boolean;
    isOnPreview: boolean = false;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.documentExternalServiceProxy = _injector.get(DocumentExternalServiceProxy);
    }

    ngOnInit() {
        this.processControl = this.session?.processTier?.find(x => x.processid == 0) != null;
        this.processTier = this.session?.processTier;
        this.createItemButton = this.processControl || this.processTier?.find(x => x.tiers?.includes(Tier.Owner)) != null;
    }


    onPreviewFile(url: string): void {
        if (this.isOnPreview)
            return;

        this.isOnPreview = true;
        this.dialog.show({
            component: AppDocumentPreviewComponent,
            cssClass: 'transparent',
            componentProps: {
                url: url,
                fileType: 'pdf',
                disableRightClick: false
            }
        }).then(() => {
            this.isOnPreview = false
        }).catch(() => this.isOnPreview = false);
    }

    showList(): void {
        this.onPreviewFile(this.documentExternalServiceProxy
            .getList());
    }

    showReport(): void {
        this.onPreviewFile(this.documentExternalServiceProxy
            .getReport(
                this.session?.employee?.empPositionsQuery?.name ?? this.session?.user?.job,
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['MacroProcessId']?.['value'],
                this.dataTable?.filters?.['ProcessId']?.['value'],
                this.dataTable?.filters?.['AreaId']?.['value'],
                this.dataTable?.filters?.['ManagementId']?.['value'],
                this.dataTable?.filters?.['Edition']?.['value'],
                this.dataTable?.filters?.['Ldr']?.['value']))
    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: DocumentExternalCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: DocumentExternalDto): void {
        this.dialog.showWithData<boolean>({
            component: DocumentExternalCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    previewItem(role: DocumentExternalDto) {
        this.dialog.showWithData<boolean>({
            component: DocumentExternalCreateEditComponent,
            componentProps: {
                id: role.id,
                onlyview: true
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(document: DocumentExternalDto) {
        this.message.confirm(`¿Estas seguro de eliminar el documento "${document.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.documentExternalServiceProxy
                    .delete(document.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el documento satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        this.processControl = this.session?.processTier?.find(x => x.processid == 0) != null;
        this.processTier = this.session?.processTier;
        this.createItemButton = this.processControl || this.processTier?.find(x => x.tiers?.includes(Tier.Owner)) != null;
    
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.documentExternalServiceProxy
            .getAll(
                this.session?.employee?.empPositionsQuery?.name ?? this.session?.user?.job,
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['MacroProcessId']?.['value'],
                this.dataTable?.filters?.['ProcessId']?.['value'],
                this.dataTable?.filters?.['AreaId']?.['value'],
                this.dataTable?.filters?.['ManagementId']?.['value'],
                this.dataTable?.filters?.['Edition']?.['value'],
                this.dataTable?.filters?.['Ldr']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    hideEdit(role: DocumentExternalDto): boolean {
        let processtier = this.processTier?.find(x => x.processid == role.processId)

        return !processtier?.tiers?.includes(Tier.Owner) && !this.processControl;
    }

    showActions(event: any, role: DocumentExternalDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                hide: this.hideEdit(role),
                callback: () => this.editItem(role)
            },
            {
                label: 'Visualizar',
                hide: false,
                callback: () => this.previewItem(role)
            },
            {
                label: 'Eliminar',
                hide: !this.processControl,
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    getMacroProcess(code: number): string {
        return this.documentMacroProcessArray.find(item => item.value === code)?.label || 'Desconocido';
    }

    getProcess(code: number): string {
        return this.documentProcessArray.find(item => item.value === code)?.label || 'Desconocido';
    }

    private getFiltersAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.documentExternalServiceProxy.getFilters().subscribe({
                next: (response) => {
                    this.documentMacroProcessArray = this.parseFilter(response.macroprocess)
                    this.documentProcessArray = this.parseFilter(response.process)
                    resolve()
                },
                error: (err) => reject(err)
            });
        });
    }

    private async loadFilters(): Promise<void> {
        await Promise.all([
            this.getFiltersAsync()
        ]);
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }

    private parseFilterCode(data: any[]): any {
        return data.map(p => {
            return {
                label: p.code,
                value: p.id ?? p.code
            };
        });
    }
}
