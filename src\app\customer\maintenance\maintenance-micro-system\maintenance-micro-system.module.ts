import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MaintenanceMicroSystemRoutingModule } from './maintenance-micro-system.routing.module';
import { MaintenanceMicroSystemDashboardComponent } from './dashboard/dashboard.component';
import { MaintenanceMicroSystemCreateEditComponent } from './create-edit/create-edit.component';
import { MaintenanceMicroSystemManagementComponent } from './management/management.component';

@NgModule({
  imports: [
    MaintenanceMicroSystemRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    MaintenanceMicroSystemDashboardComponent,
    MaintenanceMicroSystemCreateEditComponent,
    MaintenanceMicroSystemManagementComponent
  ]
})
export class MaintenanceMicroSystemModule { }
