import { Component, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppFindPersonComponent } from '@components/find-person/find-person.component';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { IFilterOption } from '@core/models/filters';
import { isNullEmptyOrWhiteSpace } from '@core/utils/tools';
import { DocumentTypeServiceProxy } from '@proxies/document-type.proxy';
import { OperationTaskDto, OperationTaskPersonDto, OperationTaskPersonRelationDto } from '@proxies/operation-task.proxy';
import { PersonAreaServiceProxy } from '@proxies/person-area.proxy';
import { PersonDto } from '@proxies/person.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';

@Component({
    selector: 'app-operation-task-person-information',
    templateUrl: 'person-information.component.html',
    styleUrls: [
        'person-information.component.scss'
    ]
})
export class OperationTaskPersonInformationComponent extends ViewComponent implements OnInit {

    private readonly documentTypeServiceProxy: DocumentTypeServiceProxy;
    private readonly personAreaServiceProxy: PersonAreaServiceProxy;

    @Input() operationTask!: OperationTaskDto;
    @Input() modalForm!: FormGroup;
    @Input() editable!: boolean;

    @ViewChild('dataTable', { static: true }) dataTable!: Table;
    @ViewChild('paginator', { static: true }) paginator!: Paginator;

    documentTypeArray: IFilterOption<number>[];
    personAreaArray: IFilterOption<number>[];

    private skipCount: number;
    private maxResultCount: number;

    constructor(injector: Injector) {
        super(injector);

        this.documentTypeServiceProxy = injector.get(DocumentTypeServiceProxy);
        this.personAreaServiceProxy = injector.get(PersonAreaServiceProxy);

        this.skipCount = 0;
        this.table.defaultRecordsCountPerPage = 5;
        this.maxResultCount = this.table.defaultRecordsCountPerPage;
    }

    ngOnInit(): void {

        this.documentTypeServiceProxy.getAll({ maxResultCount: 1000, skipCount: 0 }).subscribe({
            next: (response) => {
                this.documentTypeArray = response.items.map(p => {
                    return {
                        label: p.name,
                        value: p.id
                    };
                });
            }
        });

        this.personAreaServiceProxy.getAll({ maxResultCount: 1000, skipCount: 0 }).subscribe({
            next: (response) => {
                this.personAreaArray = response.items.map(p => {
                    return {
                        label: p.name,
                        value: p.id
                    };
                });
            }
        });

        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    onAddPerson(): void {
        this.dialog.showWithData<PersonDto>({
            component: AppFindPersonComponent
        }).then(async (response) => {
            if (response.data.result) {
                const person: PersonDto = response.data.result;

                if (this.operationTask.operationTaskPersons.findIndex(p => !p.remove && p.person?.id === person.id) === -1) {

                    this.operationTask.operationTaskPersons.push(new OperationTaskPersonRelationDto({
                        person: new OperationTaskPersonDto().fromJS(person)
                    }));

                    this.formatPagination(this.skipCount, this.maxResultCount);
                }
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        this.maxResultCount = this.table.getMaxResultCount(this.paginator, event);
        this.skipCount = this.table.getSkipCount(this.paginator, event);
        this.formatPagination(this.skipCount, this.maxResultCount);
    }

    resetFilters(): void {
        this.dataTable?.clear();
        this.getData();
    }

    deleteItem(item: OperationTaskPersonRelationDto, index: number) {
        this.message.confirm(`¿Estas seguro de eliminar la persona de "${(item.person.name || '').trim()}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {

                if (item.id) {
                    item.remove = true;
                } else {
                    this.operationTask.operationTaskPersons.splice(index, 1);
                }

                this.formatPagination(this.skipCount, this.maxResultCount);
            }
        });
    }

    showActions(event: any, item: OperationTaskPersonRelationDto, index: number) {
        this.popover.show(event, [
            {
                label: 'Eliminar',
                callback: () => this.deleteItem(item, index)
            }
        ]);
    }

    private formatPagination(skipCount: number, maxResultCount: number) {
        let index: number = 0;
        let result: number = 0;

        const documentType: IFilterOption<number>[] = this.dataTable?.filters?.['documentType']?.['value'];
        const isNullDocumentType: boolean = documentType === null || documentType === undefined || documentType.length == 0;
        const personArea: IFilterOption<number>[] = this.dataTable?.filters?.['personArea']?.['value'];
        const isNullPersonArea: boolean = personArea === null || personArea === undefined || personArea.length == 0;
        const document: string = this.dataTable?.filters?.['document']?.['value'];
        const isNullDocument: boolean = isNullEmptyOrWhiteSpace(document);
        const name: string = this.dataTable?.filters?.['name']?.['value'];
        const isNullName: boolean = isNullEmptyOrWhiteSpace(name);
        const emailAddress: string = this.dataTable?.filters?.['emailAddress']?.['value'];
        const isNullEmailAddress: boolean = isNullEmptyOrWhiteSpace(emailAddress);
        const company: string = this.dataTable?.filters?.['company']?.['value'];
        const isNullCompany: boolean = isNullEmptyOrWhiteSpace(company);
        const area: string = this.dataTable?.filters?.['area']?.['value'];
        const isNullArea: boolean = isNullEmptyOrWhiteSpace(area);
        const job: string = this.dataTable?.filters?.['job']?.['value'];
        const isNullJob: boolean = isNullEmptyOrWhiteSpace(job);

        let values = this.operationTask.operationTaskPersons.filter((p) => (
            (isNullDocumentType || documentType.findIndex(d => d.value == p?.person?.documentType?.id) !== -1) &&
            (isNullPersonArea || personArea.findIndex(d => d.value == p?.person?.personArea?.id) !== -1) &&
            (isNullDocument || (p?.person?.document || '').trim().toUpperCase().indexOf((document || '').toUpperCase().trim()) != -1) &&
            (isNullName || (p?.person?.name || '').toUpperCase().trim().indexOf((name || '').toUpperCase().trim()) != -1) &&
            (isNullEmailAddress || (p?.person?.emailAddress || '').toUpperCase().trim().indexOf((emailAddress || '').toUpperCase().trim()) != -1) &&
            (isNullCompany || (p?.person?.company || '').toUpperCase().trim().indexOf((company || '').toUpperCase().trim()) != -1) &&
            (isNullArea || (p?.person?.area || '').toUpperCase().trim().indexOf((area || '').toUpperCase().trim()) != -1) &&
            (isNullJob || (p?.person?.job || '').toUpperCase().trim().indexOf((job || '').toUpperCase().trim()) != -1)
        ));

        this.table.records = [];

        for (let item of values) {
            item.isHidden = true;

            if (!item.remove) {

                if (index >= skipCount && result < maxResultCount) {
                    this.table.records.push(item);
                    result++;
                }

                index++;
            }
        }

        this.table.totalRecordsCount = index;
    }
}