import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { EmployeeRoutingModule } from './employee.routing.module';
import { EmployeeDashboardComponent } from './dashboard/dashboard.component';
import { EmployeeCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    EmployeeRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    EmployeeDashboardComponent,
    EmployeeCreateEditComponent
  ]
})
export class EmployeeModule { }
