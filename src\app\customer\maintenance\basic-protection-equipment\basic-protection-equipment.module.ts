import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { BasicProtectionEquipmentRoutingModule } from './basic-protection-equipment.routing.module';
import { BasicProtectionEquipmentDashboardComponent } from './dashboard/dashboard.component';
import { BasicProtectionEquipmentCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    BasicProtectionEquipmentRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    BasicProtectionEquipmentDashboardComponent,
    BasicProtectionEquipmentCreateEditComponent
  ]
})
export class BasicProtectionEquipmentModule { }
