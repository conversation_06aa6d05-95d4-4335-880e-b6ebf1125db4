import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { MaintenanceProgramParameterDto, MaintenanceProgramParameterServiceProxy, MaintenanceProgramParameterType } from '@proxies/maintenance-program-parameter.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { MaintenanceProgramParameterCreateEditComponent } from '../create-edit/create-edit.component';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-maintenance-program-parameter-dashboard.component',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class MaintenanceProgramParameterDashboardComponent extends ViewComponent {

    private readonly maintenanceProgramParameterServiceProxy: MaintenanceProgramParameterServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    staticStates: IFilterOption<boolean>[] = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    maintenanceProgramParameterTypeArray: IFilterOption<MaintenanceProgramParameterType>[] = [
        { label: 'Textual', value: MaintenanceProgramParameterType.Text },
        { label: 'Numérico', value: MaintenanceProgramParameterType.Numeric },
        { label: 'Rango', value: MaintenanceProgramParameterType.Range },
        { label: 'Fichero', value: MaintenanceProgramParameterType.File },
        { label: 'Imagen', value: MaintenanceProgramParameterType.Image },
    ];
    
    maintenanceProgramParameters = {
        text: MaintenanceProgramParameterType.Text,
        numeric: MaintenanceProgramParameterType.Numeric,
        range: MaintenanceProgramParameterType.Range,
        file: MaintenanceProgramParameterType.File,
        image: MaintenanceProgramParameterType.Image
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceProgramParameterServiceProxy = _injector.get(MaintenanceProgramParameterServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: MaintenanceProgramParameterCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(item: MaintenanceProgramParameterDto) {
        this.dialog.showWithData<boolean>({
            component: MaintenanceProgramParameterCreateEditComponent,
            componentProps: {
                id: item.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(item: MaintenanceProgramParameterDto) {
        this.message.confirm(`¿Estas seguro de eliminar el registro "${item.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.maintenanceProgramParameterServiceProxy
                    .delete(item.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el registro satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.maintenanceProgramParameterServiceProxy
            .getAll({
                name: this.dataTable?.filters?.['name']?.['value'],
                enabled: this.dataTable?.filters?.['enabled']?.['value'],
                type: this.dataTable?.filters?.['type']?.['value'],
                required: this.dataTable?.filters?.['required']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator, event),
                skipCount: this.table.getSkipCount(this.paginator, event)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: MaintenanceProgramParameterDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Maintenance.MaintenanceProgramParameter.Modify'
                ],
                callback: () => this.editItem(item)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Maintenance.MaintenanceProgramParameter.Delete'
                ],
                callback: () => this.deleteItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.maintenanceProgramParameterServiceProxy
            .export({
                name: this.dataTable?.filters?.['name']?.['value'],
                enabled: this.dataTable?.filters?.['enabled']?.['value'],
                type: this.dataTable?.filters?.['type']?.['value'],
                required: this.dataTable?.filters?.['required']?.['value'],
                code: this.dataTable?.filters?.['code']?.['value'],
                sorting: this.table.getSorting(this.dataTable),
                maxResultCount: this.table.getMaxResultCount(this.paginator),
                skipCount: this.table.getSkipCount(this.paginator)
            }).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}