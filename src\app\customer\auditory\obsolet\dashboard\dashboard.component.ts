import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { DocumentDto, DocumentFilterDto, DocumentServiceProxy, StatusDto, DocumentTypeDto, Status, DocumentStatusDto } from '@proxies/auditory/document.proxy';
import { AreaPositionDto, ProcessTierDto, Tier } from '@proxies/auditory/area-position.proxy'
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { IFilterOption } from '@core/models/filters';
import { AppAuditoryService } from '@core/services/auditory/auditory.service';
import { DocumentCreateEditComponent } from '../create-edit/create-edit-component';
import { UserServiceProxy, UserDto } from '@proxies/user.proxy';
import { IntegrationServiceProxy } from '@proxies/integration.proxy';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class DocumentDashboardComponent extends ViewComponent implements OnInit {

    private documentServiceProxy: DocumentServiceProxy;
    private integrationServiceProxy: IntegrationServiceProxy;
    private userServiceProxy: UserServiceProxy;
    private auditoryDocumentService: AppAuditoryService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    documentStatusArray: IFilterOption<number>[];
    documentTypesArray: IFilterOption<number>[];
    documentAreaArray: IFilterOption<string>[];
    documentManagementArray: IFilterOption<string>[];
    documentProcessArray: IFilterOption<number>[];

    processControl: boolean
    processTier: ProcessTierDto[] = [];
    areaPosition: AreaPositionDto;
    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.documentServiceProxy = _injector.get(DocumentServiceProxy);
        this.userServiceProxy = _injector.get(UserServiceProxy);
        this.integrationServiceProxy = _injector.get(IntegrationServiceProxy);
        this.auditoryDocumentService = _injector.get(AppAuditoryService);
    }

    ngOnInit() {

    }

    showActions(event: any, role: DocumentDto) {
        this.popover.show(event, [
            {
                label: 'Visualizar',
                hide: false,
                callback: () => this.previewItem(role)
            }
        ]);
    }

    previewItem(role: DocumentDto) {
        this.dialog.showWithData<boolean>({
            component: DocumentCreateEditComponent,
            componentProps: {
                id: role.id,
                onlyview: true
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }


    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.documentServiceProxy
            .getAll(
                this.session?.employee?.empPositionsQuery?.name ?? this.session?.user?.job,
                this.dataTable?.filters?.['DocumentTypeId']?.['value'],
                this.dataTable?.filters?.['Code']?.['value'],
                [Status.Obsolet],
                this.dataTable?.filters?.['Name']?.['value'],
                this.dataTable?.filters?.['DocumentProcessId']?.['value'],
                this.dataTable?.filters?.['DocumentAreaId']?.['value'],
                this.dataTable?.filters?.['Version']?.['value'],
                null,
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    getArea(code: string): string {
        return this.documentAreaArray.find(item => item.value === code)?.label || 'Desconocido';
    }
    
    getManagement(code: string): string {
        return this.documentManagementArray.find(item => item.value === code)?.label || 'Desconocido';
    }
    
    getProcess(code: number): string {
        return this.documentProcessArray.find(item => item.value === code)?.label || 'Desconocido';
    }

    private getFiltersAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.documentServiceProxy.getFilters().subscribe({
                next: (response) => {
                    this.documentStatusArray = this.parseFilter(response.statuses);
                    this.documentTypesArray = this.parseFilter(response.documentTypes);
                    this.documentProcessArray = this.parseFilterCode(response.process);
                    this.auditoryDocumentService.saveFilter(response);
                    resolve()
                },
                error: (err) => reject(err)
            });
        });
    }
    
    private getUsersAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.userServiceProxy.getAllFilter().subscribe({
                next: (response) => {
                    this.auditoryDocumentService.saveUsers(response.items.map(x => x.user));
                    resolve()
                },
                error: (err) => reject(err)
            });
        });
    }
    
    private getAreasAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllAreaSig().subscribe({
                next: (response) => {
                    this.documentAreaArray = this.parseFilter(response.items);
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private getManagementAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.integrationServiceProxy.getAllManagementSig().subscribe({
                next: (response) => {
                    this.documentManagementArray = this.parseFilter(response.items);
                    resolve();
                },
                error: (err) => reject(err)
            });
        });
    }

    private async loadFilters(): Promise<void> {
        await Promise.all([
            this.getUsersAsync(),
            this.getFiltersAsync(),
            this.getAreasAsync(),
            this.getManagementAsync()
        ]);
    }

    private parseFilter(data: any[]): any {
        return data.map(p => {
            return {
                label: p.name,
                value: p.id ?? p.code
            };
        });
    }

    private parseFilterCode(data: any[]): any {
        return data.map(p => {
            return {
                label: p.code,
                value: p.id ?? p.code
            };
        });
    }
}