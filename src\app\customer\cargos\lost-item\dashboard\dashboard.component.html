<ion-header mode="md">
     <ion-toolbar>
        <ion-title class="fz-normal fw-bold">
            Registro de Cargo
        </ion-title>

        <ion-buttons slot="end">

            <ion-button (click)="resetFilters()" *ngIf="'Pages.Cargos.LostItem' | permission" class="ion-option me-2" color="warning" fill="solid">
                <ion-icon name="refresh"></ion-icon>
            </ion-button>

            <ion-button (click)="getData()" *ngIf="'Pages.Cargos.LostItem' | permission"  class="ion-option me-2" color="tertiary" fill="solid">
                <ion-icon name="search"></ion-icon>
            </ion-button>

            <ion-button *ngIf="'Pages.Cargos.LostItem.Modify' | permission"  class="ion-option"
                color="primary" fill="solid" (click)="createItem()">
                <ion-icon name="add"></ion-icon>
                <ion-label class="fz-small d-none d-lg-inline-block">
                    Agregar <PERSON>
                </ion-label>
            </ion-button>
        </ion-buttons> 

    </ion-toolbar>
</ion-header>

<ion-content color="light">
  <ion-card>
    <ion-card-content>
      <ion-row>
        <ion-col size="12">
          <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
          (onSort)="getData()"
              [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
              ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
              <ng-template pTemplate="header">
                <tr>
                  <th style="min-width: 130px; max-width: 130px; width: 130px">
                      Acciones
                  </th>
                  <th style="min-width: 150px; width: 150px; max-width: 150px;" pSortableColumn="RegisterTime">
                      Fecha
                      <p-sortIcon field="RegisterTime"></p-sortIcon>
                  </th>
                  <th style="min-width: 200px; width: 200px; max-width: 200px;" pSortableColumn="Center.Name">
                      Centro logístico
                      <p-sortIcon field="Center.Name"></p-sortIcon>
                  </th>
                  <th style="min-width: 200px; width: 200px; max-width: 200px;" pSortableColumn="PersonEmisor.Name">
                      Nombre de Emisor
                      <p-sortIcon field="PersonEmisor.Name"></p-sortIcon>
                  </th>
                  <th style="min-width: 200px; width: 200px; max-width: 200px;" pSortableColumn="PersonReceptor.Name">
                      Nombre de Receptor
                      <p-sortIcon field="PersonReceptor.Name"></p-sortIcon>
                  </th>
                </tr>

                <tr>
                  <th style="min-width: 130px; max-width: 130px; width: 130px">
                  </th>
                  <th style="min-width: 150px; width: 150px; max-width: 150px;">
                  </th>
                  <th style="min-width: 200px; width: 200px; max-width: 200px;">
                      <p-columnFilter field="centerId" [showMenu]="false">
                          <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                              <p-dropdown 
                                [ngModel]="value" 
                                [options]="centerOptions"
                                (onChange)="filter($event.value)" 
                                placeholder="Todos" 
                                [showClear]="true"
                                appendTo="body">
                                  <ng-template let-option pTemplate="item">
                                      <span class="ml-1 mt-1">{{ option.label }}</span>
                                  </ng-template>
                              </p-dropdown>
                          </ng-template>
                      </p-columnFilter>
                  </th>
                  <th style="min-width: 200px; width: 200px; max-width: 200px;">
                    <p-columnFilter type="text" field="nameEmisor"></p-columnFilter>
                  </th>
                  <th style="min-width: 200px; width: 200px; max-width: 200px;">
                    <p-columnFilter type="text" field="nameReceptor"></p-columnFilter>
                  </th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                  <tr>
                    <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px">
                        <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                            fill="solid" size="small">
                            <ion-label class="fz-small">
                                Acciones
                            </ion-label>
                            <ion-icon name="chevron-down" slot="end"></ion-icon>
                        </ion-button>
                    </td>
                    <td style="min-width: 70px">
                          {{record?.fecha | date: 'dd/MM/yyyy'}}
                    </td>
                    <td style="min-width: 200px">
                          {{record?.centerLogistic?.name}}
                    </td>
                    <td style="min-width: 250px">
                          {{record?.usuarioEmisor?.fullName}}
                    </td>
                    <td style="min-width: 250px">
                          {{record?.usuarioReceptor?.fullName}}
                    </td>
                  </tr>
              </ng-template>
          </p-table>

          <div class="primeng-no-data" *ngIf="table.totalRecordsCount === 0">
              No hay data
          </div>
          <div class="primeng-paging-container">
              <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                  (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                  [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                  dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
              </p-paginator>
          </div>

        </ion-col>
      </ion-row>
    </ion-card-content>
  </ion-card>
</ion-content>