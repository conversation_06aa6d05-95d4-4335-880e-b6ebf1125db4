<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar área' : 'Crear área'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonAreaName" class="form-label"
                                [ngClass]="{'ng-invalid' : (personAreaName.invalid && (personAreaName.touched || personAreaName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="PersonAreaName" name="PersonAreaName"
                                formControlName="personAreaNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="PersonAreaHasMultipleOperationTaskPerDay">
                                ¿Puede ser registrado múltiples veces por día en tareo? (*)
                            </label>
                            <select class="form-control" id="PersonAreaHasMultipleOperationTaskPerDay"
                                name="PersonAreaHasMultipleOperationTaskPerDay"
                                formControlName="personAreaHasMultipleOperationTaskPerDaySelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonAreaCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="PersonAreaCode" name="PersonAreaCode"
                                formControlName="personAreaCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>