<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal
        [title]="loaded ? (item.status == monitoringAlarmStatuses.process ? '<PERSON> tratamie<PERSON> (' + (status == monitoringAlarmStatuses.completed ? 'Finalizar' : 'En curso') + ')' : '<PERSON> tratamiento') : 'Cargando...'"
        [disabled]="disabled" size="normal">
        <app-modal-body>
            <div class="row">

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MonitoringAlarmId" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="MonitoringAlarmId" name="MonitoringAlarmId"
                                formControlName="monitoringAlarmIdInput" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MonitoringAlarmType" class="form-label">
                                Evento
                            </label>

                            <input type="text" class="form-control" id="MonitoringAlarmType" name="MonitoringAlarmType"
                                formControlName="monitoringAlarmTypeInput" readonly>
                        </div>
                    </div>
                </div>

                <ng-container *ngIf="item && item.alarmTreatmentType?.hasReal">

                    <div class="col-12">
                        <div class="ion-input mb-4">
                            <div class="input-control">

                                <label for="MonitoringAlarmType" class="form-label">
                                    ¿Es real?
                                </label>

                                <div class="d-flex flex-row align-items-center">
                                    <span>
                                        No
                                    </span>
                                    <ion-toggle (ionChange)="onMonitoringIsRealChange($event)" class="mx-2" mode="ios"
                                        color="success" formControlName="monitoringAlarmIsRealSelect"></ion-toggle>
                                    <span>
                                        Si
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="ion-input mb-4">
                            <div class="input-control">
                                <label class="form-label" for="MonitoringAlarmTreatmentReason">
                                    Razones (*)
                                </label>
                                <select class="form-control" id="MonitoringAlarmTreatmentReason"
                                    name="MonitoringAlarmTreatmentReason"
                                    formControlName="monitoringAlarmTreatmentReasonSelect">
                                    <option value="-1">
                                        Seleccione
                                    </option>
                                    <option *ngFor="let alarmTreatmentReason of alarmTreatmentReasons"
                                        [value]="alarmTreatmentReason.id">
                                        {{alarmTreatmentReason.name}}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                </ng-container>

                <div *ngIf="item && item.alarmTreatmentType?.hasDescription" class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MonitoringDescription" class="form-label">
                                Detalle de atención
                            </label>

                            <textarea type="text" class="form-control" id="MonitoringDescription"
                                name="MonitoringDescription" formControlName="monitoringDescriptionInput"
                                rows="5"></textarea>

                            <div class="input-length">
                                {{monitoringDescription?.value?.length || 0}}/25000
                            </div>

                        </div>
                    </div>
                </div>

                <ng-container *ngIf="item && item.status == monitoringAlarmStatuses.process">
                    <div class="col-12 mb-3">
                        <label class="fz-normal fw-bold text-dark mb-0">
                            Recursos publicados
                        </label>
                        <hr>
                    </div>

                    <div *ngIf="item.alarmTreatmentResources.length == 0">
                        <label class="d-block text-center fz-normal text-dark mb-0">
                            Sin recursos publicados
                        </label>
                    </div>

                    <div class="col-12 mb-3">
                        <app-resource-preview
                            *ngFor="let alarmTreatmentResource of item.alarmTreatmentResources; index as i;"
                            [resource]="alarmTreatmentResource.path" [type]="alarmTreatmentResource.type"
                            [icon]="alarmTreatmentResource.icon" [name]="alarmTreatmentResource.name"
                            [info]="alarmTreatmentResource.size" [removed]="alarmTreatmentResource.remove"
                            (onRemove)="onRemoveResource(i)">
                        </app-resource-preview>
                    </div>
                </ng-container>

                <div class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Material y cumplimiento
                    </label>
                    <hr>
                </div>

                <div class="col-12 mb-3">
                    <app-file-uploader [size]="size" [images]="true" [files]="true"
                        (onUploadItem)="onUploadItem($event)">
                    </app-file-uploader>
                </div>

                <div class="col-12">
                    <div class="alert alert-warning" role="alert">
                        Ud. puede seleccionar un archivo WORD (doc, docx), PDF (.pdf), con un tamaño máximo de 15MB
                    </div>
                </div>

                <div *ngIf="uploadResources?.length > 0" class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Recursos pendientes de publicación
                    </label>
                    <hr>
                </div>

                <div *ngFor="let resource of uploadResources; index as i;" class="col-12">
                    <app-file-preview [resource]="resource" [type]="resource.type"
                        (onRemove)="onRemoveUploadResource(i)">
                    </app-file-preview>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" [saveButtonText]="status == monitoringAlarmStatuses.completed ? 'Finalizar' : 'Guardar'" (onSave)="save()" />
    </app-modal>
</form>