import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DocumentRoutingModule } from './document.routing.module';
import { DocumentDashboardComponent } from './dashboard/dashboard.component';
import { DocumentCreateEditComponent } from './create-edit/create-edit-component';
import { DocumentStatusUpdateComponent } from './create-edit/status-update/status-update-component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';
import { TagInputModule } from 'ngx-chips'

@NgModule({
  imports: [
    DocumentRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule,
    TagInputModule
  ],
  declarations: [
    DocumentDashboardComponent,
    DocumentCreateEditComponent,
    DocumentStatusUpdateComponent
  ]
})
export class DocumentModule { }