<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal title="Gestionar asistencia" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12 mb-2">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Información general
                    </label>
                    <hr>
                </div>

                <ng-container *ngIf="step == steps.find">

                    <div class="col-5 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="MeetDocumentTypeSearch">
                                Tipo de documento (*)
                            </label>
                            <select id="MeetDocumentTypeSearch" name="MeetDocumentTypeSearch"
                                class="form-control" formControlName="meetDocumentTypeSelect">
                                <option value="-1">
                                    Seleccione
                                </option>
                                <option *ngFor="let documentType of documentTypes" [value]="documentType.id">
                                    {{documentType.name}}
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="col-7 mb-4">

                        <div class="ion-input">
                            <div class="input-control">

                                <label for="MeetDocumentSearch" class="form-label"
                                    [ngClass]="{'ng-invalid' : (meetDocument.invalid && (meetDocument.touched || meetDocument.dirty))}">
                                    Nº Documento (*)
                                </label>

                                <input type="text" class="form-control" id="MeetDocumentSearch"
                                    name="MeetDocumentSearch" formControlName="meetDocumentInput" />

                                <ion-row class="input-validation">
                                    <ion-col size="12">
                                        El nº documento es obligatorio.
                                    </ion-col>
                                </ion-row>

                            </div>
                        </div>

                    </div>

                    <div class="col-12 text-end">
                        <ion-button [disabled]="disabled" (click)="search()" mode="ios">
                            <ion-icon name="search" class="ms-2 me-2"></ion-icon>
                            <ion-label>
                                Buscar
                            </ion-label>
                        </ion-button>
                    </div>

                </ng-container>

                <ng-container *ngIf="step == steps.typing">

                    <div class="col-5 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="MeetDocumentType">
                                Tipo de documento (*)
                            </label>
                            <select id="MeetDocumentType" name="MeetDocumentType" class="form-control"
                                formControlName="meetDocumentTypeSelect">
                                <option value="-1">
                                    Seleccione
                                </option>
                                <option *ngFor="let documentType of documentTypes" [value]="documentType.id">
                                    {{documentType.name}}
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="col-7 mb-4">

                        <div class="ion-input">
                            <div class="input-control">

                                <label for="MeetDocument" class="form-label"
                                    [ngClass]="{'ng-invalid' : (meetDocument.invalid && (meetDocument.touched || meetDocument.dirty))}">
                                    Nº Documento (*)
                                </label>

                                <input type="text" class="form-control" id="MeetDocument" name="MeetDocument"
                                    formControlName="meetDocumentInput" />

                                <ion-row class="input-validation">
                                    <ion-col size="12">
                                        El nº documento es obligatorio.
                                    </ion-col>
                                </ion-row>

                            </div>
                        </div>

                    </div>

                    <div class="col-12 mb-4">

                        <div class="ion-input">
                            <div class="input-control">

                                <label for="MeetName" class="form-label"
                                    [ngClass]="{'ng-invalid' : (meetName.invalid && (meetName.touched || meetName.dirty))}">
                                    Nombre (*)
                                </label>

                                <input type="text" class="form-control" id="MeetName" name="MeetName"
                                    formControlName="meetNameInput" />

                                <ion-row class="input-validation">
                                    <ion-col size="12">
                                        El tema es obligatorio.
                                    </ion-col>
                                </ion-row>

                                <div class="input-length">
                                    {{meetName?.value?.length || 0}}/255
                                </div>

                            </div>
                        </div>

                    </div>

                    <div class="col-6 mb-4">
                        <div class="ion-input">
                            <div class="input-control">

                                <label for="MeetEmailAddress" class="form-label"
                                    [ngClass]="{'ng-invalid' : (meetEmailAddress.invalid && (meetEmailAddress.touched || meetEmailAddress.dirty))}">
                                    Correo electrónico (*)
                                </label>

                                <input type="email" class="form-control" id="MeetEmailAddress" name="MeetEmailAddress"
                                    formControlName="meetEmailAddressInput" />

                                <ion-row class="input-validation">
                                    <ion-col size="12">
                                        {{
                                        meetEmailAddress.value ?
                                        'El correo electrónico es inválido.' :
                                        'El correo electrónico es obligatorio.'
                                        }}
                                    </ion-col>
                                </ion-row>

                                <div class="input-length">
                                    {{meetEmailAddress?.value?.length || 0}}/255
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="col-12 mb-4">

                        <div class="ion-input">
                            <div class="input-control">

                                <label for="MeetCompany" class="form-label"
                                    [ngClass]="{'ng-invalid' : (meetCompany.invalid && (meetCompany.touched || meetCompany.dirty))}">
                                    Empresa (*)
                                </label>

                                <input type="text" class="form-control" id="MeetCompany" name="MeetCompany"
                                    formControlName="meetCompanyInput" />

                                <ion-row class="input-validation">
                                    <ion-col size="12">
                                        La empresa es obligatoria
                                    </ion-col>
                                </ion-row>

                            </div>
                        </div>

                    </div>

                </ng-container>
            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>