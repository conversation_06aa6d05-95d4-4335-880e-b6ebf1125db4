import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AuditRoutingModule } from './audit.routing.module';
import { AuditDashboardComponent } from './dashboard/dashboard.component';
import { AuditCreateEditComponent } from './create-edit/create-edit-component';
import { ThemeModule } from '@theme/theme.module';
import { SharedModule } from '@shared/shared.module';
import { CoreModule } from '@core/core.module';
import { ComponentModule } from '@components/component.module';
import { AuditoryResultDashboardComponent } from './create-edit/audit-result/dashboard/dashboard.component';
import { AuditoryResultCreateEditComponent } from './create-edit/audit-result/create-edit/create-edit-component';

@NgModule({
  imports: [
    AuditRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ThemeModule,
    IonicModule,
    CoreModule,
    ComponentModule
  ],
  declarations: [
    AuditDashboardComponent,
    AuditCreateEditComponent,
    AuditoryResultDashboardComponent,
    AuditoryResultCreateEditComponent
  ]
})
export class AuditModule { }