<form class="h-100 w-100" [formGroup]="modalForm">
    <app-modal [title]="id ? 'Editar problema de mantenimiento correctivo' : 'Crear problema de mantenimiento correctivo'" [disabled]="disabled">
        <app-modal-body>
            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceActivityProblemName" class="form-label"
                                [ngClass]="{'ng-invalid' : (maintenanceActivityProblemName.invalid && (maintenanceActivityProblemName.touched || maintenanceActivityProblemName.dirty))}">
                                Nombre (*)
                            </label>

                            <input type="text" class="form-control" id="MaintenanceActivityProblemName"
                                name="MaintenanceActivityProblemName"
                                formControlName="maintenanceActivityProblemNameInput">

                            <ion-row class="input-validation">
                                <ion-col size="12">
                                    El nombre es obligatorio.
                                </ion-col>
                            </ion-row>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="MaintenanceActivityProblemEnabled">
                                ¿Habilitado? (*)
                            </label>
                            <select class="form-control" id="MaintenanceActivityProblemEnabled"
                                name="MaintenanceActivityProblemEnabled"
                                formControlName="maintenanceActivityProblemEnabledSelect">
                                <option value="true">
                                    Si
                                </option>
                                <option value="false">
                                    No
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div *ngIf="id" class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceActivityProblemCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="MaintenanceActivityProblemCode"
                                name="MaintenanceActivityProblemCode"
                                formControlName="maintenanceActivityProblemCodeInput" readonly>
                        </div>
                    </div>
                </div>

            </div>
        </app-modal-body>
        <app-modal-footer [disabled]="disabled" (onSave)="save()" />
    </app-modal>
</form>