import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { UserDto, UserMethodDto, UserServiceProxy } from '@proxies/user.proxy';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';
import { UserCreateEditComponent } from '../create-edit/create-edit-component';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-user-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class UserDashboardComponent extends ViewComponent implements OnInit {

    private userServiceProxy: UserServiceProxy;
    private fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    verifiedStates = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    activeStates = [
        { label: 'Si', value: true },
        { label: 'No', value: false }
    ];

    roles: IFilterOption<number>[];
    documentTypes: IFilterOption<number>[];

    constructor(_injector: Injector) {
        super(_injector);

        this.userServiceProxy = _injector.get(UserServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    ngOnInit(): void {
        this.userServiceProxy
            .getFilters()
            .subscribe({
                next: (response) => {
                    
                    this.roles = response.roles.map(p => {
                        return {
                            label: p.name,
                            value: p.id
                        };
                    });

                    this.documentTypes = response.documentTypes.map(p => {
                        return {
                            label: p.name,
                            value: p.id
                        };
                    });

                }
            });
    }

    createItem() {
        this.dialog.showWithData<boolean>({
            component: UserCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(user: UserDto) {
        this.dialog.showWithData<boolean>({
            component: UserCreateEditComponent,
            componentProps: {
                id: user.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(user: UserDto) {
        this.message.confirm(`¿Estas seguro de eliminar el rol "${user.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.userServiceProxy
                    .delete(user.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado el rol satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent) {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.userServiceProxy
            .getAll(
                this.dataTable?.filters?.['documentType']?.['value'],
                this.dataTable?.filters?.['document']?.['value'],
                this.dataTable?.filters?.['username']?.['value'],
                this.dataTable?.filters?.['names']?.['value'],
                this.dataTable?.filters?.['surname']?.['value'],
                this.dataTable?.filters?.['secondSurname']?.['value'],
                this.dataTable?.filters?.['emailAddress']?.['value'],
                this.dataTable?.filters?.['isEmailConfirmed']?.['value'],
                this.dataTable?.filters?.['phoneNumber']?.['value'],
                this.dataTable?.filters?.['job']?.['value'],
                this.dataTable?.filters?.['role']?.['value'],
                this.dataTable?.filters?.['isActive']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, data: UserMethodDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Administration.User.Modify'
                ],
                callback: () => this.editItem(data.user)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Administration.User.Delete'
                ],
                callback: () => this.deleteItem(data.user)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.userServiceProxy
            .export(
                this.dataTable?.filters?.['documentType']?.['value'],
                this.dataTable?.filters?.['document']?.['value'],
                this.dataTable?.filters?.['username']?.['value'],
                this.dataTable?.filters?.['names']?.['value'],
                this.dataTable?.filters?.['surname']?.['value'],
                this.dataTable?.filters?.['secondSurname']?.['value'],
                this.dataTable?.filters?.['emailAddress']?.['value'],
                this.dataTable?.filters?.['isEmailConfirmed']?.['value'],
                this.dataTable?.filters?.['phoneNumber']?.['value'],
                this.dataTable?.filters?.['job']?.['value'],
                this.dataTable?.filters?.['role']?.['value'],
                this.dataTable?.filters?.['isActive']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator),
                this.table.getSkipCount(this.paginator))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters() {
        this.dataTable?.clear();
    }
}