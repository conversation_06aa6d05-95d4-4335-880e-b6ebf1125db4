<div class="h-100 w-100">
    <app-modal [title]="editable ? (index ? 'Editar persona' : 'Crear persona') : 'Visualizar persona'" size="normal">
        <app-modal-body>

            <div class="row">

                <div class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Información de ejecución
                    </label>
                    <hr>
                </div>

                <div class="col-sm-12 col-md-6 col-lg-4 col-xl-4 col-xxl-4 mb-4">
                    <app-time-input name="OperationTaskProgramDetailStartTime" label="Hora inicio" [(value)]="startTime"
                        [disabled]="!editable" />
                </div>

                <div class="col-sm-12 col-md-6 col-lg-4 col-xl-4 col-xxl-4 mb-4">
                    <app-time-input name="OperationTaskProgramDetailStartTime" label="Hora fin" [(value)]="endTime"
                        [disabled]="!editable" />
                </div>

                <div class="col-12 mb-3">
                    <label class="fz-normal fw-bold text-dark mb-0">
                        Información de la persona
                    </label>
                    <hr>
                </div>
                
                <div *ngIf="editable" class="col-12 mb-4">
                    <div class="ion-input">
                        <div class="input-control">
                            <label class="form-label" for="OperationTaskDetailPerson">
                                Buscar persona (*)
                            </label>
                            <div class="input-group action">
                                <input (click)="onAddPerson()" class="form-control rounded"
                                    id="OperationTaskDetailPerson" name="OperationTaskDetailPerson"
                                    value="Presiona para buscar una persona" readonly />
                                <ion-icon class="input-group-icon-right" name="search"></ion-icon>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div *ngIf="detail.person" class="row">

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">
                            <label class="form-label" for="PersonDocumentType">
                                Tipo de documento
                            </label>
                            <input type="text" class="form-control" id="PersonDocumentType" name="PersonDocumentType"
                                value="{{detail.person.documentType?.name}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonDocument" class="form-label">
                                Documento
                            </label>

                            <input type="text" class="form-control" id="PersonDocument" name="PersonDocument"
                                value="{{detail.person.document}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonName" class="form-label">
                                Nombres y apellidos (*)
                            </label>

                            <input type="text" class="form-control" id="PersonName" name="PersonName"
                                value="{{detail.person.name}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonEmailAddress" class="form-label">
                                Correo electrónico
                            </label>

                            <input type="text" class="form-control" id="PersonEmailAddress" name="PersonEmailAddress"
                                value="{{detail.person.emailAddress}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonPhoneNumber" class="form-label">
                                Teléfono
                            </label>

                            <input type="text" class="form-control" id="PersonPhoneNumber" name="PersonPhoneNumber"
                                value="{{detail.person.phoneNumber}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonCompany" class="form-label">
                                Empresa
                            </label>

                            <input type="text" class="form-control" id="PersonCompany" name="PersonCompany"
                                value="{{detail.person.company}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonArea" class="form-label">
                                Área
                            </label>

                            <input type="text" class="form-control" id="PersonArea" name="PersonArea"
                                value="{{detail.person.area}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="PersonJob" class="form-label">
                                Cargo
                            </label>

                            <input type="text" class="form-control" id="PersonJob" name="PersonJob"
                                value="{{detail.person.job}}" readonly>
                        </div>
                    </div>
                </div>

            </div>

        </app-modal-body>
        <app-modal-footer [showSaveButton]="editable" (onSave)="save()" />
    </app-modal>
</div>