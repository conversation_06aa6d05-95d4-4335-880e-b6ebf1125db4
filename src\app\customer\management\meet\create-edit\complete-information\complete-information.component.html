<form [formGroup]="modalForm" class="row">

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de ejecución de la charla
        </label>
        <hr>
    </div>

    <div class="col-6 mb-4">
        <app-date-input name="MeetCompleteDate" label="Fecha de operación" [(value)]="meetCompleteDate" [disabled]="true" />
    </div> 

    <div class="col-6 mb-4">
        <app-time-input name="MeetCompleteTime" label="Hora de operación" [(value)]="meetCompleteTime" [disabled]="true" />
    </div>

    <div class="col-12 mb-4">

        <div class="ion-input">
            <div class="input-control">

                <label for="MeetCompleteDescription" class="form-label">
                    Comentarios
                </label>

                <textarea type="text" class="form-control" id="MeetCompleteDescription" name="MeetCompleteDescription"
                    formControlName="meetCompleteDescriptionInput" readonly></textarea>

            </div>
        </div>

    </div>

    <div *ngIf="meet?.meetCompleteResources?.length > 0" class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Recursos publicados
        </label>
        <hr>
    </div>

    <div class="col-12 mb-3">
        <app-resource-preview *ngFor="let meetCompleteResource of meet.meetCompleteResources; index as i;"
            [resource]="meetCompleteResource.path" [type]="meetCompleteResource.type" [icon]="meetCompleteResource.icon"
            [name]="meetCompleteResource.name" [info]="meetCompleteResource.size"
            [removed]="meetCompleteResource.remove" [showRemove]="false">
        </app-resource-preview>
    </div>
</form>