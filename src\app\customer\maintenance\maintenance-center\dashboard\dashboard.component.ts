import { Component, Injector, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { AppFileDownloadService } from '@core/services/file-download.service';
import { MaintenanceCenterDto, MaintenanceCenterServiceProxy } from '@proxies/maintenance-center.proxy';
import { Paginator } from 'primeng/paginator';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-center-dashboard.component',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})
export class MaintenanceCenterDashboardComponent extends ViewComponent {

    private readonly maintenanceCenterServiceProxy: MaintenanceCenterServiceProxy;
    private readonly fileDownloadService: AppFileDownloadService;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceCenterServiceProxy = _injector.get(MaintenanceCenterServiceProxy);
        this.fileDownloadService = _injector.get(AppFileDownloadService);
    }

    managementItem(item: MaintenanceCenterDto): void {
        this.navigation.forward('/customer/maintenance/maintenance-centers/management/' + item.center.code);
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.maintenanceCenterServiceProxy
            .getAll({}).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, item: MaintenanceCenterDto): void {
        this.popover.show(event, [
            {
                label: 'Asignar área de ubicación',
                permissions: [
                    'Pages.Maintenance.MaintenanceCenter.Path'
                ],
                callback: () => this.managementItem(item)
            }
        ]);
    }

    async export(): Promise<void> {
        const loading = await this.loader.show();

        this.maintenanceCenterServiceProxy
            .export({}).pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.fileDownloadService.download(result);
                }
            });
    }

    resetFilters(): void {
        this.dataTable?.clear();
    }
}