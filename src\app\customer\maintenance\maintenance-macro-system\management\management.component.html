<div class="h-100 w-100">
    <app-modal title="Gestionar Sistemas 2" size="large">
        <app-modal-body>

            <div class="row">

                <div class="col-12">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceMacroSystemName" class="form-label">
                                Nombre
                            </label>

                            <input type="text" class="form-control" id="MaintenanceMacroSystemName"
                                name="MaintenanceMacroSystemName" value="{{item?.name}}" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="ion-input mb-4">
                        <div class="input-control">

                            <label for="MaintenanceMacroSystemCode" class="form-label">
                                Código
                            </label>

                            <input type="text" class="form-control" id="MaintenanceMacroSystemCode"
                                name="MaintenanceMacroSystemCode" value="{{item?.code}}" readonly>
                        </div>
                    </div>
                </div>

            </div>

            <div class="row">

                <div class="col-12 text-end mb-4">
                    <ion-button (click)="resetFilters()" class="ion-option me-2" color="warning" fill="solid">
                        <ion-icon name="refresh"></ion-icon>
                    </ion-button>
        
                    <ion-button (click)="export()" class="ion-option me-2" color="success" fill="solid">
                        <ion-icon name="cloud-download"></ion-icon>
                    </ion-button>
                    
                    <ion-button *ngIf="'Pages.Maintenance.MaintenanceMicroSystem.Modify' | permission" (click)="createItem()" class="ion-option" color="primary" fill="solid">
                        <ion-icon name="add"></ion-icon>
                        <ion-label class="fz-small d-none d-lg-inline-block">
                            Agregar Sistema 2
                        </ion-label>
                    </ion-button>
        
                </div>

                <div class="col-12">
                    <p-table #dataTable sortMode="multiple" (onLazyLoad)="getData($event)" [value]="table.records"
                        [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
                        ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                            !(
                                [
                                    'Pages.Maintenance.MaintenanceMicroSystem.Modify',
                                    'Pages.Maintenance.MaintenanceMicroSystem.Delete'
                                ] | permissionAny
                            )
                            ">
                                    Acciones
                                </th>
                                <th style="min-width: 200px" pSortableColumn="Name">
                                    Nombre
                                    <p-sortIcon field="Name"></p-sortIcon>
                                </th>
                                <th class="text-center" style="min-width: 150px; max-width: 150px; width: 150px"
                                    pSortableColumn="Code">
                                    Código
                                    <p-sortIcon field="Code"></p-sortIcon>
                                </th>
                            </tr>
                            <tr>
                                <th style="min-width: 130px; max-width: 130px; width: 130px" [hidden]="
                            !(
                                [
                                    'Pages.Maintenance.MaintenanceMicroSystem.Modify',
                                    'Pages.Maintenance.MaintenanceMicroSystem.Delete'
                                ] | permissionAny
                            )
                            ">
                                </th>
                                <th style="min-width: 200px">
                                    <p-columnFilter type="text" field="name"></p-columnFilter>
                                </th>
                                <th style="min-width: 150px; max-width: 150px; width: 150px">
                                    <p-columnFilter type="text" field="code"></p-columnFilter>
                                </th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                            <tr>
                                <td class="action-column" style="min-width: 130px; max-width: 130px; width: 130px"
                                    [hidden]="
                            !(
                                [
                                    'Pages.Maintenance.MaintenanceMicroSystem.Modify',
                                    'Pages.Maintenance.MaintenanceMicroSystem.Delete'
                                ] | permissionAny
                            )
                            ">
                                    <ion-button (click)="showActions($event, record)" class="ion-action" color="primary"
                                        fill="solid" size="small">
                                        <ion-label class="fz-small">
                                            Acciones
                                        </ion-label>
                                        <ion-icon name="chevron-down" slot="end"></ion-icon>
                                    </ion-button>
                                </td>
                                <td style="min-width: 200px">
                                    {{record.name}}
                                </td>
                                <td class="text-center" style="min-width: 150px; max-width: 150px; width: 150px">
                                    {{record.code}}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
                        No hay data
                    </div>
                    <div class="primeng-paging-container">
                        <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator
                            (onPageChange)="getData($event)" [totalRecords]="table.totalRecordsCount"
                            [rowsPerPageOptions]="table.predefinedRecordsCountPerPage" [showCurrentPageReport]="true"
                            dropdownAppendTo="body" currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
                        </p-paginator>
                    </div>
                </div>
            </div>
        </app-modal-body>
        <app-modal-footer [showSaveButton]="false" />
    </app-modal>
</div>