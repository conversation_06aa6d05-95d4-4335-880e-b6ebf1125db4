import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ThemeModule } from '@theme/theme.module';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { TrainingTypeRoutingModule } from './training-type.routing.module';
import { TrainingTypeDashboardComponent } from './dashboard/dashboard.component';
import { TrainingTypeCreateEditComponent } from './create-edit/create-edit.component';

@NgModule({
  imports: [
    TrainingTypeRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ThemeModule,
    CoreModule,
    SharedModule
  ],
  declarations: [
    TrainingTypeDashboardComponent,
    TrainingTypeCreateEditComponent
  ]
})
export class TrainingTypeModule { }
