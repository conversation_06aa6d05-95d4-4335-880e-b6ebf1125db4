import { Component, Injector, OnInit, ViewChild } from '@angular/core';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Paginator } from 'primeng/paginator';
import { finalize, Subscription } from 'rxjs';
import { AreaPositionCreateEditComponent } from '../create-edit/create-edit-component';
import { AreaPositionServiceProxy, AreaPositionDto } from '@proxies/auditory/area-position.proxy'
import { ProcessServiceProxy } from '@proxies/auditory/process.proxy';
import { IFilterOption } from '@core/models/filters';

@Component({
    selector: 'app-document-dashboard',
    templateUrl: 'dashboard.component.html',
    styleUrls: [
        'dashboard.component.scss'
    ]
})

export class AreaPositionDashboardComponent extends ViewComponent implements OnInit {

    private areaPositionServiceProxy: AreaPositionServiceProxy;
    private processServiceProxy: ProcessServiceProxy;

    @ViewChild('dataTable', { static: true }) dataTable: Table;
    @ViewChild('paginator', { static: true }) paginator: Paginator;

    processArray: IFilterOption<number>[];

    filterLoaded: boolean;

    private subscription!: Subscription;

    constructor(_injector: Injector) {
        super(_injector);

        this.areaPositionServiceProxy = _injector.get(AreaPositionServiceProxy);
        this.processServiceProxy = _injector.get(ProcessServiceProxy);
    }

    ngOnInit() {

    }

    createItem(): void {
        this.dialog.showWithData<boolean>({
            component: AreaPositionCreateEditComponent
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    editItem(role: AreaPositionDto): void {
        this.dialog.showWithData<boolean>({
            component: AreaPositionCreateEditComponent,
            componentProps: {
                id: role.id
            }
        }).then((response) => {
            if (response.data.result) {
                this.getData();
            }
        });
    }

    deleteItem(obj: AreaPositionDto) {
        this.message.confirm(`¿Estas seguro de eliminar la posicion "${obj.name}"?`, 'Aviso', async (confirmation) => {
            if (confirmation) {
                const loading = await this.loader.show();
                this.areaPositionServiceProxy
                    .delete(obj.id)
                    .pipe(finalize(async () => await loading.dismiss()))
                    .subscribe({
                        next: () => {
                            this.notify.success('Se ha eliminado la posicion satisfactoriamente', 5000);
                            this.getData();
                        }
                    });
            }
        });
    }

    async getData(event?: TableLazyLoadEvent): Promise<void> {
        if (!this.filterLoaded) {
            await this.loadFilters();
            this.filterLoaded = true;
        }

        if (this.table.shouldResetPaging(event)) {
            this.paginator.changePage(0);

            if (this.table.records && this.table.records.length > 0) {
                return;
            }
        }

        const loading = await this.loader.show();

        this.subscription?.unsubscribe();
        this.subscription = this.areaPositionServiceProxy
            .getAll(
                this.dataTable?.filters?.['AreaPositionName']?.['value'],
                this.dataTable?.filters?.['AreaPositionProcessId']?.['value'],
                this.table.getSorting(this.dataTable),
                this.table.getMaxResultCount(this.paginator, event),
                this.table.getSkipCount(this.paginator, event))
            .pipe(finalize(async () => await loading.dismiss()))
            .subscribe({
                next: (result) => {
                    this.table.totalRecordsCount = result.totalCount;
                    this.table.records = result.items;
                }
            });
    }

    showActions(event: any, role: AreaPositionDto) {
        this.popover.show(event, [
            {
                label: 'Editar',
                permissions: [
                    'Pages.Auditory.Models.Modify'
                ],
                callback: () => this.editItem(role)
            },
            {
                label: 'Eliminar',
                permissions: [
                    'Pages.Auditory.Models.Delete'
                ],
                callback: () => this.deleteItem(role)
            }
        ]);
    }

    resetFilters() {
        this.dataTable?.clear();
    }

    private async loadFilters(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.processServiceProxy
                .getAllFilter()
                .subscribe({
                    next: (response) => {
                        this.processArray = response.items.map(p => {
                            return {
                                label: p.name,
                                value: p.id
                            };
                        });
                        resolve();
                    }, error: () => reject()
                });
        });
    }
}
