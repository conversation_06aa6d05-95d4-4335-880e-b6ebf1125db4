<app-modal [title]="loaded ? 'Visualizar Capacitación' : 'Cargando...'" [disabled]="false" size="extra">
    <app-modal-body>
        <div *ngIf="loaded">
            <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true">
                
                <!-- Tab 1: Archivos y Videos -->
                <p-tabPanel header="Archivos y Videos" *ngIf="!attended && watchedVideo">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-file-video me-2"></i>
                                        Recursos de la Capacitación
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div *ngIf="hasResources(); else noResources">

                                        <!-- Videos Principales -->
                                        <div *ngIf="getVideoResources().length > 0" class="mb-4">
                                            <h6 class="fw-bold text-primary mb-3">
                                                <i class="fas fa-video me-2"></i>
                                                Videos de la Capacitación
                                            </h6>
                                            <div class="row">
                                                <div class="col-12 mb-3" *ngFor="let video of getVideoResources()">
                                                    <div class="video-wrapper"
                                                         appVideoTracker
                                                         [videoData]="video"
                                                         (onVideoStarted)="onVideoStarted(video)"
                                                         (onVideoEnded)="onVideoEnded(video)">
                                                        <app-resource-preview
                                                            [resource]="video.path"
                                                            [type]="video.type"
                                                            [icon]="video.icon"
                                                            [name]="video.name"
                                                            [info]="video.size"
                                                            [removed]="video.remove"
                                                            [showRemove]="false">
                                                        </app-resource-preview>
 
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Otros Adjuntos -->
                                        <div *ngIf="getOtherResources().length > 0">
                                            <h6 class="fw-bold text-secondary mb-3">
                                                <i class="fas fa-paperclip me-2"></i>
                                                Otros Adjuntos
                                            </h6>
                                            <div class="row">
                                                <div class="col-12 mb-3" *ngFor="let resource of getOtherResources()">
                                                    <app-resource-preview
                                                        [resource]="resource.path"
                                                        [type]="resource.type"
                                                        [icon]="resource.icon"
                                                        [name]="resource.name"
                                                        [info]="resource.size"
                                                        [removed]="resource.remove"
                                                        [showRemove]="false">
                                                    </app-resource-preview>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    
                                    <ng-template #noResources>
                                        <div class="text-center py-5">
                                            <ion-icon name="folder-open-outline" size="large" color="medium"></ion-icon>
                                            <p class="mt-3 mb-0 text-muted">No hay archivos disponibles para esta capacitación</p>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>
 
                <!-- Tab 2: Evaluación -->
                <p-tabPanel header="Evaluación"  *ngIf="!attended">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-clipboard-check me-2"></i>
                                        <h5 class="fw-bold">{{evaluation.title}}</h5>
                                    </h6>
                                     <p class="text-muted" *ngIf="evaluation.description">{{evaluation.description}}</p>
                                </div>
                                <div class="card-body">

                                    <!-- Mensaje de evaluación ya completada -->
                                    <div *ngIf="isEvaluationCompleted" class="alert alert-success text-center mb-4">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <ion-icon name="checkmark-circle" size="large" class="me-3" color="success"></ion-icon>
                                            <div>
                                                <h5 class="mb-1 text-success">¡Evaluación Completada!</h5>
                                                <p class="mb-0">Ya has completado exitosamente esta evaluación.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Contenido de la evaluación (solo si no está completada) -->
                                    <div *ngIf="!isEvaluationCompleted">
                                        <div *ngIf="evaluation && !evaluationLoading; else loadingEvaluation">

                                      
                                        <!-- Preguntas -->
                                        <div class="questions-container">
                                            <div class="question-item mb-4" *ngFor="let question of evaluation.questions; let i = index">
                                                <div class="question-header mb-3">
                                                    <h6 class="fw-bold">
                                                        Pregunta {{i + 1}}
                                                    </h6>
                                                    <p class="question-text">{{question.questionText}}</p>
                                                </div>

                                                <!-- Opciones de respuesta -->
                                                <div class="options-container">
                                                    <div class="form-check mb-2">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            [name]="'question_' + question.id"
                                                            [id]="'question_' + question.id + '_a'"
                                                            value="A"
                                                            [(ngModel)]="question.selectedAnswer">
                                                        <label class="form-check-label" [for]="'question_' + question.id + '_a'">
                                                            A) {{question.optionA}}
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            [name]="'question_' + question.id"
                                                            [id]="'question_' + question.id + '_b'"
                                                            value="B"
                                                            [(ngModel)]="question.selectedAnswer">
                                                        <label class="form-check-label" [for]="'question_' + question.id + '_b'">
                                                            B) {{question.optionB}}
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            [name]="'question_' + question.id"
                                                            [id]="'question_' + question.id + '_c'"
                                                            value="C"
                                                            [(ngModel)]="question.selectedAnswer">
                                                        <label class="form-check-label" [for]="'question_' + question.id + '_c'">
                                                            C) {{question.optionC}}
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input
                                                            class="form-check-input"
                                                            type="radio"
                                                            [name]="'question_' + question.id"
                                                            [id]="'question_' + question.id + '_d'"
                                                            value="D"
                                                            [(ngModel)]="question.selectedAnswer">
                                                        <label class="form-check-label" [for]="'question_' + question.id + '_d'">
                                                            D) {{question.optionD}}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Botón de enviar -->
                                        <div class="text-center mt-4">
                                            <ion-button
                                                (click)="submitEvaluation()"
                                                color="primary"
                                                [disabled]="!isEvaluationComplete()">
                                                <ion-icon name="send-outline" slot="start"></ion-icon>
                                                Enviar Evaluación
                                            </ion-button>
                                        </div>

                                        </div>
                                    </div>

                                    <ng-template #loadingEvaluation>
                                        <div class="text-center py-5">
                                            <ion-spinner name="crescent"></ion-spinner>
                                            <p class="mt-3 text-muted">Cargando evaluación...</p>
                                        </div>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>

                <!-- Tab 3: Satisfacción -->
                <p-tabPanel header="Satisfacción">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-star me-2"></i>
                                        Calificación de Satisfacción
                                    </h6>
                                </div>
                                <div class="card-body">

                                    <!-- Calificación por estrellas -->
                                    <div class="satisfaction-rating mb-4">
                                        <h6 class="fw-bold mb-3">¿Cómo calificarías esta capacitación?</h6>
                                        <div class="stars-container text-center mb-3">
                                            <div class="stars-wrapper">
                                                <ion-icon
                                                    *ngFor="let star of [1,2,3,4 ]; let i = index"
                                                    [name]="i < satisfactionRating ? 'star' : 'star-outline'"
                                                    [class]="i < satisfactionRating ? 'star-filled' : 'star-empty'"
                                                    (click)="setSatisfactionRating(i + 1)"
                                                    size="large">
                                                </ion-icon>
                                            </div>
                                            <p class="mt-2 text-muted">
                                                <span *ngIf="satisfactionRating === 0">Selecciona una calificación</span>
                                                <span *ngIf="satisfactionRating === 1">Muy insatisfecho</span>
                                                <span *ngIf="satisfactionRating === 2">Insatisfecho</span>
                                                <span *ngIf="satisfactionRating === 3">Neutral</span>
                                                <span *ngIf="satisfactionRating === 4">Satisfecho</span>
                                                <span *ngIf="satisfactionRating === 5">Muy satisfecho</span>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Comentarios -->
                                    <div class="satisfaction-comments mb-4">
                                        <h6 class="fw-bold mb-3">Comentarios adicionales (opcional)</h6>
                                        <div class="form-group">
                                            <textarea
                                                class="form-control"
                                                rows="4"
                                                placeholder="Comparte tu experiencia, sugerencias o comentarios sobre esta capacitación..."
                                                [(ngModel)]="satisfactionComments"
                                                maxlength="500">
                                            </textarea>
                                            <small class="form-text text-muted">
                                                {{satisfactionComments?.length || 0}}/500 caracteres
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Botón de guardar -->
                                    <div class="text-center">
                                        <ion-button
                                            (click)="saveSatisfaction()"
                                            color="primary"
                                            [disabled]="satisfactionRating === 0 || satisfactionSaving">
                                            <ion-icon name="save-outline" slot="start"></ion-icon>
                                            {{satisfactionSaving ? 'Guardando...' : 'Guardar Calificación'}}
                                        </ion-button>
                                    </div>

                                    <!-- Mensaje de éxito -->
                                    <div *ngIf="satisfactionSaved" class="alert alert-success text-center mt-3">
                                        <ion-icon name="checkmark-circle" class="me-2"></ion-icon>
                                        ¡Gracias por tu calificación! Tu opinión es muy importante para nosotros.
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </p-tabPanel>

            </p-tabView>
        </div>
        
        <div *ngIf="!loaded" class="text-center py-5">
            <ion-spinner name="crescent" color="primary"></ion-spinner>
            <p class="mt-3 text-muted">Cargando información de la capacitación...</p>
        </div>
    </app-modal-body>
    
    <app-modal-footer>
        <ion-button (click)="close()" fill="outline" color="medium">
            <ion-icon name="close-outline" slot="start"></ion-icon>
            Cerrar
        </ion-button>
    </app-modal-footer>
</app-modal>
