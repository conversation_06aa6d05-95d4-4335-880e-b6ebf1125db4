<div class="row">

    <div class="col-12 mb-3">
        <label class="fz-normal fw-bold text-dark mb-0">
            Información de historial
        </label>
        <hr>
    </div>

    <div class="col-12">
        <p-table #dataTable sortMode="multiple" [value]="meet?.meetOperations"
            [rows]="table.defaultRecordsCountPerPage" [paginator]="false" [lazy]="true" [scrollable]="true"
            ScrollWidth="100%" scrollDirection="horizontal" [resizableColumns]="table.resizableColumns">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        Operación
                    </th>
                    <th style="min-width: 150px; max-width: 150px; width: 150px">
                        Creado el
                    </th>
                    <th style="min-width: 200px">
                        Creado por
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-record="$implicit" let-rowIndex="rowIndex">
                <tr [hidden]="record.isHidden">
                    <td style="min-width: 150px; max-width: 150px; width: 150px">
                        <ion-badge color="primary" *ngIf="record.type == meetOperationTypes.created">
                            Creación
                        </ion-badge>
                        <ion-badge color="success" *ngIf="record.type == meetOperationTypes.completed">
                            Ejecutar
                        </ion-badge>
                        <ion-badge color="danger" *ngIf="record.type == meetOperationTypes.canceled">
                            Cancelar
                        </ion-badge>
                    </td>
                    <td class="action-column" style="min-width: 150px; max-width: 150px; width: 150px">
                        {{record?.creationTime | luxonFormat: 'dd/MM/yyyy HH:mm:ss'}}
                    </td>
                    <td style="min-width: 200px">
                        {{record?.creationUser?.name}} {{record?.creationUser?.surname}} {{record?.creationUser?.secondSurname}}
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="primeng-no-data" *ngIf="table.totalRecordsCount == 0">
            No hay data
        </div>
        <div class="primeng-paging-container">
            <p-paginator [rows]="table.defaultRecordsCountPerPage" #paginator (onPageChange)="getData($event)"
                [totalRecords]="table.totalRecordsCount" [rowsPerPageOptions]="table.predefinedRecordsCountPerPage"
                [showCurrentPageReport]="true" dropdownAppendTo="body"
                currentPageReportTemplate="Registros: {{table.totalRecordsCount}}">
            </p-paginator>
        </div>
    </div>

</div>