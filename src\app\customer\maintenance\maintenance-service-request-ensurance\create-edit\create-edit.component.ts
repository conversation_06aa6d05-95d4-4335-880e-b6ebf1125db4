import { Component, Injector, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ViewComponent } from '@core/inheritance/app-component.base';
import { isNullEmptyOrWhiteSpace, isValidNumber } from '@core/utils/tools';
import { MaintenanceServiceRequestEnsuranceDto, MaintenanceServiceRequestEnsuranceServiceProxy } from '@proxies/maintenance-service-request-ensurance.proxy';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-maintenance-service-request-ensurance-create-edit',
    templateUrl: 'create-edit.component.html',
    styleUrls: [
        'create-edit.component.scss'
    ]
})
export class MaintenanceServiceRequestEnsuranceCreateEditComponent extends ViewComponent implements OnInit {

    private maintenanceServiceRequestEnsuranceServiceProxy: MaintenanceServiceRequestEnsuranceServiceProxy;
    private formBuilder: FormBuilder;

    @Input() id: number;

    item: MaintenanceServiceRequestEnsuranceDto = new MaintenanceServiceRequestEnsuranceDto();

    disabled: boolean = false;

    modalForm!: FormGroup;

    get maintenanceServiceRequestEnsuranceName(): AbstractControl {
        return this.modalForm.controls['maintenanceServiceRequestEnsuranceNameInput'];
    };

    get maintenanceServiceRequestEnsuranceDescriptionRequired(): AbstractControl {
        return this.modalForm.controls['maintenanceServiceRequestEnsuranceDescriptionRequiredSelect'];
    };

    get maintenanceServiceRequestEnsuranceEnabled(): AbstractControl {
        return this.modalForm.controls['maintenanceServiceRequestEnsuranceEnabledSelect'];
    };

    get maintenanceServiceRequestEnsuranceCode(): AbstractControl {
        return this.modalForm.controls['maintenanceServiceRequestEnsuranceCodeInput'];
    };

    get maintenanceServiceRequestEnsuranceIndex(): AbstractControl {
        return this.modalForm.controls['maintenanceServiceRequestEnsuranceIndexInput'];
    };

    constructor(_injector: Injector) {
        super(_injector);

        this.maintenanceServiceRequestEnsuranceServiceProxy = _injector.get(MaintenanceServiceRequestEnsuranceServiceProxy);
        this.formBuilder = _injector.get(FormBuilder);

        this.modalForm = this.formBuilder.group({
            maintenanceServiceRequestEnsuranceNameInput: ['', Validators.compose([Validators.required, Validators.maxLength(255)])],
            maintenanceServiceRequestEnsuranceEnabledSelect: ['false', [Validators.required]],
            maintenanceServiceRequestEnsuranceDescriptionRequiredSelect: ['false', [Validators.required]],
            maintenanceServiceRequestEnsuranceIndexInput: ['', [Validators.required]],
            maintenanceServiceRequestEnsuranceCodeInput: ['', Validators.compose([Validators.required, Validators.maxLength(32)])],
        });
    }

    async ngOnInit(): Promise<void> {
        if (this.id) {
            const loading = await this.loader.show();

            this.maintenanceServiceRequestEnsuranceServiceProxy
                .get(this.id)
                .pipe(finalize(async () => await loading.dismiss()))
                .subscribe({
                    next: (response) => {
                        this.item = response;

                        this.maintenanceServiceRequestEnsuranceName.setValue(this.item.name);
                        this.maintenanceServiceRequestEnsuranceDescriptionRequired.setValue(this.item.descriptionRequired ? 'true' : 'false');
                        this.maintenanceServiceRequestEnsuranceEnabled.setValue(this.item.enabled ? 'true' : 'false');
                        this.maintenanceServiceRequestEnsuranceIndex.setValue(this.item.index);
                        this.maintenanceServiceRequestEnsuranceCode.setValue(this.item.code);
                    },
                    error: async () => await this.dialog.dismiss()
                });
        } else {
            this.item = new MaintenanceServiceRequestEnsuranceDto().fromJS({});
        }
    }

    async save(): Promise<void> {

        this.item.name = this.maintenanceServiceRequestEnsuranceName.value;
        this.item.descriptionRequired = this.maintenanceServiceRequestEnsuranceDescriptionRequired.value == 'true';
        this.item.enabled = this.maintenanceServiceRequestEnsuranceEnabled.value == 'true';
        this.item.code = this.maintenanceServiceRequestEnsuranceCode.value;

        if (isNullEmptyOrWhiteSpace(this.item.name)) {
            this.message.info('El nombre del tipo de capacitación es obligatorio', 'Aviso');
            return;
        }

        const indexString: string = this.maintenanceServiceRequestEnsuranceIndex.value;
        let indexValue: number = +indexString;

        if (!isValidNumber(indexString) || isNaN(indexValue)) {
            this.message.info('El valor del index es obligatorio', 'Aviso');
            return;
        }

        this.item.index = indexValue;

        if (this.disabled)
            return;

        this.disabled = true;
        const loading = await this.loader.show();

        if (this.id) {
            this.maintenanceServiceRequestEnsuranceServiceProxy
                .update(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de capacitación actualizado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        } else {
            this.maintenanceServiceRequestEnsuranceServiceProxy
                .create(this.item)
                .pipe(finalize(async () => {
                    this.disabled = false;
                    await loading.dismiss();
                })).subscribe({
                    next: () => {
                        this.notify.success('Tipo de capacitación creado exitosamente', 5000);
                        this.dialog.dismiss(true);
                    }
                });
        }
    }
}